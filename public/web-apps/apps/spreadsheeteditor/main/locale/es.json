{"cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.Controllers.Chat.notcriticalErrorTitle": "Aviso", "Common.Controllers.Chat.textEnterMessage": "Introduzca su mensaje aquí", "Common.Controllers.History.notcriticalErrorTitle": "Advertencia", "Common.define.chartData.textArea": "Á<PERSON>", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON> a<PERSON>a", "Common.define.chartData.textAreaStackedPer": "Área apilada 100% ", "Common.define.chartData.textBar": "Barr<PERSON>", "Common.define.chartData.textBarNormal": "<PERSON>umna a<PERSON>", "Common.define.chartData.textBarNormal3d": "Columna 3D agrupada", "Common.define.chartData.textBarNormal3dPerspective": "Columna 3D", "Common.define.chartData.textBarStacked": "<PERSON><PERSON><PERSON> apilada", "Common.define.chartData.textBarStacked3d": "Columna 3D apilada", "Common.define.chartData.textBarStackedPer": "Columna apilada 100%", "Common.define.chartData.textBarStackedPer3d": "Columna 3D apilada 100%", "Common.define.chartData.textCharts": "Grá<PERSON><PERSON>", "Common.define.chartData.textColumn": "Gráfico de columnas", "Common.define.chartData.textColumnSpark": "Histograma", "Common.define.chartData.textCombo": "Combinado", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON> api<PERSON>a - Columna a<PERSON>", "Common.define.chartData.textComboBarLine": "<PERSON><PERSON>na a<PERSON> <PERSON><PERSON><PERSON>", "Common.define.chartData.textComboBarLineSecondary": "Columna agrupada - Línea en eje secundario", "Common.define.chartData.textComboCustom": "Combinación personalizada", "Common.define.chartData.textDoughnut": "<PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Barra agrupada", "Common.define.chartData.textHBarNormal3d": "Barra 3D agrupada", "Common.define.chartData.textHBarStacked": "Barra apilada", "Common.define.chartData.textHBarStacked3d": "Barra 3D apilada", "Common.define.chartData.textHBarStackedPer": "Barra apilada 100%", "Common.define.chartData.textHBarStackedPer3d": "Barra 3D apilada 100%", "Common.define.chartData.textLine": "Lín<PERSON>", "Common.define.chartData.textLine3d": "Línea 3D", "Common.define.chartData.textLineMarker": "Línea con marcadores", "Common.define.chartData.textLineSpark": "Lín<PERSON>", "Common.define.chartData.textLineStacked": "<PERSON><PERSON><PERSON> apilada", "Common.define.chartData.textLineStackedMarker": "Línea apilada con marcadores", "Common.define.chartData.textLineStackedPer": "Línea apilada 100%", "Common.define.chartData.textLineStackedPerMarker": "Línea apilada con marcadores 100%", "Common.define.chartData.textPie": "Gráfico circular", "Common.define.chartData.textPie3d": "Circular 3D", "Common.define.chartData.textPoint": "XY (Dispersión)", "Common.define.chartData.textScatter": "Dispersión", "Common.define.chartData.textScatterLine": "Dispersión con líneas rectas", "Common.define.chartData.textScatterLineMarker": "Dispersión con líneas rectas y marcadores", "Common.define.chartData.textScatterSmooth": "Dispersión con líneas suavizadas", "Common.define.chartData.textScatterSmoothMarker": "Dispersión con líneas suavizadas y marcadores", "Common.define.chartData.textSparks": "Sparklines", "Common.define.chartData.textStock": "De cotizaciones", "Common.define.chartData.textSurface": "Superficie", "Common.define.chartData.textWinLossSpark": "Ganancia/pérdida", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Sin formato establecido", "Common.define.conditionalData.text1Above": "1 por encima de des. est.", "Common.define.conditionalData.text1Below": "1 por debajo de des. est.", "Common.define.conditionalData.text2Above": "2 por encima de des. est.", "Common.define.conditionalData.text2Below": "2 por debajo de des. est.", "Common.define.conditionalData.text3Above": "3 por encima de des. est.", "Common.define.conditionalData.text3Below": "3 por debajo de des. est.", "Common.define.conditionalData.textAbove": "Encima", "Common.define.conditionalData.textAverage": "Promedio", "Common.define.conditionalData.textBegins": "Empieza con", "Common.define.conditionalData.textBelow": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textBetween": "<PERSON><PERSON>", "Common.define.conditionalData.textBlank": "En blanco", "Common.define.conditionalData.textBlanks": "<PERSON><PERSON><PERSON> celdas en blanco", "Common.define.conditionalData.textBottom": "Inferior", "Common.define.conditionalData.textContains": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDataBar": "Barra de datos", "Common.define.conditionalData.textDate": "<PERSON><PERSON>", "Common.define.conditionalData.textDuplicate": "Duplicar", "Common.define.conditionalData.textEnds": "Termina con", "Common.define.conditionalData.textEqAbove": "Igual o superior a", "Common.define.conditionalData.textEqBelow": "Igual o menor que", "Common.define.conditionalData.textEqual": "Igual que", "Common.define.conditionalData.textError": "Error", "Common.define.conditionalData.textErrors": "<PERSON><PERSON><PERSON> errores", "Common.define.conditionalData.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textGreater": "Mayor que", "Common.define.conditionalData.textGreaterEq": "Mayor o igual a", "Common.define.conditionalData.textIconSets": "Conjuntos de iconos", "Common.define.conditionalData.textLast7days": "En los últimos 7 días", "Common.define.conditionalData.textLastMonth": "<PERSON><PERSON> pasado", "Common.define.conditionalData.textLastWeek": "Semana pasada", "Common.define.conditionalData.textLess": "<PERSON><PERSON> que", "Common.define.conditionalData.textLessEq": "<PERSON>or o igual a", "Common.define.conditionalData.textNextMonth": "<PERSON><PERSON> si<PERSON>", "Common.define.conditionalData.textNextWeek": "<PERSON><PERSON> sigu<PERSON>e", "Common.define.conditionalData.textNotBetween": "No está entre", "Common.define.conditionalData.textNotBlanks": "No contiene celdas en blanco", "Common.define.conditionalData.textNotContains": "No contiene", "Common.define.conditionalData.textNotEqual": "No igual a", "Common.define.conditionalData.textNotErrors": "No contiene errores", "Common.define.conditionalData.textText": "Texto", "Common.define.conditionalData.textThisMonth": "<PERSON>ste mes", "Common.define.conditionalData.textThisWeek": "<PERSON><PERSON> semana", "Common.define.conditionalData.textToday": "Hoy", "Common.define.conditionalData.textTomorrow": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textTop": "Superior", "Common.define.conditionalData.textUnique": "Único", "Common.define.conditionalData.textValue": "El valor es", "Common.define.conditionalData.textYesterday": "Ayer", "Common.define.smartArt.textAccentedPicture": "Imagen destacada", "Common.define.smartArt.textAccentProcess": "Proceso destacado", "Common.define.smartArt.textAlternatingFlow": "Flujo alternativo", "Common.define.smartArt.textAlternatingHexagons": "Hexágonos alternativos", "Common.define.smartArt.textAlternatingPictureBlocks": "Bloques de imágenes alternativos", "Common.define.smartArt.textAlternatingPictureCircles": "Círculos con imágenes alternativos", "Common.define.smartArt.textArchitectureLayout": "Diseño de arquitectura", "Common.define.smartArt.textArrowRibbon": "<PERSON>inta de flechas", "Common.define.smartArt.textAscendingPictureAccentProcess": "Proceso de imágenes destacadas ascendente", "Common.define.smartArt.textBalance": "<PERSON><PERSON>", "Common.define.smartArt.textBasicBendingProcess": "Proceso curvo básico", "Common.define.smartArt.textBasicBlockList": "Lista de bloques básica", "Common.define.smartArt.textBasicChevronProcess": "Proceso cheurón básico", "Common.define.smartArt.textBasicCycle": "Ciclo básico", "Common.define.smartArt.textBasicMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textBasicPie": "Circular básico", "Common.define.smartArt.textBasicProcess": "Proceso básico", "Common.define.smartArt.textBasicPyramid": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "Common.define.smartArt.textBasicRadial": "Radial básico", "Common.define.smartArt.textBasicTarget": "Objetivo básico", "Common.define.smartArt.textBasicTimeline": "Escala de tiempo básica", "Common.define.smartArt.textBasicVenn": "<PERSON><PERSON>n b<PERSON><PERSON>", "Common.define.smartArt.textBendingPictureAccentList": "Lista destacada con círculos abajo", "Common.define.smartArt.textBendingPictureBlocks": "Bloques de imágenes con cuadro", "Common.define.smartArt.textBendingPictureCaption": "Imágenes con títulos", "Common.define.smartArt.textBendingPictureCaptionList": "Lista de títulos de imágenes", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Imágenes con texto semitransparente", "Common.define.smartArt.textBlockCycle": "Ciclo de bloques", "Common.define.smartArt.textBubblePictureList": "Lista de imágenes con burbujas", "Common.define.smartArt.textCaptionedPictures": "Imágenes con títulos", "Common.define.smartArt.textChevronAccentProcess": "Proceso cheurón destacado", "Common.define.smartArt.textChevronList": "Lista de cheurones", "Common.define.smartArt.textCircleAccentTimeline": "Línea de tiempo con círculos", "Common.define.smartArt.textCircleArrowProcess": "Proceso de círculos con flecha", "Common.define.smartArt.textCirclePictureHierarchy": "Jerarquía con imágenes en círculos", "Common.define.smartArt.textCircleProcess": "Proceso de círculos", "Common.define.smartArt.textCircleRelationship": "Relación de círculo", "Common.define.smartArt.textCircularBendingProcess": "Proceso curvo circular", "Common.define.smartArt.textCircularPictureCallout": "Globo de imagen circular", "Common.define.smartArt.textClosedChevronProcess": "Proceso de cheurón cerrado", "Common.define.smartArt.textContinuousArrowProcess": "Proceso de flechas continuo", "Common.define.smartArt.textContinuousBlockProcess": "Proceso de bloque continuo", "Common.define.smartArt.textContinuousCycle": "<PERSON><PERSON><PERSON> continuo", "Common.define.smartArt.textContinuousPictureList": "Lista de imágenes continua", "Common.define.smartArt.textConvergingArrows": "<PERSON>le<PERSON><PERSON> convergentes", "Common.define.smartArt.textConvergingRadial": "Radial convergente", "Common.define.smartArt.textConvergingText": "Texto convergente", "Common.define.smartArt.textCounterbalanceArrows": "Flechas de contrapeso", "Common.define.smartArt.textCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textCycleMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textDescendingBlockList": "Lista de bloques descendente", "Common.define.smartArt.textDescendingProcess": "Proceso descendente", "Common.define.smartArt.textDetailedProcess": "Proceso detallado", "Common.define.smartArt.textDivergingArrows": "Flechas divergentes", "Common.define.smartArt.textDivergingRadial": "Radial divergente", "Common.define.smartArt.textEquation": "Ecuación", "Common.define.smartArt.textFramedTextPicture": "Imagen de texto enmarcado", "Common.define.smartArt.textFunnel": "Embudo", "Common.define.smartArt.textGear": "Engranaje", "Common.define.smartArt.textGridMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textGroupedList": "Lista agrupada", "Common.define.smartArt.textHalfCircleOrganizationChart": "Organigrama con semicírculos", "Common.define.smartArt.textHexagonCluster": "Grupo de hexágonos", "Common.define.smartArt.textHexagonRadial": "Radial con hexágon<PERSON>", "Common.define.smartArt.textHierarchy": "Jerar<PERSON><PERSON><PERSON>", "Common.define.smartArt.textHierarchyList": "Lista de jerarquías", "Common.define.smartArt.textHorizontalBulletList": "Lista de viñetas horizontal", "Common.define.smartArt.textHorizontalHierarchy": "Jerarquía horizontal", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Jerarquía etiquetada horizontal", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Jerarquía horizontal de varios niveles", "Common.define.smartArt.textHorizontalOrganizationChart": "Organigrama horizontal", "Common.define.smartArt.textHorizontalPictureList": "Lista horizontal de imágenes", "Common.define.smartArt.textIncreasingArrowProcess": "Proceso de flechas crecientes", "Common.define.smartArt.textIncreasingCircleProcess": "Proceso de círculos crecientes", "Common.define.smartArt.textInterconnectedBlockProcess": "Bloque interconectado", "Common.define.smartArt.textInterconnectedRings": "Anillos interconectados", "Common.define.smartArt.textInvertedPyramid": "Pirámide invertida", "Common.define.smartArt.textLabeledHierarchy": "Jerarquía etiquetada", "Common.define.smartArt.textLinearVenn": "Venn lineal", "Common.define.smartArt.textLinedList": "Lista alineada", "Common.define.smartArt.textList": "Lista", "Common.define.smartArt.textMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "Ciclo multidireccional", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Organigrama con nombres y cargos", "Common.define.smartArt.textNestedTarget": "Objetivo anidado", "Common.define.smartArt.textNondirectionalCycle": "Ciclo sin dirección", "Common.define.smartArt.textOpposingArrows": "Flechas opuestas", "Common.define.smartArt.textOpposingIdeas": "Ideas opuestas", "Common.define.smartArt.textOrganizationChart": "Organigrama", "Common.define.smartArt.textOther": "<PERSON><PERSON>", "Common.define.smartArt.textPhasedProcess": "Proceso en fases", "Common.define.smartArt.textPicture": "Imagen", "Common.define.smartArt.textPictureAccentBlocks": "Imágenes destacadas en bloques", "Common.define.smartArt.textPictureAccentList": "Lista de imágenes destacadas", "Common.define.smartArt.textPictureAccentProcess": "Proceso de imágenes destacadas", "Common.define.smartArt.textPictureCaptionList": "Lista de títulos de imágenes", "Common.define.smartArt.textPictureFrame": "MarcoDeFotos", "Common.define.smartArt.textPictureGrid": "Imágenes en cuadrícula", "Common.define.smartArt.textPictureLineup": "Imágenes en paralelo", "Common.define.smartArt.textPictureOrganizationChart": "Organigrama con imágenes", "Common.define.smartArt.textPictureStrips": "Imágenes en columna", "Common.define.smartArt.textPieProcess": "Proceso circular", "Common.define.smartArt.textPlusAndMinus": "Más y menos", "Common.define.smartArt.textProcess": "Proceso", "Common.define.smartArt.textProcessArrows": "Flechas de proceso", "Common.define.smartArt.textProcessList": "Lista de procesos", "Common.define.smartArt.textPyramid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPyramidList": "Lista en pirámide", "Common.define.smartArt.textRadialCluster": "Diseño radial", "Common.define.smartArt.textRadialCycle": "Ciclo radial", "Common.define.smartArt.textRadialList": "Lista radial", "Common.define.smartArt.textRadialPictureList": "Lista radial con imágenes", "Common.define.smartArt.textRadialVenn": "Venn radial", "Common.define.smartArt.textRandomToResultProcess": "Proceso de azar a resultado", "Common.define.smartArt.textRelationship": "Relación", "Common.define.smartArt.textRepeatingBendingProcess": "Proceso curvo repetitivo", "Common.define.smartArt.textReverseList": "Lista inversa", "Common.define.smartArt.textSegmentedCycle": "Ciclo segmentado", "Common.define.smartArt.textSegmentedProcess": "Proceso segmentado", "Common.define.smartArt.textSegmentedPyramid": "Pirámide segmentada", "Common.define.smartArt.textSnapshotPictureList": "Lista de imágenes instantáneas", "Common.define.smartArt.textSpiralPicture": "Imagen en espiral", "Common.define.smartArt.textSquareAccentList": "Lista de imágenes con cuadrados", "Common.define.smartArt.textStackedList": "Lista apilada", "Common.define.smartArt.textStackedVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStaggeredProcess": "Proceso escalonado", "Common.define.smartArt.textStepDownProcess": "Proceso de nivel inferior", "Common.define.smartArt.textStepUpProcess": "Proceso de nivel superior", "Common.define.smartArt.textSubStepProcess": "Proceso de pasos secundarios", "Common.define.smartArt.textTabbedArc": "Arco con pestañas", "Common.define.smartArt.textTableHierarchy": "Jerarquía de tabla", "Common.define.smartArt.textTableList": "Lista de tablas", "Common.define.smartArt.textTabList": "Lista de pestañas", "Common.define.smartArt.textTargetList": "Lista de objetivo", "Common.define.smartArt.textTextCycle": "Ciclo de texto", "Common.define.smartArt.textThemePictureAccent": "Imágenes temáticas destacadas", "Common.define.smartArt.textThemePictureAlternatingAccent": "Imágenes temáticas destacadas alternativas", "Common.define.smartArt.textThemePictureGrid": "Imágenes temáticas en cuadrícula", "Common.define.smartArt.textTitledMatrix": "<PERSON><PERSON> con tí<PERSON>", "Common.define.smartArt.textTitledPictureAccentList": "Lista de imágenes destacadas con título", "Common.define.smartArt.textTitledPictureBlocks": "Bloques de imágenes con títulos", "Common.define.smartArt.textTitlePictureLineup": "Serie de imágenes con título", "Common.define.smartArt.textTrapezoidList": "Lista de trapezoides", "Common.define.smartArt.textUpwardArrow": "Flecha arriba", "Common.define.smartArt.textVaryingWidthList": "Lista de ancho variable", "Common.define.smartArt.textVerticalAccentList": "Lista con rectángulos en vertical", "Common.define.smartArt.textVerticalArrowList": "Lista vertical de flechas", "Common.define.smartArt.textVerticalBendingProcess": "Proceso curvo vertical", "Common.define.smartArt.textVerticalBlockList": "Lista de bloques verticales", "Common.define.smartArt.textVerticalBoxList": "Lista vertical de cuadros", "Common.define.smartArt.textVerticalBracketList": "Lista vertical con corchetes", "Common.define.smartArt.textVerticalBulletList": "Lista vertical de viñetas", "Common.define.smartArt.textVerticalChevronList": "Lista vertical de cheurones", "Common.define.smartArt.textVerticalCircleList": "Lista con círculos en vertical", "Common.define.smartArt.textVerticalCurvedList": "Lista curvada vertical", "Common.define.smartArt.textVerticalEquation": "Ecuación vertical", "Common.define.smartArt.textVerticalPictureAccentList": "Lista con círculos a la izquierda", "Common.define.smartArt.textVerticalPictureList": "Lista vertical de imágenes", "Common.define.smartArt.textVerticalProcess": "Proceso vertical", "Common.Translation.textMoreButton": "Más", "Common.Translation.tipFileLocked": "El documento está bloqueado para su edición. Puede hacer cambios y guardarlo como copia local más tarde.", "Common.Translation.tipFileReadOnly": "El archivo es de solo lectura. Para no perder los cambios, guarde el archivo con otro nombre o en otra ubicación.", "Common.Translation.warnFileLocked": "El archivo está siendo editado en otra aplicación. Puede continuar editándolo y guardarlo como una copia.", "Common.Translation.warnFileLockedBtnEdit": "Crear copia", "Common.Translation.warnFileLockedBtnView": "Abrir para visualizar", "Common.UI.ButtonColored.textAutoColor": "Automático", "Common.UI.ButtonColored.textNewColor": "Color personalizado", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON> bordes", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON> bordes", "Common.UI.ComboDataView.emptyComboText": "Sin estilo", "Common.UI.ExtendedColorDialog.addButtonText": "Agregar", "Common.UI.ExtendedColorDialog.textCurrent": "Actual", "Common.UI.ExtendedColorDialog.textHexErr": "El valor introducido es incorrecto.<br>Por favor, introduzca un valor de 000000 a FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Nuevo", "Common.UI.ExtendedColorDialog.textRGBErr": "El valor introducido es incorrecto.<br><PERSON>r favor, introduzca un valor numérico de 0 a 225.", "Common.UI.HSBColorPicker.textNoColor": "Sin Color", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Ocultar la contraseña", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Mostrar la contraseña", "Common.UI.SearchBar.textFind": "Buscar", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipNextResult": "Resul<PERSON><PERSON> si<PERSON>", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Abrir los ajustes avanzados", "Common.UI.SearchBar.tipPreviousResult": "Resultado anterior", "Common.UI.SearchDialog.textHighlight": "Resaltar resultados", "Common.UI.SearchDialog.textMatchCase": "Sensible a mayúsculas y minúsculas", "Common.UI.SearchDialog.textReplaceDef": "Introduzca el texto de sustitución", "Common.UI.SearchDialog.textSearchStart": "Introduzca su texto aquí", "Common.UI.SearchDialog.textTitle": "Buscar y reemplazar", "Common.UI.SearchDialog.textTitle2": "Buscar", "Common.UI.SearchDialog.textWholeWords": "<PERSON><PERSON><PERSON> palabras completas", "Common.UI.SearchDialog.txtBtnHideReplace": "Esconder Sustitución", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "Common.UI.SynchronizeTip.textDontShow": "No volver a mostrar este mensaje", "Common.UI.SynchronizeTip.textSynchronize": "El documento ha sido cambiado por otro usuario.<br/>Por favor haga clic para guardar sus cambios y recargue las actualizaciones.", "Common.UI.ThemeColorPalette.textRecentColors": "Colores recientes", "Common.UI.ThemeColorPalette.textStandartColors": "Colores estándar", "Common.UI.ThemeColorPalette.textThemeColors": "Colores de tema", "Common.UI.Themes.txtThemeClassicLight": "Clásico claro", "Common.UI.Themes.txtThemeContrastDark": "Contraste oscuro", "Common.UI.Themes.txtThemeDark": "Oscuro", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "Igual que el sistema", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmación", "Common.UI.Window.textDontShow": "No volver a mostrar este mensaje", "Common.UI.Window.textError": "Error", "Common.UI.Window.textInformation": "Información", "Common.UI.Window.textWarning": "Aviso", "Common.UI.Window.yesButtonText": "Sí", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Control", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "dirección: ", "Common.Views.About.txtLicensee": "LICENCIATARIO ", "Common.Views.About.txtLicensor": "LICENCIANTE", "Common.Views.About.txtMail": "correo: ", "Common.Views.About.txtPoweredBy": "Desarrollado por", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Versión ", "Common.Views.AutoCorrectDialog.textAdd": "Agregar", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Aplicar mientras trabaja", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autocorrección", "Common.Views.AutoCorrectDialog.textAutoFormat": "Autoformato mientras escribe", "Common.Views.AutoCorrectDialog.textBy": "Por", "Common.Views.AutoCorrectDialog.textDelete": "Eliminar", "Common.Views.AutoCorrectDialog.textHyperlink": "Rutas de red e Internet por hipervínculos", "Common.Views.AutoCorrectDialog.textMathCorrect": "Autocorrección matemática", "Common.Views.AutoCorrectDialog.textNewRowCol": "Incluir nuevas filas y columnas en la tabla", "Common.Views.AutoCorrectDialog.textRecognized": "Funciones reconocidas", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Las siguientes expresiones son expresiones matemáticas reconocidas. No se pondrán en cursiva automáticamente.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "<PERSON><PERSON><PERSON><PERSON><PERSON> escribe", "Common.Views.AutoCorrectDialog.textReplaceType": "Reemplazar texto mientras escribe", "Common.Views.AutoCorrectDialog.textReset": "Restablecer", "Common.Views.AutoCorrectDialog.textResetAll": "Restablecer ajustes predeterminados", "Common.Views.AutoCorrectDialog.textRestore": "Restaurar", "Common.Views.AutoCorrectDialog.textTitle": "Autocorrección", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Las funciones reconocidas deben contener solo letras de la A a la Z, mayúsculas o minúsculas.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Cualquier expresión que haya agregado se eliminará y las eliminadas se restaurarán. ¿Desea continuar?", "Common.Views.AutoCorrectDialog.warnReplace": "La entrada de autocorreción para %1 ya existe. ¿Desea reemplazarla?", "Common.Views.AutoCorrectDialog.warnReset": "Las autocorrecciones que haya agregado se eliminarán y las modificadas recuperarán sus valores originales. ¿Desea continuar?", "Common.Views.AutoCorrectDialog.warnRestore": "La entrada de autocorrección para %1 será restablecida a su valor original. ¿Desea continuar?", "Common.Views.Chat.textSend": "Enviar", "Common.Views.Comments.mniAuthorAsc": "Autor de A a Z", "Common.Views.Comments.mniAuthorDesc": "Autor de Z a A", "Common.Views.Comments.mniDateAsc": "<PERSON>ás antiguo", "Common.Views.Comments.mniDateDesc": "Más reciente", "Common.Views.Comments.mniFilterGroups": "Filtrar por grupo", "Common.Views.Comments.mniPositionAsc": "<PERSON><PERSON>", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON>", "Common.Views.Comments.textAdd": "Agregar", "Common.Views.Comments.textAddComment": "Agregar comentario", "Common.Views.Comments.textAddCommentToDoc": "Agregar comentario al documento", "Common.Views.Comments.textAddReply": "Agregar respuesta", "Common.Views.Comments.textAll": "Todo", "Common.Views.Comments.textAnonym": "Visitante", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON>r comentarios", "Common.Views.Comments.textComments": "Comentarios", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Introduzca su comentario aquí", "Common.Views.Comments.textHintAddComment": "Agregar comentario", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON>r de nuevo", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Resolver", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Ordenar comentarios", "Common.Views.Comments.textViewResolved": "No tiene permiso para volver a abrir el comentario", "Common.Views.Comments.txtEmpty": "Sin comentarios en la hoja.", "Common.Views.CopyWarningDialog.textDontShow": "No volver a mostrar este mensaje", "Common.Views.CopyWarningDialog.textMsg": "Se puede realizar las acciones de copiar, cortar y pegar usando los botones en la barra de herramientas y el menú contextual sólo en esta pestaña del editor.<br><br>Si quiere copiar o pegar algo fuera de esta pestaña, use las combinaciones de teclas siguientes:", "Common.Views.CopyWarningDialog.textTitle": "Acciones de Copiar, Cortar y Pegar", "Common.Views.CopyWarningDialog.textToCopy": "para copiar", "Common.Views.CopyWarningDialog.textToCut": "para cortar", "Common.Views.CopyWarningDialog.textToPaste": "para pegar", "Common.Views.DocumentAccessDialog.textLoading": "Cargando...", "Common.Views.DocumentAccessDialog.textTitle": "Ajustes de uso compartido", "Common.Views.EditNameDialog.textLabel": "Etiqueta:", "Common.Views.EditNameDialog.textLabelError": "La etiqueta no debe estar vacía.", "Common.Views.Header.labelCoUsersDescr": "Usuarios que están editando el archivo:", "Common.Views.Header.textAddFavorite": "Marcar como favorito", "Common.Views.Header.textAdvSettings": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textBack": "Abrir ubicación del archivo", "Common.Views.Header.textCompactView": "Esconder barra de herramientas", "Common.Views.Header.textHideLines": "<PERSON><PERSON><PERSON><PERSON> reglas", "Common.Views.Header.textHideStatusBar": "Combinar las barras de hoja y de estado", "Common.Views.Header.textReadOnly": "Sólo lectura", "Common.Views.Header.textRemoveFavorite": "Eliminar de Favoritos", "Common.Views.Header.textSaveBegin": "Guardando...", "Common.Views.Header.textSaveChanged": "Modificado", "Common.Views.Header.textSaveEnd": "Se guardaron todos los cambios", "Common.Views.Header.textSaveExpander": "Se guardaron todos los cambios", "Common.Views.Header.textShare": "Compartir", "Common.Views.Header.textZoom": "Ampliación", "Common.Views.Header.tipAccessRights": "Gestionar derechos de acceso al documento", "Common.Views.Header.tipDownload": "Descargar archivo", "Common.Views.Header.tipGoEdit": "Editar el archivo actual", "Common.Views.Header.tipPrint": "Imprimir archivo", "Common.Views.Header.tipPrintQuick": "Impresión rápida", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Guardar", "Common.Views.Header.tipSearch": "Búsqueda", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Desacoplar en una ventana independiente", "Common.Views.Header.tipUsers": "Ver usuarios", "Common.Views.Header.tipViewSettings": "<PERSON><PERSON> a<PERSON>", "Common.Views.Header.tipViewUsers": "Ver usuarios y gestionar derechos de acceso a documentos", "Common.Views.Header.txtAccessRights": "Cambiar derechos de acceso", "Common.Views.Header.txtRename": "Cambiar nombre", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON> historial", "Common.Views.History.textHide": "<PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Ocultar cambios detallados", "Common.Views.History.textRestore": "Restaurar", "Common.Views.History.textShow": "Expandir", "Common.Views.History.textShowAll": "Mostrar cambios detallados", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Pegar URL de imagen:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Este campo es obligatorio", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Este campo debe ser una URL en el formato \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "Con viñetas", "Common.Views.ListSettingsDialog.textFromFile": "Desde archivo", "Common.Views.ListSettingsDialog.textFromStorage": "Desde almacenamiento", "Common.Views.ListSettingsDialog.textFromUrl": "Desde URL", "Common.Views.ListSettingsDialog.textNumbering": "Numerado", "Common.Views.ListSettingsDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.tipChange": "Cambiar viñeta", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>ñ<PERSON>", "Common.Views.ListSettingsDialog.txtColor": "Color", "Common.Views.ListSettingsDialog.txtImage": "Imagen", "Common.Views.ListSettingsDialog.txtImport": "Importación", "Common.Views.ListSettingsDialog.txtNewBullet": "Nueva viñeta", "Common.Views.ListSettingsDialog.txtNewImage": "Imagen nueva", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "% de texto", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "Empezar en", "Common.Views.ListSettingsDialog.txtSymbol": "Símbolo", "Common.Views.ListSettingsDialog.txtTitle": "Ajustes de lista", "Common.Views.ListSettingsDialog.txtType": "Tipo", "Common.Views.OpenDialog.closeButtonText": "Cerrar archivo", "Common.Views.OpenDialog.textInvalidRange": "<PERSON><PERSON> de celdas inválido", "Common.Views.OpenDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtAdvanced": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtColon": "Colon", "Common.Views.OpenDialog.txtComma": "Coma", "Common.Views.OpenDialog.txtDelimiter": "Delimitador", "Common.Views.OpenDialog.txtDestData": "<PERSON>ja dónde situar los datos", "Common.Views.OpenDialog.txtEmpty": "Este campo es obligatorio", "Common.Views.OpenDialog.txtEncoding": "Codificación ", "Common.Views.OpenDialog.txtIncorrectPwd": "La contraseña es incorrecta", "Common.Views.OpenDialog.txtOpenFile": "Escribir la contraseña para abrir el archivo", "Common.Views.OpenDialog.txtOther": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtPassword": "Contraseña", "Common.Views.OpenDialog.txtPreview": "Vista previa", "Common.Views.OpenDialog.txtProtected": "Una vez que se ha introducido la contraseña y abierto el archivo, la contraseña actual al archivo se restablecerá", "Common.Views.OpenDialog.txtSemicolon": "Punto y coma", "Common.Views.OpenDialog.txtSpace": "Espacio", "Common.Views.OpenDialog.txtTab": "Pestaña", "Common.Views.OpenDialog.txtTitle": "Elegir opciones de %1", "Common.Views.OpenDialog.txtTitleProtected": "Archivo protegido", "Common.Views.PasswordDialog.txtDescription": "Establezca una contraseña para proteger este documento", "Common.Views.PasswordDialog.txtIncorrectPwd": "La contraseña de confirmación no es idéntica", "Common.Views.PasswordDialog.txtPassword": "Contraseña", "Common.Views.PasswordDialog.txtRepeat": "Repita la contraseña", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON><PERSON> contras<PERSON>", "Common.Views.PasswordDialog.txtWarning": "Precaución: Si pierde u olvida su contraseña, no podrá recuperarla. Guárdalo en un lugar seguro.", "Common.Views.PluginDlg.textLoading": "Cargando", "Common.Views.Plugins.groupCaption": "Extensiones", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textClosePanel": "Cerrar plugin", "Common.Views.Plugins.textLoading": "Cargando", "Common.Views.Plugins.textStart": "Iniciar", "Common.Views.Plugins.textStop": "Detener", "Common.Views.Protection.hintAddPwd": "Encriptar con contraseña", "Common.Views.Protection.hintDelPwd": "Eliminar contraseña", "Common.Views.Protection.hintPwd": "Cambie o elimine la contraseña", "Common.Views.Protection.hintSignature": "Agregar firma digital o línea de firma", "Common.Views.Protection.txtAddPwd": "Agregar contraseña", "Common.Views.Protection.txtChangePwd": "Cambiar contraseña", "Common.Views.Protection.txtDeletePwd": "Eliminar contraseña", "Common.Views.Protection.txtEncrypt": "Encriptar", "Common.Views.Protection.txtInvisibleSignature": "Agregar firma digital", "Common.Views.Protection.txtSignature": "Firma", "Common.Views.Protection.txtSignatureLine": "Agregar línea de firma", "Common.Views.RenameDialog.textName": "Nombre de archivo", "Common.Views.RenameDialog.txtInvalidName": "El nombre de archivo no debe contener los símbolos siguientes:", "Common.Views.ReviewChanges.hintNext": "Al siguiente cambio", "Common.Views.ReviewChanges.hintPrev": "Al cambio anterior", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Co-edición a tiempo real. Todos los cambios se guardan de forma automática.", "Common.Views.ReviewChanges.strStrict": "Estricto", "Common.Views.ReviewChanges.strStrictDesc": "Use el botón \"Guardar\" para sincronizar los cambios que usted y otros realicen.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Aceptar cambio actual", "Common.Views.ReviewChanges.tipCoAuthMode": "Establezca el modo de co-edición", "Common.Views.ReviewChanges.tipCommentRem": "Eliminar comentarios", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Eliminar comentarios actuales", "Common.Views.ReviewChanges.tipCommentResolve": "Resolver comentarios", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolver los comentarios actuales", "Common.Views.ReviewChanges.tipHistory": "Mostrar historial de versiones", "Common.Views.ReviewChanges.tipRejectCurrent": "<PERSON><PERSON><PERSON> Actual", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON><PERSON><PERSON> cambios", "Common.Views.ReviewChanges.tipReviewView": "Seleccionar el modo en el que quiere que se presenten los cambios", "Common.Views.ReviewChanges.tipSetDocLang": "Establecer el idioma de documento", "Common.Views.ReviewChanges.tipSetSpelling": "Сorrección ortográfica", "Common.Views.ReviewChanges.tipSharing": "Gestionar derechos de acceso al documento", "Common.Views.ReviewChanges.txtAccept": "Aceptar", "Common.Views.ReviewChanges.txtAcceptAll": "Aceptar todos los cambios", "Common.Views.ReviewChanges.txtAcceptChanges": "Aceptar cambios", "Common.Views.ReviewChanges.txtAcceptCurrent": "Aceptar cambio actual", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Modo de co-edición", "Common.Views.ReviewChanges.txtCommentRemAll": "Eliminar todos los comentarios", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Eliminar comentarios actuales", "Common.Views.ReviewChanges.txtCommentRemMy": "Eliminar mis comentarios", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Eliminar mis actuales comentarios", "Common.Views.ReviewChanges.txtCommentRemove": "Eliminar", "Common.Views.ReviewChanges.txtCommentResolve": "Resolver", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolver todos los comentarios", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolver comentarios actuales", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolver mis comentarios", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolver mis comentarios actuales", "Common.Views.ReviewChanges.txtDocLang": "Idioma", "Common.Views.ReviewChanges.txtFinal": "Todos los cambio aceptados (vista previa)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Historial de versiones", "Common.Views.ReviewChanges.txtMarkup": "Todos los cambios (Edición)", "Common.Views.ReviewChanges.txtMarkupCap": "Margen", "Common.Views.ReviewChanges.txtNext": "Siguient<PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Todos los cambios rechazados (Vista previa)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Anterior", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON><PERSON> todos los cambios", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "<PERSON><PERSON><PERSON> Actual", "Common.Views.ReviewChanges.txtSharing": "Compartir", "Common.Views.ReviewChanges.txtSpelling": "Сorrección ortográfica", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON><PERSON><PERSON> cambios", "Common.Views.ReviewChanges.txtView": "Modo de visualización", "Common.Views.ReviewPopover.textAdd": "Agregar", "Common.Views.ReviewPopover.textAddReply": "Agregar respuesta", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Introduzca su comentario aquí", "Common.Views.ReviewPopover.textMention": "+mención proporcionará acceso al documento y enviará un correo", "Common.Views.ReviewPopover.textMentionNotify": "+mención notificará al usuario por correo", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON>r de nuevo", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Resolver", "Common.Views.ReviewPopover.textViewResolved": "No tiene permiso para volver a abrir el comentario", "Common.Views.ReviewPopover.txtDeleteTip": "Eliminar", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Cargando", "Common.Views.SaveAsDlg.textTitle": "Carpeta para guardar", "Common.Views.SearchPanel.textByColumns": "Por columnas", "Common.Views.SearchPanel.textByRows": "<PERSON><PERSON> <PERSON>las", "Common.Views.SearchPanel.textCaseSensitive": "<PERSON><PERSON><PERSON><PERSON> <PERSON> de minúsculas", "Common.Views.SearchPanel.textCell": "<PERSON><PERSON>", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textContentChanged": "Se ha cambiado el documento", "Common.Views.SearchPanel.textFind": "Buscar", "Common.Views.SearchPanel.textFindAndReplace": "Buscar y reemplazar", "Common.Views.SearchPanel.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFormulas": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "Common.Views.SearchPanel.textItemEntireCell": "Todo el contenido de celda", "Common.Views.SearchPanel.textLookIn": "Buscar en ", "Common.Views.SearchPanel.textMatchUsingRegExp": "Coincidir utilizando expresiones regulares", "Common.Views.SearchPanel.textName": "Nombre", "Common.Views.SearchPanel.textNoMatches": "No hay coincidencias", "Common.Views.SearchPanel.textNoSearchResults": "No hay resultados de búsqueda", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "Common.Views.SearchPanel.textReplaceWith": "<PERSON>em<PERSON><PERSON><PERSON> por", "Common.Views.SearchPanel.textSearch": "Búsqueda", "Common.Views.SearchPanel.textSearchAgain": "{0}Realiza nueva búsqueda{1} para obtener resultados precisos.", "Common.Views.SearchPanel.textSearchHasStopped": "La búsqueda se ha detenido", "Common.Views.SearchPanel.textSearchOptions": "Opciones de búsqueda", "Common.Views.SearchPanel.textSearchResults": "Resultados de búsqueda: {0}/{1}", "Common.Views.SearchPanel.textSelectDataRange": "<PERSON><PERSON><PERSON><PERSON><PERSON> rang<PERSON> de <PERSON>", "Common.Views.SearchPanel.textSheet": "Hoja", "Common.Views.SearchPanel.textSpecificRange": "Intervalo específico", "Common.Views.SearchPanel.textTooManyResults": "Hay demasiados resultados para mostrar aquí", "Common.Views.SearchPanel.textValue": "Valor", "Common.Views.SearchPanel.textValues": "Valores", "Common.Views.SearchPanel.textWholeWords": "<PERSON><PERSON><PERSON> palabras completas", "Common.Views.SearchPanel.textWithin": "<PERSON><PERSON> de", "Common.Views.SearchPanel.textWorkbook": "Libro de trabajo", "Common.Views.SearchPanel.tipNextResult": "Resul<PERSON><PERSON> si<PERSON>", "Common.Views.SearchPanel.tipPreviousResult": "Resultado anterior", "Common.Views.SelectFileDlg.textLoading": "Cargando", "Common.Views.SelectFileDlg.textTitle": "Seleccionar origen de datos", "Common.Views.SignDialog.textBold": "Negrita", "Common.Views.SignDialog.textCertificate": "Certificar", "Common.Views.SignDialog.textChange": "Cambiar", "Common.Views.SignDialog.textInputName": "Ingresar nombre de quien firma", "Common.Views.SignDialog.textItalic": "Cursiva", "Common.Views.SignDialog.textNameError": "El nombre del firmante no debe estar vacío.", "Common.Views.SignDialog.textPurpose": "Propósito al firmar este documento", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "Seleccionar imagen", "Common.Views.SignDialog.textSignature": "La firma se ve como", "Common.Views.SignDialog.textTitle": "Firmar documento", "Common.Views.SignDialog.textUseImage": "o pulsar 'Seleccionar Imagen' para usar una imagen como firma", "Common.Views.SignDialog.textValid": "Válido desde %1 hasta %2", "Common.Views.SignDialog.tipFontName": "Nombre de fuente", "Common.Views.SignDialog.tipFontSize": "Tamaño de fuente", "Common.Views.SignSettingsDialog.textAllowComment": "Permitir al firmante agregar comentarios en el diálogo de firma", "Common.Views.SignSettingsDialog.textDefInstruction": "Antes de firmar este documento, verifique que el contenido que está firmando es correcto.", "Common.Views.SignSettingsDialog.textInfoEmail": "Correo electrónico del firmante sugerido", "Common.Views.SignSettingsDialog.textInfoName": "Firmante sugerido", "Common.Views.SignSettingsDialog.textInfoTitle": "Título del firmante sugerido", "Common.Views.SignSettingsDialog.textInstructions": "Instrucciones para quien firma", "Common.Views.SignSettingsDialog.textShowDate": "Presentar fecha de la firma", "Common.Views.SignSettingsDialog.textTitle": "Configuración de firma", "Common.Views.SignSettingsDialog.txtEmpty": "Este campo es obligatorio", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Valor HEX de Unicode", "Common.Views.SymbolTableDialog.textCopyright": "Signo de Copyright", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON><PERSON> do<PERSON> de cierre", "Common.Views.SymbolTableDialog.textDOQuote": "<PERSON><PERSON><PERSON> do<PERSON> de apertura", "Common.Views.SymbolTableDialog.textEllipsis": "Elipsis horizontal", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEnDash": "<PERSON><PERSON><PERSON> corto", "Common.Views.SymbolTableDialog.textEnSpace": "<PERSON><PERSON><PERSON><PERSON> corto", "Common.Views.SymbolTableDialog.textFont": "Letra ", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON><PERSON> sin ruptura", "Common.Views.SymbolTableDialog.textNBSpace": "Espacio de no separación", "Common.Views.SymbolTableDialog.textPilcrow": "Signo de antígrafo", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 <PERSON><PERSON>ac<PERSON>", "Common.Views.SymbolTableDialog.textRange": "Ra<PERSON>", "Common.Views.SymbolTableDialog.textRecent": "Símbolos utilizados recientemente", "Common.Views.SymbolTableDialog.textRegistered": "Signo de marca registrada", "Common.Views.SymbolTableDialog.textSCQuote": "Comillas simples de cierre", "Common.Views.SymbolTableDialog.textSection": "Signo de sección", "Common.Views.SymbolTableDialog.textShortcut": "Tecla de método abreviado", "Common.Views.SymbolTableDialog.textSHyphen": "G<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSOQuote": "Comillas simples de apertura", "Common.Views.SymbolTableDialog.textSpecial": "Caracteres especiales", "Common.Views.SymbolTableDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textTitle": "Símbolo", "Common.Views.SymbolTableDialog.textTradeMark": "Símbolo de marca comercial", "Common.Views.UserNameDialog.textDontShow": "No volver a preguntarme", "Common.Views.UserNameDialog.textLabel": "Etiqueta:", "Common.Views.UserNameDialog.textLabelError": "La etiqueta no debe estar vacía.", "SSE.Controllers.DataTab.strSheet": "Hoja", "SSE.Controllers.DataTab.textAddExternalData": "Se ha añadido el enlace a un origen externo. Puede actualizar tales enlaces en la pestaña Datos.", "SSE.Controllers.DataTab.textColumns": "Columnas", "SSE.Controllers.DataTab.textDontUpdate": "No actualizar", "SSE.Controllers.DataTab.textEmptyUrl": "Debe especificar la URL.", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON>", "SSE.Controllers.DataTab.textUpdate": "Actualizar", "SSE.Controllers.DataTab.textWizard": "Texto en columnas", "SSE.Controllers.DataTab.txtDataValidation": "Validación de datos", "SSE.Controllers.DataTab.txtErrorExternalLink": "Se ha producido un error al actualizar", "SSE.Controllers.DataTab.txtExpand": "Expandir", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Los datos junto a la selección no serán eliminados. ¿Quiere ampliar la selección para incluir los datos adyacentes o continuar sólo con las celdas actualmente seleccionadas?", "SSE.Controllers.DataTab.txtExtendDataValidation": "La selección contiene algunas celdas sin ajustes de Validación de Datos.<br>¿Desea extender la Validación de Datos a estas celdas?", "SSE.Controllers.DataTab.txtImportWizard": "Asistente para importar texto", "SSE.Controllers.DataTab.txtRemDuplicates": "Eliminar duplicados", "SSE.Controllers.DataTab.txtRemoveDataValidation": "La selección contiene más de un tipo de validación.<br>¿Borrar los ajustes actuales y continuar?", "SSE.Controllers.DataTab.txtRemSelected": "Eliminar en seleccionado", "SSE.Controllers.DataTab.txtUrlTitle": "Pegar una URL de datos", "SSE.Controllers.DataTab.warnUpdateExternalData": "Este libro de trabajo contiene enlaces a uno o más orígenes externos que podrían ser inseguros.<br>Si confía en estos enlaces, actualícelos para obtener los datos más recientes.", "SSE.Controllers.DocumentHolder.alignmentText": "Alineación", "SSE.Controllers.DocumentHolder.centerText": "Al centro", "SSE.Controllers.DocumentHolder.deleteColumnText": "Eliminar columna", "SSE.Controllers.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON> fila", "SSE.Controllers.DocumentHolder.deleteText": "Eliminar", "SSE.Controllers.DocumentHolder.errorInvalidLink": "El enlace no existe. <PERSON>r favor, corrija o elimine el enlace.", "SSE.Controllers.DocumentHolder.guestText": "Visitante", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Columna i<PERSON>erda", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON>na derecha", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Fila de arriba", "SSE.Controllers.DocumentHolder.insertRowBelowText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertText": "Insertar", "SSE.Controllers.DocumentHolder.leftText": "A la izquierda", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Aviso", "SSE.Controllers.DocumentHolder.rightText": "A la derecha", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Opciones de Autocorrección", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON>cho de columna {0} símbolos ({1} píxeles)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Altura de fila {0} puntos ({1} píxeles)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Haga clic en el enlace para abrirlo o haga clic y mantenga pulsado el botón del ratón para seleccionar la celda.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Insertar columna a la izquierda", "SSE.Controllers.DocumentHolder.textInsertTop": "Insertar fila arriba", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Pegado especial", "SSE.Controllers.DocumentHolder.textStopExpand": "Interrumpir la expansión automática de las tablas", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Este elemento está siendo editado por otro usuario.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Superior a la media", "SSE.Controllers.DocumentHolder.txtAddBottom": "Agregar borde inferior", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Agregar barra de fracción", "SSE.Controllers.DocumentHolder.txtAddHor": "Agregar línea horizontal", "SSE.Controllers.DocumentHolder.txtAddLB": "Agregar línea inferior izquierda", "SSE.Controllers.DocumentHolder.txtAddLeft": "Agre<PERSON> borde <PERSON>", "SSE.Controllers.DocumentHolder.txtAddLT": "Agregar línea superior izquierda", "SSE.Controllers.DocumentHolder.txtAddRight": "Agregar borde derecho", "SSE.Controllers.DocumentHolder.txtAddTop": "Agregar borde superior", "SSE.Controllers.DocumentHolder.txtAddVer": "Agregar línea vertical", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Alinear a carácter", "SSE.Controllers.DocumentHolder.txtAll": "(Todos)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Devuelve todo el contenido de la tabla o de las columnas de la tabla especificadas, incluyendo las cabeceras de las columnas, los datos y las filas totales", "SSE.Controllers.DocumentHolder.txtAnd": "y", "SSE.Controllers.DocumentHolder.txtBegins": "Empieza con", "SSE.Controllers.DocumentHolder.txtBelowAve": "Debajo de la media", "SSE.Controllers.DocumentHolder.txtBlanks": "(Vacíos)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Propiedades de borde", "SSE.Controllers.DocumentHolder.txtBottom": "Abajo ", "SSE.Controllers.DocumentHolder.txtColumn": "Columna", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Alineación de columna", "SSE.Controllers.DocumentHolder.txtContains": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Enlace copiado al portapapeles", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Devuelve las celdas de datos de la tabla o de las columnas de la tabla especificadas", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON><PERSON> de <PERSON>o", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Eliminar argumento", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Borrar abertura manual", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Eliminar carácteres encerrados", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Eliminar caracteres encerrados y separadores", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Eliminar ecuación", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Eliminar radical", "SSE.Controllers.DocumentHolder.txtEnds": "Termina con", "SSE.Controllers.DocumentHolder.txtEquals": "I<PERSON>les", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Igual al color de celda", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Igual al color de fuente", "SSE.Controllers.DocumentHolder.txtExpand": "Expandir y ordenar", "SSE.Controllers.DocumentHolder.txtExpandSort": "Los datos al lado del rango seleccionado no serán ordenados. ¿Quiere Usted expandir el rango seleccionado para incluir datos de las celdas adyacentes o continuar ordenación del rango seleccionado?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "<PERSON>ás bajo", "SSE.Controllers.DocumentHolder.txtFilterTop": "Top", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Cambiar a la fracción lineal", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Cambiar a la fracción sesgada", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Cambiar a la fracción apilada", "SSE.Controllers.DocumentHolder.txtGreater": "Mayor que", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "<PERSON> que o igual a", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Char sobre texto", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Char debajo de texto", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Devuelve las cabeceras de las columnas de la tabla o de las columnas de la tabla especificadas", "SSE.Controllers.DocumentHolder.txtHeight": "Altura", "SSE.Controllers.DocumentHolder.txtHideBottom": "Esconder borde inferior", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Esconder límite inferior", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Esconder corchete de cierre", "SSE.Controllers.DocumentHolder.txtHideDegree": "Ocultar grado", "SSE.Controllers.DocumentHolder.txtHideHor": "Esconder línea horizontal", "SSE.Controllers.DocumentHolder.txtHideLB": "Esconder línea inferior izquierda ", "SSE.Controllers.DocumentHolder.txtHideLeft": "Esconder borde i<PERSON>o", "SSE.Controllers.DocumentHolder.txtHideLT": "Esconder línea superior izquierda", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Esconder corchete de apertura", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Esconder marcador de posición", "SSE.Controllers.DocumentHolder.txtHideRight": "Esconder borde derecho", "SSE.Controllers.DocumentHolder.txtHideTop": "Ocultar borde superior", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Ocultar límite superior", "SSE.Controllers.DocumentHolder.txtHideVer": "Ocultar línea vertical", "SSE.Controllers.DocumentHolder.txtImportWizard": "Asistente para importar texto", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Aumentar el tamaño del argumento", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Insertar argumento después", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Insertar argumento antes", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Insertar salto manual", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Insertar la ecuación después", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Insertar la ecuación antes", "SSE.Controllers.DocumentHolder.txtItems": "objetos", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON><PERSON> solo texto", "SSE.Controllers.DocumentHolder.txtLess": "<PERSON><PERSON> que", "SSE.Controllers.DocumentHolder.txtLessEquals": "<PERSON>or que o igual a", "SSE.Controllers.DocumentHolder.txtLimitChange": "Cambiar ubicación de límites", "SSE.Controllers.DocumentHolder.txtLimitOver": "Límite sobre el texto", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Límite debajo del texto", "SSE.Controllers.DocumentHolder.txtLockSort": "Se encuentran datos junto a su selección, pero no tiene permisos suficientes para modificar esas celdas.<br>¿Desea continuar con la selección actual?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Coincidir corchetes con el alto de los argumentos", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Alineación de la matriz", "SSE.Controllers.DocumentHolder.txtNoChoices": "No hay selecciones para llenar la celda.<br>Se puede seleccionar solo valores de texto de columna para recambio.", "SSE.Controllers.DocumentHolder.txtNotBegins": "No empieza con", "SSE.Controllers.DocumentHolder.txtNotContains": "No contiene", "SSE.Controllers.DocumentHolder.txtNotEnds": "No termina con", "SSE.Controllers.DocumentHolder.txtNotEquals": "No es igual", "SSE.Controllers.DocumentHolder.txtOr": "o", "SSE.Controllers.DocumentHolder.txtOverbar": "Barra sobre texto", "SSE.Controllers.DocumentHolder.txtPaste": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formula sin bordes", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formula + ancho de columna", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Formato de destino", "SSE.Controllers.DocumentHolder.txtPasteFormat": "<PERSON><PERSON><PERSON> solo formato ", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formula + formato de número", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "<PERSON><PERSON><PERSON> solo formula", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formula + todo formateo", "SSE.Controllers.DocumentHolder.txtPasteLink": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Imagen vinculada", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Combinar el formato condicional", "SSE.Controllers.DocumentHolder.txtPastePicture": "Imagen", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Formato de origen", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Transponer", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Valor + todo formato", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Valor + formato de número", "SSE.Controllers.DocumentHolder.txtPasteValues": "<PERSON><PERSON>ar solo valor", "SSE.Controllers.DocumentHolder.txtPercent": "<PERSON><PERSON> cientos", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Re-hacer expansión automática de la tabla", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Quitar la barra de fracción", "SSE.Controllers.DocumentHolder.txtRemLimit": "Eliminar límite", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "<PERSON><PERSON><PERSON> car<PERSON>cter de acento", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Eliminar barra", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "¿Desea eliminar esta firma?<br>No se puede deshacer.", "SSE.Controllers.DocumentHolder.txtRemScripts": "<PERSON><PERSON>ar índices", "SSE.Controllers.DocumentHolder.txtRemSubscript": "<PERSON><PERSON><PERSON> sub<PERSON>", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "<PERSON><PERSON>ar super<PERSON>", "SSE.Controllers.DocumentHolder.txtRowHeight": "Altura de fila", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Índices después de texto", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Índices antes de texto", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Mostrar límite inferior", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Mostrar corchete de cierre", "SSE.Controllers.DocumentHolder.txtShowDegree": "Mostrar grado", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Mostrar corchete de apertura", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Mostrar marcador de posición", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Mostrar límite superior", "SSE.Controllers.DocumentHolder.txtSorting": "Ordenación", "SSE.Controllers.DocumentHolder.txtSortSelected": "Ordenar los objetos seleccionados", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Estirar corchetes", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Elija solo esta fila de la columna especificada", "SSE.Controllers.DocumentHolder.txtTop": "Top", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Devuelve el total de filas de la tabla o de las columnas de la tabla especificadas", "SSE.Controllers.DocumentHolder.txtUnderbar": "Barra debajo de texto", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Deshacer la expansión automática de la tabla", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Usar el Asistente para importar texto", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Hacer clic en este enlace puede ser perjudicial para su dispositivo y sus datos.<br>¿Está seguro de que quiere continuar?", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryAll": "Todo", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Base de datos", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "<PERSON><PERSON> y hora", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Ingeniería", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Financiero", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Información", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 usados por última vez", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Lógico", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Búsqueda y referencia", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matemáticas y trigonometría", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Estadístico", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Texto y datos", "SSE.Controllers.LeftMenu.newDocumentTitle": "Hoja de cálculo sin nombre", "SSE.Controllers.LeftMenu.textByColumns": "Columnas", "SSE.Controllers.LeftMenu.textByRows": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textFormulas": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "SSE.Controllers.LeftMenu.textItemEntireCell": "Todo el contenido de celda", "SSE.Controllers.LeftMenu.textLoadHistory": "Cargando historial de versiones...", "SSE.Controllers.LeftMenu.textLookin": "Buscar en ", "SSE.Controllers.LeftMenu.textNoTextFound": "No se puede encontrar los datos que usted busca. Por favor, ajuste los parámetros de búsqueda.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Se ha realizado el reemplazo. {0} ocurrencias fueron saltadas.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "La búsqueda se ha realizado. Ocurrencias reemplazadas: {0}", "SSE.Controllers.LeftMenu.textSearch": "Buscar", "SSE.Controllers.LeftMenu.textSheet": "Hoja", "SSE.Controllers.LeftMenu.textValues": "Valores", "SSE.Controllers.LeftMenu.textWarning": "Aviso", "SSE.Controllers.LeftMenu.textWithin": "<PERSON><PERSON> de", "SSE.Controllers.LeftMenu.textWorkbook": "Libro de trabajo", "SSE.Controllers.LeftMenu.txtUntitled": "Sin título", "SSE.Controllers.LeftMenu.warnDownloadAs": "Si sigue guardando en este formato todas las características a excepción del texto se perderán.<br> ¿Está seguro de que quiere continuar?", "SSE.Controllers.Main.confirmAddCellWatches": "Esta acción añadirá {0} inspecciones de celda.<br>¿Desea continuar?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "Esta acción añadirá sólo {0} inspecciones de celda por motivo de guardar memoria.<br>¿<PERSON>ea continuar?", "SSE.Controllers.Main.confirmMaxChangesSize": "El tamaño de las acciones excede la limitación establecida para su servidor.<br><PERSON><PERSON> \"Deshacer\" para cancelar su última acción o pulse \"Continuar\" para mantener la acción localmente (debe descargar el archivo o copiar su contenido para asegurarse de que no se pierde nada).", "SSE.Controllers.Main.confirmMoveCellRange": "El rango de celdas final puede contener los datos. ¿Quiere continuar?", "SSE.Controllers.Main.confirmPutMergeRange": "Los datos de origen contienen celdas combinadas.<br><PERSON>b<PERSON>n estado sin combinar antes de que se pegaran en la tabla.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Las fórmulas de la fila de encabezado se eliminarán y se convertirán en texto estático.<br>¿<PERSON>ea continuar?", "SSE.Controllers.Main.convertationTimeoutText": "Se superó el tiempo de espera de conversión.", "SSE.Controllers.Main.criticalErrorExtText": "Pulse \"OK\" para regresar a la lista de documentos.", "SSE.Controllers.Main.criticalErrorTitle": "Error", "SSE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON> <PERSON>.", "SSE.Controllers.Main.downloadTextText": "Cargando hoja de cálculo...", "SSE.Controllers.Main.downloadTitleText": "Cargando hoja de c<PERSON>lo", "SSE.Controllers.Main.errNoDuplicates": "No se han encontrado valores duplicados.", "SSE.Controllers.Main.errorAccessDeny": "Usted no tiene permisos para realizar la acción que está intentando hacer.<br> <PERSON>r favor, contacte con el Administrador del Servidor de Documentos.", "SSE.Controllers.Main.errorArgsRange": "Un error en la fórmula introducida.<br>Se está usando un intervalo de argumentos incorrecto.", "SSE.Controllers.Main.errorAutoFilterChange": "La operación no está permitida, ya que está intentando cambiar celdas en una tabla de su hoja de cálculo.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "No se puede realizar la operación para las celdas seleccionadas porque usted no puede mover una parte de la tabla.<br> Seleccione otro rango de celdas para que toda la tabla sea seleccionada e intente de nuevo.", "SSE.Controllers.Main.errorAutoFilterDataRange": "No se puede realizar la operación para el rango de celdas seleccionado.<br>Seleccione un rango de datos uniforme diferente del existente y vuelva a intentarlo.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "No se puede realizar la operación porque el área contiene celdas filtradas.<br>Por favor muestre los elementos filtrados y vuelva a intentarlo.", "SSE.Controllers.Main.errorBadImageUrl": "URL de imagen es incorrecto", "SSE.Controllers.Main.errorCannotPasteImg": "No podemos pegar esta imagen desde el Portapapeles, pero puede guardarla en su dispositivo e \ninsertarla desde allí, o puede copiar la imagen sin texto y pegarla en la hoja de cálculo.", "SSE.Controllers.Main.errorCannotUngroup": "No se puede desagrupar. Para crear un esquema de documento seleccione filas o columnas y agrúpelas.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "No puede utilizar esta orden en una hoja protegida. Para usar esta orden, desproteja la hoja. <br> Es posible que le pidan una contraseña.", "SSE.Controllers.Main.errorChangeArray": "No se puede cambiar parte de una matriz.", "SSE.Controllers.Main.errorChangeFilteredRange": "Esto cambiará un rango filtrado de la hoja de cálculo.<br>Para completar esta tarea, quite los Autofiltros.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "La celda o el gráfico que está intentando cambiar se encuentra en una hoja protegida.<br>Para hacer un cambio, quitele la protección a la hoja. Es posible que se le pida que introduzca una contraseña.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Se ha perdido la conexión con servidor. El documento no puede ser editado ahora.", "SSE.Controllers.Main.errorConnectToServer": "No se consiguió guardar el documento. Por favor, compruebe los ajustes de conexión o póngase en contacto con su administrador.<br>Al hacer clic en el botón 'OK' se le solicitará que descargue el documento.", "SSE.Controllers.Main.errorConvertXml": "El archivo tiene un formato no soportado.<br>S<PERSON>lo se puede utilizar el formato XML Spreadsheet 2003.", "SSE.Controllers.Main.errorCopyMultiselectArea": "No se puede usar este comando con varias selecciones.<br>Seleccione un solo rango e intente de nuevo.", "SSE.Controllers.Main.errorCountArg": "Un error en la fórmula introducida.<br>Se está usando un número de argumentos incorrecto.", "SSE.Controllers.Main.errorCountArgExceed": "Un error en la fórmula introducida.<br>El número de argumentos ha sido excedido.", "SSE.Controllers.Main.errorCreateDefName": "Los rangos con nombre existentes no pueden ser editados y los nuevos no se pueden crear<br>en este momento ya que algunos de ellos están editándose.", "SSE.Controllers.Main.errorDatabaseConnection": "Error externo.<br>Error de conexión de base de datos. Por favor póngase en contacto con soporte si el error se mantiene.", "SSE.Controllers.Main.errorDataEncrypted": "Se han recibido cambios cifrados, ellos no pueden ser descifrados.", "SSE.Controllers.Main.errorDataRange": "<PERSON><PERSON>.", "SSE.Controllers.Main.errorDataValidate": "El valor que ha introducido no es válido.<br>Un usuario ha restringido los valores que pueden ser introducidos en esta celda.", "SSE.Controllers.Main.errorDefaultMessage": "Código de error: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Está intentando eliminar una columna que contiene una celda bloqueada. Las celdas bloqueadas no pueden borrarse mientras la hoja de cálculo esté protegida.<br> Para borrar una celda bloqueada, desproteja la hoja. Es posible que se le pida que introduzca una contraseña.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Está intentando eliminar una fila que contiene una celda bloqueada. Las celdas bloqueadas no se pueden eliminar mientras la hoja de cálculo esté protegida.<br> Para eliminar una celda bloqueada, desproteja la hoja. Es posible que se le pida que introduzca una contraseña.", "SSE.Controllers.Main.errorDirectUrl": "Por favor, verifique el vínculo al documento.<br><PERSON><PERSON> vínculo debe ser un vínculo directo al archivo para descargar.", "SSE.Controllers.Main.errorEditingDownloadas": "Se produjo un error durante el trabajo con el documento.<br>Use la opción 'Descargar como' para guardar la copia de seguridad de este archivo en el disco duro.", "SSE.Controllers.Main.errorEditingSaveas": "Se produjo un error durante el trabajo con el documento.<br>Use la opción 'Guardar como...' para guardar la copia de seguridad de este archivo en el disco duro.", "SSE.Controllers.Main.errorEditView": "La vista de hoja existente no puede ser editada y las nuevas no se pueden crear en este momento, ya que algunas de ellas se están editando.", "SSE.Controllers.Main.errorEmailClient": "No se pudo encontrar ningun cliente de correo", "SSE.Controllers.Main.errorFilePassProtect": "El archivo está protegido por una contraseña y no puede ser abierto.", "SSE.Controllers.Main.errorFileRequest": "Error externo.<br><PERSON><PERSON><PERSON> de solicitud de archivo. Por favor póngase en contacto con soporte si el error se mantiene.", "SSE.Controllers.Main.errorFileSizeExceed": "El tamaño del archivo excede la limitación establecida para su servidor. Por favor, póngase en contacto con el administrador del Servidor de documentos para obtener más detalles. ", "SSE.Controllers.Main.errorFileVKey": "Error externo.<br><PERSON><PERSON>e de seguridad incorrecto. Por favor póngase en contacto con soporte si el error se mantiene.", "SSE.Controllers.Main.errorFillRange": "Es imposible rellenar el rango de celdas seleccionado.<br><PERSON>das las celdas seleccionadas deben tener el mismo tamaño.", "SSE.Controllers.Main.errorForceSave": "Se produjo un error al guardar el archivo. Utilice la opción \"Descargar como\" para guardar el archivo en el disco duro o inténtelo de nuevo más tarde.", "SSE.Controllers.Main.errorFormulaName": "Un error en la fórmula introducida.<br>Nombre de fórmula incorrecto.", "SSE.Controllers.Main.errorFormulaParsing": "Error interno mientras analizando la fórmula.", "SSE.Controllers.Main.errorFrmlMaxLength": "La longitud de su fórmula excede el límite de 8192 carácteres.<br><PERSON>r <PERSON>, edí<PERSON>a e intente de nuevo.", "SSE.Controllers.Main.errorFrmlMaxReference": "No puede introducir esta fórmula porque tiene demasiados valores,<br>referencias de celda, y/o nombres.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Valores de texto en fórmulas son limitados al número de caracteres - 255.<br>Use la función CONCATENAR u operador de concatenación (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "La función se refiere a una hoja que no existe. <br> <PERSON>r favor, compruebe los datos e inténtelo de nuevo.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "La operación no se pudo completar para el rango de celdas seleccionado.<br>Seleccione un rango de modo que la primera fila de la tabla esté en la misma fila <br> y la tabla resultante se superponga a la actual.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "La operación no se pudo completar para el rango de celdas seleccionado.<br>Seleccione un rango que no incluye otras tablas.", "SSE.Controllers.Main.errorInconsistentExt": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo no coincide con la extensión del mismo.", "SSE.Controllers.Main.errorInconsistentExtDocx": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a documentos de texto (por ejemplo, docx), pero el archivo tiene extensión inconsistente: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a uno de los siguientes formatos: pdf/djvu/xps/oxps, pero el archivo tiene extensión inconsistente: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a presentaciones (por ejemplo, pptx), pero el archivo tiene extensión inconsistente: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a hojas de cálculo (por ejemplo, xlsx), pero el archivo tiene extensión inconsistente: %1.", "SSE.Controllers.Main.errorInvalidRef": "Introducir un nombre correcto para la selección o una referencia válida para ir a.", "SSE.Controllers.Main.errorKeyEncrypt": "Descriptor de clave desconocido", "SSE.Controllers.Main.errorKeyExpire": "Descriptor de clave ha expirado", "SSE.Controllers.Main.errorLabledColumnsPivot": "Para crear una tabla dinámica, utilice datos que estén organizados como una lista con columnas etiquetadas.", "SSE.Controllers.Main.errorLoadingFont": "Las fuentes no están cargadas.<br><PERSON><PERSON> <PERSON>, póngase en contacto con el administrador del Document Server.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "La referencia a la ubicación o al rango de datos no es válida.", "SSE.Controllers.Main.errorLockedAll": "No se pudo realizar la operación porque la hoja ha sido bloqueada por otro usuario.", "SSE.Controllers.Main.errorLockedCellPivot": "No puede modificar datos dentro de una tabla dinámica.", "SSE.Controllers.Main.errorLockedWorksheetRename": "No se puede cambiar el nombre de la hoja en este momento, porque se está cambiando el nombre por otro usuario", "SSE.Controllers.Main.errorMaxPoints": "El número máximo de puntos en serie por gráfico es 4096.", "SSE.Controllers.Main.errorMoveRange": "Es imposible cambiar una parte de la celda unida", "SSE.Controllers.Main.errorMoveSlicerError": "Las segmentaciones de tabla no pueden ser copiadas de un libro a otro.<br>Inténtelo de nuevo al seleccionar toda la tabla y las segmentaciones.", "SSE.Controllers.Main.errorMultiCellFormula": "Fórmulas de matriz con celdas múltiples no están permitidas en tablas.", "SSE.Controllers.Main.errorNoDataToParse": "No se seleccionaron datos para redistribuir.", "SSE.Controllers.Main.errorOpenWarning": "Una de las fórmulas del archivo excede el límite de 8192 caracteres.<br>La fórmula fue eliminada.", "SSE.Controllers.Main.errorOperandExpected": "La función de sintaxis introducida no es correcta. Le recomendamos verificar si no le hace falta uno del paréntesis - '(' o ')'", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "La contraseña que ha proporcionado no es correcta.<br>Verifique que la tecla Bloq Mayús está desactivada y asegúrese de utilizar las mayúsculas correctas.", "SSE.Controllers.Main.errorPasteMaxRange": "El área de copiar no coincide con el área de pegar.<br>Para pegar las celdas copiadas, por favor, seleccione una zona con el mismo tamaño o haga clic en la primera celda de una fila.", "SSE.Controllers.Main.errorPasteMultiSelect": "Esta acción no se puede realizar en un rango de selecciones múltiples.<br>Seleccione un solo rango y vuelva a intentarlo.", "SSE.Controllers.Main.errorPasteSlicerError": "Las segmentaciones de tabla no pueden ser copiadas de un libro a otro.", "SSE.Controllers.Main.errorPivotGroup": "No se puede agrupar esta selección.", "SSE.Controllers.Main.errorPivotOverlap": "El informe de la tabla dinámica no puede superponerse a la tabla.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "El informe de la tabla dinámica se ha guardado sin los datos subyacentes.<br>Utilice el botón 'Actualizar' para actualizar el informe.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Lamentablemente, no es posible imprimir más de 1500 páginas a la vez en la versión actual del programa.<br>Esta restricción será eliminada en próximos lanzamientos.", "SSE.Controllers.Main.errorProcessSaveResult": "Problemas al guardar", "SSE.Controllers.Main.errorServerVersion": "La versión del editor ha sido actualizada. La página será recargada para aplicar los cambios.", "SSE.Controllers.Main.errorSessionAbsolute": "Sesión de editar el documento ha expirado. Por favor, recargue la página.", "SSE.Controllers.Main.errorSessionIdle": "El documento no ha sido editado durante bastante tiempo. Por favor, recargue la página.", "SSE.Controllers.Main.errorSessionToken": "Conexión al servidor ha sido interrumpido. Por favor, recargue la página.", "SSE.Controllers.Main.errorSetPassword": "No se pudo establecer la contraseña.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "La referencia de ubicación no es válida porque las celdas no están todas en la misma columna o fila.<br>Seleccione las celdas que están todas en una sola columna o fila.", "SSE.Controllers.Main.errorStockChart": "Orden de las filas incorrecto. Para compilar un gráfico de cotizaciones introduzca los datos en la hoja de tal modo:<br> precio de apertura, precio máxi<PERSON>, precio mín<PERSON>, precio de cierre.", "SSE.Controllers.Main.errorToken": "El token de seguridad de documento tiene un formato incorrecto.<br>Por favor, contacte con el Administrador del Servidor de Documentos.", "SSE.Controllers.Main.errorTokenExpire": "El token de seguridad de documento ha sido expirado.<br><PERSON>r favor, contacte con el Administrador del Servidor de Documentos.", "SSE.Controllers.Main.errorUnexpectedGuid": "Error externo.<br>GUID inesparada. Por favor póngase en contacto con soporte si el error mantiene.", "SSE.Controllers.Main.errorUpdateVersion": "Se ha cambiado la versión del archivo. La página será actualizada.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Se ha restablecido la conexión a Internet y se ha cambiado la versión del archivo. <br>Para poder seguir trabajando, es necesario descargar el archivo o copiar su contenido para asegurarse de que no se ha perdido nada, y luego volver a cargar esta página.", "SSE.Controllers.Main.errorUserDrop": "No se puede acceder al archivo ahora.", "SSE.Controllers.Main.errorUsersExceed": "Se superó la cantidad de usuarios permitidos por el plan de precios", "SSE.Controllers.Main.errorViewerDisconnect": "Se ha perdido la conexión. Usted todavía puede visualizar el documento,<br> pero no puede descargarlo o imprimirlo hasta que la conexión sea restaurada y la página sea recargada.", "SSE.Controllers.Main.errorWrongBracketsCount": "Un error en la fórmula introducida.<br><PERSON>st<PERSON> usando un número incorrecto de corchetes.", "SSE.Controllers.Main.errorWrongOperator": "Un error en la fórmula introducida. Un operador no válido está siendo usado.<br>Por favor, corrija el error.", "SSE.Controllers.Main.errorWrongPassword": "La contraseña que ha proporcionado no es correcta.", "SSE.Controllers.Main.errRemDuplicates": "Duplicar valores encontrados y eliminados: {0}, valores únicos restantes: {1}.", "SSE.Controllers.Main.leavePageText": "Usted tiene cambios no guardados en esta hoja de cálculo. Haga clic en 'Permanecer en esta página', después 'Guardar' para guardarlos. Haga clic en 'Abandonar esta página' para descartar todos los cambios no guardados.", "SSE.Controllers.Main.leavePageTextOnClose": "Todos los cambios no guardados en esta hoja de cálculo se perderán.<br> <PERSON>ga clic en \"Cancelar\" y luego en \"Guardar\" para guardarlos. Haga clic en \" OK \" para deshacerse de todos los cambios no guardados.", "SSE.Controllers.Main.loadFontsTextText": "Cargando datos...", "SSE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadFontTextText": "Cargando datos...", "SSE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadImagesTextText": "Cargando imágenes...", "SSE.Controllers.Main.loadImagesTitleText": "<PERSON>gan<PERSON>", "SSE.Controllers.Main.loadImageTextText": "Cargando imagen...", "SSE.Controllers.Main.loadImageTitleText": "Cargando imagen", "SSE.Controllers.Main.loadingDocumentTitleText": "Cargando hoja de c<PERSON>lo", "SSE.Controllers.Main.notcriticalErrorTitle": "Aviso", "SSE.Controllers.Main.openErrorText": "Se ha producido un error al abrir el archivo ", "SSE.Controllers.Main.openTextText": "Abriendo hoja de cálculo...", "SSE.Controllers.Main.openTitleText": "Abriendo hoja de cálculo", "SSE.Controllers.Main.pastInMergeAreaError": "No se puede cambiar parte de una celda combinada", "SSE.Controllers.Main.printTextText": "Imprimiendo hoja de cálculo...", "SSE.Controllers.Main.printTitleText": "Imprimiendo hoja de cálculo", "SSE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.requestEditFailedMessageText": "Alguien está editando este documento en este momento. Por favor, inténtelo de nuevo más tarde.", "SSE.Controllers.Main.requestEditFailedTitleText": "Acceso denegado", "SSE.Controllers.Main.saveErrorText": "Se ha producido un error al guardar el archivo ", "SSE.Controllers.Main.saveErrorTextDesktop": "Este archivo no se puede guardar o crear.<br>Las razones posibles son: <br>1. El archivo es sólo para leer. <br>2. El archivo está siendo editado por otros usuarios. <br>3. El disco está lleno o corrupto.", "SSE.Controllers.Main.saveTextText": "Guardando hoja de cálculo...", "SSE.Controllers.Main.saveTitleText": "Guardando hoja de c<PERSON>lo", "SSE.Controllers.Main.scriptLoadError": "La conexión a Internet es demasiado lenta, no se podía cargar algunos componentes. Por favor, recargue la página.", "SSE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textApplyAll": "Aplicar a todas las ecuaciones", "SSE.Controllers.Main.textBuyNow": "Visitar sitio web", "SSE.Controllers.Main.textChangesSaved": "Todos los cambios son guardados", "SSE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textCloseTip": "Pulse para cerrar el consejo", "SSE.Controllers.Main.textConfirm": "Confirmación", "SSE.Controllers.Main.textContactUs": "Contactar con equipo de ventas", "SSE.Controllers.Main.textContinue": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textConvertEquation": "Esta ecuación fue creada con una versión antigua del editor de ecuaciones que ya no es compatible. Para editarla, convierta la ecuación al formato ML de Office Math.<br>¿Convertir ahora?", "SSE.Controllers.Main.textCustomLoader": "Note, por favor, que según los términos de la licencia Usted no tiene derecho a cambiar el cargador.<br>Por favor, póngase en contacto con nuestro Departamento de Ventas para obtener una cotización.", "SSE.Controllers.Main.textDisconnect": "Se ha perdido la conexión", "SSE.Controllers.Main.textFillOtherRows": "<PERSON><PERSON><PERSON> o<PERSON> filas", "SSE.Controllers.Main.textFormulaFilledAllRows": "La fórmula ha rellenado {0} filas que tienen datos. Rellenar otras filas vacías puede requerir unos minutos.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "La fórmula ha rellenado las primeras {0} filas. Rellenar otras filas vacías puede requerir unos minutos.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "La fórmula ha llenado sólo las primeras {0} filas que tienen datos por razones de ahorro de memoria. Hay otras {1} filas con datos en esta hoja. Puede rellenarlas manualmente.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "La fórmula ha llenado sólo las primeras {0} filas por razones de ahorro de memoria. Las demás filas de esta hoja no tienen datos.", "SSE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textHasMacros": "El archivo contiene macros automáticas.<br>¿Quiere ejecutar macros?", "SSE.Controllers.Main.textLearnMore": "Más información", "SSE.Controllers.Main.textLoadingDocument": "Cargando hoja de c<PERSON>lo", "SSE.Controllers.Main.textLongName": "Escriba un nombre que tenga menos de 128 caracteres.", "SSE.Controllers.Main.textNeedSynchronize": "Usted tiene actualizaciones", "SSE.Controllers.Main.textNo": "No", "SSE.Controllers.Main.textNoLicenseTitle": "Se ha alcanzado el límite de licencias", "SSE.Controllers.Main.textPaidFeature": "Función de pago", "SSE.Controllers.Main.textPleaseWait": "La operación puede tomar más tiempo de lo esperado. Espere por favor...", "SSE.Controllers.Main.textReconnect": "Se ha restablecido la conexión", "SSE.Controllers.Main.textRemember": "Recordar mi elección para todos los archivos", "SSE.Controllers.Main.textRememberMacros": "Recordar mi elección para todas las macros", "SSE.Controllers.Main.textRenameError": "El nombre de usuario no debe estar vacío.", "SSE.Controllers.Main.textRenameLabel": "Escriba un nombre que se utilizará para la colaboración", "SSE.Controllers.Main.textRequestMacros": "Una macro realiza una solicitud a la URL. ¿Quiere permitir la solicitud al %1?", "SSE.Controllers.Main.textShape": "Forma", "SSE.Controllers.Main.textStrict": "<PERSON><PERSON> estricto", "SSE.Controllers.Main.textText": "Texto", "SSE.Controllers.Main.textTryQuickPrint": "Ha seleccionado Impresión rápida: todo el documento se imprimirá en la última impresora seleccionada o predeterminada.<br>¿Desea continuar?", "SSE.Controllers.Main.textTryUndoRedo": "Las funciones Deshacer/<PERSON><PERSON><PERSON> están desactivadas para el modo co-edición rápido.<br><PERSON><PERSON> en el botón \"modo estricto\" para cambiar al modo de co-edición estricta para editar el archivo sin la interferencia de otros usuarios y enviar sus cambios sólo después de guardarlos. Se puede cambiar entre los modos de co-edición usando los ajustes avanzados de edición.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Las funciones Deshacer/<PERSON><PERSON><PERSON> están desactivadas en el modo de co-edición rápido.", "SSE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textYes": "Sí", "SSE.Controllers.Main.titleLicenseExp": "Licencia ha expirado", "SSE.Controllers.Main.titleServerVersion": "Editor ha sido actualizado", "SSE.Controllers.Main.txtAccent": "Ace<PERSON>", "SSE.Controllers.Main.txtAll": "(Todos)", "SSE.Controllers.Main.txtArt": "Su texto aquí", "SSE.Controllers.Main.txtBasicShapes": "Formas básicas", "SSE.Controllers.Main.txtBlank": "(en blanco)", "SSE.Controllers.Main.txtButtons": "Botones", "SSE.Controllers.Main.txtByField": "%1 de %2", "SSE.Controllers.Main.txtCallouts": "Llamadas", "SSE.Controllers.Main.txtCharts": "Grá<PERSON><PERSON>", "SSE.Controllers.Main.txtClearFilter": "<PERSON><PERSON><PERSON> filtro", "SSE.Controllers.Main.txtColLbls": "Etiquetas de columna", "SSE.Controllers.Main.txtColumn": "Columna", "SSE.Controllers.Main.txtConfidential": "Confidencial", "SSE.Controllers.Main.txtDate": "<PERSON><PERSON>", "SSE.Controllers.Main.txtDays": "Días", "SSE.Controllers.Main.txtDiagramTitle": "Título del gráfico", "SSE.Controllers.Main.txtEditingMode": "Establecer el modo de edición...", "SSE.Controllers.Main.txtErrorLoadHistory": "Error al cargar el historial", "SSE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON><PERSON> figu<PERSON>", "SSE.Controllers.Main.txtFile": "Archivo", "SSE.Controllers.Main.txtGrandTotal": "Total general", "SSE.Controllers.Main.txtGroup": "Agrupar", "SSE.Controllers.Main.txtHours": "<PERSON><PERSON>", "SSE.Controllers.Main.txtLines": "Líneas", "SSE.Controllers.Main.txtMath": "Matemáticas", "SSE.Controllers.Main.txtMinutes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMonths": "Meses", "SSE.Controllers.Main.txtMultiSelect": "Sele<PERSON><PERSON> múl<PERSON>", "SSE.Controllers.Main.txtOr": "%1 o %2", "SSE.Controllers.Main.txtPage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPageOf": "Página %1 de %2", "SSE.Controllers.Main.txtPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPreparedBy": "Preparado por", "SSE.Controllers.Main.txtPrintArea": "Área_de_impresión", "SSE.Controllers.Main.txtQuarter": "<PERSON><PERSON>.", "SSE.Controllers.Main.txtQuarters": "Trimestres", "SSE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtRowLbls": "Etiquetas de fila", "SSE.Controllers.Main.txtSeconds": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSeries": "Serie", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Llamada con línea 1 (borde y barra de énfasis)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Llamada con línea 2 (borde y barra de é<PERSON>fa<PERSON>)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Llamada con línea 3 (borde y barra de énfa<PERSON>)", "SSE.Controllers.Main.txtShape_accentCallout1": "Llamada con línea 1 (barra de <PERSON>)", "SSE.Controllers.Main.txtShape_accentCallout2": "Llamada con línea 2 (bar<PERSON> <PERSON>)", "SSE.Controllers.Main.txtShape_accentCallout3": "Llamada con línea 3 (barra <PERSON>)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Atrás o Botón Anterior", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Botón Al inicio", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Botón en blanco", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Botón Documento", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Botón Al fin", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Adelante o Botón Siguiente", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Bo<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonHome": "Botón Inicio", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Botón Información", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Botón Vídeo ", "SSE.Controllers.Main.txtShape_actionButtonReturn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonSound": "Botón Sonido", "SSE.Controllers.Main.txtShape_arc": "Arco", "SSE.Controllers.Main.txtShape_bentArrow": "Flecha do<PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5": "Conector angular", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Conector angular de flecha", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Conector angular de flecha doble", "SSE.Controllers.Main.txtShape_bentUpArrow": "Flecha doblada hacia arriba", "SSE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_blockArc": "Arco de bloque", "SSE.Controllers.Main.txtShape_borderCallout1": "Llamada con línea 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Llamada con línea 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Llamada con línea 3", "SSE.Controllers.Main.txtShape_bracePair": "Llaves", "SSE.Controllers.Main.txtShape_callout1": "Llamada con línea 1 (sin borde)", "SSE.Controllers.Main.txtShape_callout2": "Llamada con línea 2 (sin borde)", "SSE.Controllers.Main.txtShape_callout3": "Llamada con línea 3 (sin borde)", "SSE.Controllers.Main.txtShape_can": "Сilindro", "SSE.Controllers.Main.txtShape_chevron": "Cheurón", "SSE.Controllers.Main.txtShape_chord": "Acorde", "SSE.Controllers.Main.txtShape_circularArrow": "Flecha circular", "SSE.Controllers.Main.txtShape_cloud": "Nube", "SSE.Controllers.Main.txtShape_cloudCallout": "Llamada de nube", "SSE.Controllers.Main.txtShape_corner": "Esquina", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Conector curvado", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Conector curvado de flecha", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Conector curvado de flecha doble", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Flecha curvada hacia abajo", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Flecha curvada hacia la izquierda", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Flecha curvada hacia la derecha", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Flecha curvada hacia arriba", "SSE.Controllers.Main.txtShape_decagon": "Decágono", "SSE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON> diagonal", "SSE.Controllers.Main.txtShape_diamond": "Rombo", "SSE.Controllers.Main.txtShape_dodecagon": "Dodecágono", "SSE.Controllers.Main.txtShape_donut": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_doubleWave": "Doble onda", "SSE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON>cha a<PERSON>", "SSE.Controllers.Main.txtShape_downArrowCallout": "Llamada de flecha hacia abajo", "SSE.Controllers.Main.txtShape_ellipse": "Elipse", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Cinta curvada hacia abajo", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Cinta curvada hacia arriba", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Proceso alternativo", "SSE.Controllers.Main.txtShape_flowChartCollate": "Intercalar", "SSE.Controllers.Main.txtShape_flowChartConnector": "Conector", "SSE.Controllers.Main.txtShape_flowChartDecision": "Decisión", "SSE.Controllers.Main.txtShape_flowChartDelay": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDisplay": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDocument": "Documento", "SSE.Controllers.Main.txtShape_flowChartExtract": "Extracto", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Datos", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Almacenamiento interno", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Disco magnético", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Almacenamiento de acceso directo", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Almacenamiento de acceso secuencial", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Entrada manual", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Operación manual", "SSE.Controllers.Main.txtShape_flowChartMerge": "Combinar", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Multidocumento", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Conector fuera de <PERSON>a", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Datos almacenados", "SSE.Controllers.Main.txtShape_flowChartOr": "Diagrama de flujo: O", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Proceso predefinido", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Preparación", "SSE.Controllers.Main.txtShape_flowChartProcess": "Proceso", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Tarjeta", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Cinta perforada", "SSE.Controllers.Main.txtShape_flowChartSort": "Ordenar", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Y", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Terminador", "SSE.Controllers.Main.txtShape_foldedCorner": "Esquina doblada", "SSE.Controllers.Main.txtShape_frame": "<PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "Medio marco", "SSE.Controllers.Main.txtShape_heart": "Corazón", "SSE.Controllers.Main.txtShape_heptagon": "<PERSON>pt<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_hexagon": "Hexágono", "SSE.Controllers.Main.txtShape_homePlate": "Pentágono", "SSE.Controllers.Main.txtShape_horizontalScroll": "Pergamino horizontal", "SSE.Controllers.Main.txtShape_irregularSeal1": "Explosión 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Explosión 2", "SSE.Controllers.Main.txtShape_leftArrow": "Flecha izquierda", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Llamada de flecha a la izquierda", "SSE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON>r llave", "SSE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON> corchete", "SSE.Controllers.Main.txtShape_leftRightArrow": "Flecha izquierda y derecha", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Llamada de flecha izquierda y derecha", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Flecha izquierda, derecha y arriba", "SSE.Controllers.Main.txtShape_leftUpArrow": "Flecha izquierda y arriba", "SSE.Controllers.Main.txtShape_lightningBolt": "Rayo", "SSE.Controllers.Main.txtShape_line": "Lín<PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "Fle<PERSON>", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON><PERSON> doble", "SSE.Controllers.Main.txtShape_mathDivide": "División", "SSE.Controllers.Main.txtShape_mathEqual": "Igual", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Multiplicar", "SSE.Controllers.Main.txtShape_mathNotEqual": "No igual", "SSE.Controllers.Main.txtShape_mathPlus": "Más", "SSE.Controllers.Main.txtShape_moon": "Luna", "SSE.Controllers.Main.txtShape_noSmoking": "Señal de prohibición", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Flecha a la derecha con muesca", "SSE.Controllers.Main.txtShape_octagon": "Octágono", "SSE.Controllers.Main.txtShape_parallelogram": "Paralelogramo", "SSE.Controllers.Main.txtShape_pentagon": "Pentágono", "SSE.Controllers.Main.txtShape_pie": "Sector del círculo", "SSE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plus": "Más", "SSE.Controllers.Main.txtShape_polyline1": "A mano alzada", "SSE.Controllers.Main.txtShape_polyline2": "Forma libre", "SSE.Controllers.Main.txtShape_quadArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Llamada de flecha cu<PERSON>", "SSE.Controllers.Main.txtShape_rect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ribbon": "<PERSON>inta hacia abajo", "SSE.Controllers.Main.txtShape_ribbon2": "Cinta hacia arriba", "SSE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON> derecha", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Llamada de flecha a la derecha", "SSE.Controllers.Main.txtShape_rightBrace": "Ce<PERSON>r llave", "SSE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON><PERSON> corchete", "SSE.Controllers.Main.txtShape_round1Rect": "Redondear rectángulo de esquina sencilla", "SSE.Controllers.Main.txtShape_round2DiagRect": "Redondear rectángulo de esquina diagonal", "SSE.Controllers.Main.txtShape_round2SameRect": "Redondear rectángulo de esquina del mismo lado", "SSE.Controllers.Main.txtShape_roundRect": "Rectángulo con esquinas redondeadas", "SSE.Controllers.Main.txtShape_rtTriangle": "Triángulo rectángulo", "SSE.Controllers.Main.txtShape_smileyFace": "<PERSON>", "SSE.Controllers.Main.txtShape_snip1Rect": "Recortar rectángulo de esquina sencilla", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Recortar rectángulo de esquina diagonal", "SSE.Controllers.Main.txtShape_snip2SameRect": "Recortar rectángulo de esquina del mismo lado", "SSE.Controllers.Main.txtShape_snipRoundRect": "Recortar y redondear rectángulo de esquina sencilla", "SSE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star10": "Estrella de 10 puntas", "SSE.Controllers.Main.txtShape_star12": "Estrella de 12 puntas", "SSE.Controllers.Main.txtShape_star16": "Estrella de 16 puntas", "SSE.Controllers.Main.txtShape_star24": "Estrella de 24 puntas", "SSE.Controllers.Main.txtShape_star32": "Estrella de 32 puntas", "SSE.Controllers.Main.txtShape_star4": "Estrella de 4 puntas", "SSE.Controllers.Main.txtShape_star5": "Estrella de 5 puntas", "SSE.Controllers.Main.txtShape_star6": "Estrella de 6 puntas", "SSE.Controllers.Main.txtShape_star7": "Estrella de 7 puntas", "SSE.Controllers.Main.txtShape_star8": "Estrella de 8 puntas", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Flecha a la derecha con bandas", "SSE.Controllers.Main.txtShape_sun": "Sol", "SSE.Controllers.Main.txtShape_teardrop": "Lágrima", "SSE.Controllers.Main.txtShape_textRect": "Cuadro de texto", "SSE.Controllers.Main.txtShape_trapezoid": "Trapecio", "SSE.Controllers.Main.txtShape_triangle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_upArrow": "Flecha hacia arriba", "SSE.Controllers.Main.txtShape_upArrowCallout": "Llamada de flecha hacia arriba", "SSE.Controllers.Main.txtShape_upDownArrow": "Flecha hacia arriba y abajo", "SSE.Controllers.Main.txtShape_uturnArrow": "Flecha en U", "SSE.Controllers.Main.txtShape_verticalScroll": "Desplazamiento vertical", "SSE.Controllers.Main.txtShape_wave": "On<PERSON>", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Llamada ovalada", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Llamada rectangular", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Llamada rectangular redondeada", "SSE.Controllers.Main.txtStarsRibbons": "Cintas y estrellas", "SSE.Controllers.Main.txtStyle_Bad": "Malo", "SSE.Controllers.Main.txtStyle_Calculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Check_Cell": "Celda de control", "SSE.Controllers.Main.txtStyle_Comma": "Financiero", "SSE.Controllers.Main.txtStyle_Currency": "Moneda", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Texto explicativo", "SSE.Controllers.Main.txtStyle_Good": "Bueno", "SSE.Controllers.Main.txtStyle_Heading_1": "Título 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Título 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Título 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Título 4", "SSE.Controllers.Main.txtStyle_Input": "Entrada", "SSE.Controllers.Main.txtStyle_Linked_Cell": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Neutral": "Neutral", "SSE.Controllers.Main.txtStyle_Normal": "Normal", "SSE.Controllers.Main.txtStyle_Note": "<PERSON>a", "SSE.Controllers.Main.txtStyle_Output": "Salida", "SSE.Controllers.Main.txtStyle_Percent": "<PERSON>r ciento", "SSE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Total": "Total", "SSE.Controllers.Main.txtStyle_Warning_Text": "Texto de advertencia", "SSE.Controllers.Main.txtTab": "Pestaña", "SSE.Controllers.Main.txtTable": "Tabla", "SSE.Controllers.Main.txtTime": "<PERSON><PERSON>", "SSE.Controllers.Main.txtUnlock": "Desb<PERSON>que<PERSON>", "SSE.Controllers.Main.txtUnlockRange": "Desbloquear rango", "SSE.Controllers.Main.txtUnlockRangeDescription": "Introduzca la contraseña para cambiar este rango:", "SSE.Controllers.Main.txtUnlockRangeWarning": "Un rango que está tratando de cambiar está protegido por contraseña.", "SSE.Controllers.Main.txtValues": "Valores", "SSE.Controllers.Main.txtXAxis": "<PERSON><PERSON>", "SSE.Controllers.Main.txtYAxis": "<PERSON><PERSON>", "SSE.Controllers.Main.txtYears": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.unknownErrorText": "Error des<PERSON>.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Su navegador no está soportado.", "SSE.Controllers.Main.uploadDocExtMessage": "Formato de documento desconocido", "SSE.Controllers.Main.uploadDocFileCountMessage": "No hay documentos subidos", "SSE.Controllers.Main.uploadDocSizeMessage": "Límite de tamaño máximo del documento excedido.", "SSE.Controllers.Main.uploadImageExtMessage": "Formato de imagen desconocido.", "SSE.Controllers.Main.uploadImageFileCountMessage": "No hay imágenes subidas.", "SSE.Controllers.Main.uploadImageSizeMessage": "La imagen es demasiado grande. El tamaño máximo es de 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "Subiendo imagen...", "SSE.Controllers.Main.uploadImageTitleText": "Subiendo imagen", "SSE.Controllers.Main.waitText": "Por favor, espere...", "SSE.Controllers.Main.warnBrowserIE9": "Este aplicación tiene baja capacidad en IE9. Utilice IE10 o superior", "SSE.Controllers.Main.warnBrowserZoom": "La configuración actual de zoom de su navegador no está soportada por completo. Por favor restablezca al zoom predeterminado pulsando Ctrl+0.", "SSE.Controllers.Main.warnLicenseExceeded": "Usted ha alcanzado el límite de conexiones simultáneas con %1 editores. Este documento se abrirá sólo para su visualización.<br>Por favor, contacte con su administrador para recibir más información.", "SSE.Controllers.Main.warnLicenseExp": "Su licencia ha expirado.<br>Por favor, actualice su licencia y recargue la página.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Licencia expirada.<br>No tiene acceso a la funcionalidad de edición de documentos.<br>Por favor, póngase en contacto con su administrador.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "La licencia requiere ser renovada.<br>Tiene un acceso limitado a la funcionalidad de edición de documentos.<br>Por favor, póngase en contacto con su administrador para obtener un acceso completo", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Usted ha alcanzado el límite de usuarios para los editores de %1. <PERSON><PERSON> favor, contacte con su administrador para recibir más información.", "SSE.Controllers.Main.warnNoLicense": "Usted ha alcanzado el límite de conexiones simultáneas con %1 editores. Este documento se abrirá sólo para su visualización.<br>Contacte con el equipo de ventas de %1 para conocer los términos de actualización personal.", "SSE.Controllers.Main.warnNoLicenseUsers": "Usted ha alcanzado el límite de usuarios para los editores de %1. Contacte con el equipo de ventas de %1 para conocer los términos de actualización personal.", "SSE.Controllers.Main.warnProcessRightsChange": "Se le ha denegado el derecho a editar este archivo.", "SSE.Controllers.Print.strAllSheets": "Todas las hojas", "SSE.Controllers.Print.textFirstCol": "Primera columna", "SSE.Controllers.Print.textFirstRow": "Primera fila", "SSE.Controllers.Print.textFrozenCols": "Columnas inmovilizadas", "SSE.Controllers.Print.textFrozenRows": "<PERSON><PERSON> ", "SSE.Controllers.Print.textInvalidRange": "¡ERROR!¡Rango de celdas inválido! ", "SSE.Controllers.Print.textNoRepeat": "No repetir", "SSE.Controllers.Print.textRepeat": "Repetir...", "SSE.Controllers.Print.textSelectRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>o", "SSE.Controllers.Print.textWarning": "Aviso", "SSE.Controllers.Print.txtCustom": "Personalizado", "SSE.Controllers.Print.warnCheckMargings": "<PERSON><PERSON><PERSON><PERSON> son <PERSON>", "SSE.Controllers.Search.textInvalidRange": "¡ERROR! Rango de celdas inválido", "SSE.Controllers.Search.textNoTextFound": "No se puede encontrar los datos que usted busca. Por favor, ajuste los parámetros de búsqueda.", "SSE.Controllers.Search.textReplaceSkipped": "El reemplazo se realizó. Se omitieron {0} ocurrencias.", "SSE.Controllers.Search.textReplaceSuccess": "Se realizó la búsqueda. {0} ocurrencias se sustituyeron", "SSE.Controllers.Statusbar.errorLastSheet": "Un libro debe contener al menos una hoja visible.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Imposible borrar la hoja de cálculo.", "SSE.Controllers.Statusbar.strSheet": "Hoja", "SSE.Controllers.Statusbar.textDisconnect": "<b>Se ha perdido la conexión</b><br>Intentando conectar. Compruebe la configuración de la conexión.", "SSE.Controllers.Statusbar.textSheetViewTip": "Está en el modo Vista de Hoja. Los filtros y la ordenación son visibles solo para usted y aquellos que aún están en esta vista.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Está en el modo de vista de hoja. Los filtros sólo los puede ver usted y los que aún están en esta vista.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Las hojas de trabajo seleccionadas pueden contener datos. ¿Está seguro de que quiere proceder?", "SSE.Controllers.Statusbar.zoomText": "Zoom {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "El tipo de letra que usted va a guardar no está disponible en este dispositivo. <br>El estilo de letra se mostrará usando uno de los tipos de letra del sistema, el tipo de letra guardado va a usarse cuando esté disponible.<br>¿Desea continuar?", "SSE.Controllers.Toolbar.errorComboSeries": "Para crear un gráfico combinado, seleccione al menos dos series de datos.", "SSE.Controllers.Toolbar.errorMaxPoints": "El número máximo de puntos en serie por gráfico es 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "¡ERROR! El número máximo de series de datos por gráfico es 225", "SSE.Controllers.Toolbar.errorStockChart": "Orden de las filas incorrecto. Para crear un gráfico de cotizaciones introduzca los datos en la hoja de la forma siguiente:<br> precio de apertura, precio má<PERSON><PERSON>, precio mín<PERSON>, precio de cierre.", "SSE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textBracket": "Corchetes", "SSE.Controllers.Toolbar.textDirectional": "Direccional", "SSE.Controllers.Toolbar.textFontSizeErr": "El valor introducido es incorrecto.<br>Por favor, introduzca un valor numérico entre 1 y 409", "SSE.Controllers.Toolbar.textFraction": "Fracciones", "SSE.Controllers.Toolbar.textFunction": "Funciones", "SSE.Controllers.Toolbar.textIndicator": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.textInsert": "Insertar", "SSE.Controllers.Toolbar.textIntegral": "Integrales", "SSE.Controllers.Toolbar.textLargeOperator": "Operadores grandes", "SSE.Controllers.Toolbar.textLimitAndLog": "Límites y logaritmos ", "SSE.Controllers.Toolbar.textLongOperation": "Operación larga", "SSE.Controllers.Toolbar.textMatrix": "Matrices", "SSE.Controllers.Toolbar.textOperator": "Operadores", "SSE.Controllers.Toolbar.textPivot": "Tabla pivote", "SSE.Controllers.Toolbar.textRadical": "Radicales", "SSE.Controllers.Toolbar.textRating": "Clasificaciones", "SSE.Controllers.Toolbar.textRecentlyUsed": "Usados recientemente", "SSE.Controllers.Toolbar.textScript": "Índices", "SSE.Controllers.Toolbar.textShapes": "Formas", "SSE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textWarning": "Aviso", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Flecha derecha-izquierda superior", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Flecha superior izquierda", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Flecha superior derecha", "SSE.Controllers.Toolbar.txtAccent_Bar": "Barr<PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Barra subyacente", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Barra superpuesta", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Fórmula encuadrada (con marcador de posición)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Fórmula en<PERSON> (ejemplo)", "SSE.Controllers.Toolbar.txtAccent_Check": "Comprobar", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Llave subyacente", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Llave superpuesta", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC con barra superpuesta", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y con barra superpuesta", "SSE.Controllers.Toolbar.txtAccent_DDDot": "<PERSON><PERSON> puntos", "SSE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON> puntos", "SSE.Controllers.Toolbar.txtAccent_Dot": "Punt<PERSON>", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Barra doble superpuesta", "SSE.Controllers.Toolbar.txtAccent_Grave": "Acento grave", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Carácter de agrupación inferior", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Carácter de agrupación superior", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Arpón superior hacia la izquierda", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Arpón superior hacia la derecha", "SSE.Controllers.Toolbar.txtAccent_Hat": "Circunfle<PERSON>", "SSE.Controllers.Toolbar.txtAccent_Smile": "Acento breve", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "Corchetes angulares", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Corchetes angulares con separador", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Corchetes angulares con dos separadores", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Corchete angular de cierre", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Corchete angular de apertura", "SSE.Controllers.Toolbar.txtBracket_Curve": "Llaves", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Llaves con separador", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Llave de cierre", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Llave de apertura", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Casos (dos condiciones)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Casos (tres condiciones)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Objeto de pila", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Objeto acotado entre paréntesis", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Ejemplo de casos", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Coeficiente de binomio", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Coeficiente binomial en corchetes angulares", "SSE.Controllers.Toolbar.txtBracket_Line": "Plecas", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Pleca de cierre", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Pleca de apertura", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON> dobles", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Pleca doble de cierre", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Pleca doble de apertura", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Corchete inferior", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Corchete inferior de cierre", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Corchete inferior de apertura", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Paréntesis con separador", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Paréntesis de cierre", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Paréntesis de apertura", "SSE.Controllers.Toolbar.txtBracket_Square": "Corchetes", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Marcador de posición entre dos corchetes de cierre", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Corchetes invertidos", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Corchete de cierre", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Corchete de apertura", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Marcador de posición entre dos corchetes de apertura", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Corchetes dobles", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Corchete doble de cierre", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Corchete doble de apertura", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Corchete de techo", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Corchete de techo de cierre", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Corchete de techo de apertura", "SSE.Controllers.Toolbar.txtDeleteCells": "Eliminar celdas", "SSE.Controllers.Toolbar.txtExpand": "Expandir y ordenar", "SSE.Controllers.Toolbar.txtExpandSort": "Los datos al lado del rango seleccionado no serán ordenados. ¿Quiere Usted expandir el rango seleccionado para incluir datos de las celdas adyacentes o continuar ordenación del rango seleccionado?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Fracción sesgada", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "dx sobre dy", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Delta mayúscula y sobre delta mayúscula x", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "y parcial sobre x parcial", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Delta y sobre delta x", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Fracción lineal", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi dividir a 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Fracción pequeña", "SSE.Controllers.Toolbar.txtFractionVertical": "Fracción apilada", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Función de coseno inversa", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Función de coseno inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Función de cotangente inversa", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Función de cotangente inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Función de cosecante inversa", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Función de cosecante inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Función de secante inversa", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Función de secante inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Función de seno inversa", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Función de seno inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Función de tangente inversa", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Función de tangente inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Cos": "Función de coseno", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Función de coseno hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Cot": "Función de cotangente", "SSE.Controllers.Toolbar.txtFunction_Coth": "Función de cotangente hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Csc": "Función de cosecante", "SSE.Controllers.Toolbar.txtFunction_Csch": "Función de cosecante hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Seno zeta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Fórmula de tangente", "SSE.Controllers.Toolbar.txtFunction_Sec": "Función de secante", "SSE.Controllers.Toolbar.txtFunction_Sech": "Función de secante hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Sin": "Función de seno", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Función de seno hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Tan": "Función de tangente", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Función de tangente hiperbólica", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Personalizado", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Datos y modelo", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Correcto, incorrecto y neutro", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "Sin nombre", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Formato de número", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Estilos de celda temáticos", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Títulos y encabezados", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Personalizado", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Oscuro", "SSE.Controllers.Toolbar.txtGroupTable_Light": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Medio", "SSE.Controllers.Toolbar.txtInsertCells": "Insertar celdas", "SSE.Controllers.Toolbar.txtIntegral": "Integral", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Diferencial zeta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Diferencial x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Diferencial y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral con límites acotados", "SSE.Controllers.Toolbar.txtIntegralDouble": "Integral doble", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Integral doble con límites acotados", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Integral doble con límites", "SSE.Controllers.Toolbar.txtIntegralOriented": "Integral de contorno", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integral de contorno con límites acotados", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integral de superficie", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integral de superficie con límites acotados", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integral de superficie con límites", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integral de contorno con límites", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integral de volumen", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integral de volumen con límites acotados", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integral de volumen con límites", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integral con límites", "SSE.Controllers.Toolbar.txtIntegralTriple": "Integral triple", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Integral triple con límites acotados", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Integral triple con límites", "SSE.Controllers.Toolbar.txtInvalidRange": "¡Error! Alcance de celdas no válido", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Y lógico", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Y lógico con límite inferior", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Y lógico con límites", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Y lógico con límite inferior en subíndice", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Y lógico con límites de subíndice/supraíndice", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-producto", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Coproducto con límite inferior", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Coproducto con límites", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Coproducto con límite inferior en subíndice", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Coproducto con límites de subíndice/supraíndice", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Sumatoria sobre k de n sobre k", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Sumatoria de i igual a cero a n", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Ejemplo de suma con dos índices", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Ejemplo del producto", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Ejemplo de unión", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "O lógico", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "O lógico con límite inferior", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "O lógico con límites", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "O lógico con límite inferior en subíndice", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "O lógico con límites de subíndice/supraíndice", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersección", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersección con límite inferior", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersección con límites", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersección con límite inferior en subíndice", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersección con límites de subíndice/superíndice", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Producto", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Producto con límite inferior", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Producto con límites", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Producto con límite inferior en subíndice", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Producto con límites de subíndice/superíndice", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Sumatoria con límite inferior", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Sumatoria con límites", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Sumatoria con límite inferior en subíndice", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Sumatoria con límites de subíndice/supraíndice", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Unión", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Unión con límite inferior", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Unión con límites", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Unión con límite inferior en subíndice", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Unión con límites de subíndice/superíndice", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Ejemplo de límite", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Ejemplo de máximo", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Límite", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritmo natural", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmo", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmo", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Máximo", "SSE.Controllers.Toolbar.txtLimitLog_Min": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLockSort": "Se encuentran datos junto a su selección, pero no tiene permisos suficientes para modificar esas celdas.<br>¿Desea continuar con la selección actual?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "Matriz vacía de 1x2", "SSE.Controllers.Toolbar.txtMatrix_1_3": "Matriz vacía de 1x3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "Matriz vacía de 2x1", "SSE.Controllers.Toolbar.txtMatrix_2_2": "Matriz vacía de 2x2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matriz de 2 por 2 vacía entre plecas dobles", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Determinante de 2 por 2 vacío", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matriz de 2 por 2 vacía entre paréntesis", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Matriz de 2 por 2 vacía entre paréntesis", "SSE.Controllers.Toolbar.txtMatrix_2_3": "Matriz vacía de 2x3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "Matriz vacía de 3x1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "Matriz vacía de 3x2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "Matriz vacía de 3x3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Puntos en línea base", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Puntos en línea media", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON> diagonales", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Puntos verticales", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> dispersa entre parén<PERSON>is", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON> dispersa entre corchetes", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "Matriz de identidad de 2x2 con ceros", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Matriz de identidad de 2x2 con celdas en blanco que no están en la diagonal", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "Matriz de identidad de 3x3 con ceros", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matriz de identidad de 3x3 con celdas en blanco que no están en la diagonal", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Flecha derecha-izquierda inferior", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Flecha derecha-izquierda superior", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Flecha inferior izquierda", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Flecha superior izquierda", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Flecha inferior derecha", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Flecha superior derecha", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Dos puntos igual", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Produce", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Produce con delta", "SSE.Controllers.Toolbar.txtOperator_Definition": "Igual por definición", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta igual a", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Flecha doble inferior derecha e izquierda", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Flecha doble superior derecha e izquierda", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Flecha inferior izquierda", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Flecha superior izquierda", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Flecha inferior derecha", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Flecha superior derecha", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Igual igual", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Menos igual", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Más igual", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Unidad de medida", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Lado derecho de la fórmula cuadrática", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON> cuadrada de un cuadrado más b al cuadrado", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON> cu<PERSON> con índice", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Radical con índice", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_1": "x subíndice y al cuadrado", "SSE.Controllers.Toolbar.txtScriptCustom_2": "e elevado a menos i omega t", "SSE.Controllers.Toolbar.txtScriptCustom_3": "x al cuadrado", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Y superíndice izquierdo n subíndice izquierdo uno", "SSE.Controllers.Toolbar.txtScriptSub": "Subíndice", "SSE.Controllers.Toolbar.txtScriptSubSup": "Subíndice-Superíndice", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Subíndice-superíndice izquierdo", "SSE.Controllers.Toolbar.txtScriptSup": "Sobreíndice", "SSE.Controllers.Toolbar.txtSorting": "Ordenación", "SSE.Controllers.Toolbar.txtSortSelected": "Ordenar los objetos seleccionados", "SSE.Controllers.Toolbar.txtSymbol_about": "Aproximadamente", "SSE.Controllers.Toolbar.txtSymbol_additional": "Complemento", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "Casi igual a", "SSE.Controllers.Toolbar.txtSymbol_ast": "Operador asterisco", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Operador de viñeta", "SSE.Controllers.Toolbar.txtSymbol_cap": "Intersección", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Elipsis horizontal de línea media", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Aproximadamente igual a", "SSE.Controllers.Toolbar.txtSymbol_cup": "Unión", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Elipsis en diagonal de derecha a izquierda", "SSE.Controllers.Toolbar.txtSymbol_degree": "Grados", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Signe de division", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Flecha hacia abajo", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Conjunto vacío", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Épsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "Igual", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Idéntico a", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "Existe", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Factorial", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Grados Fahrenheit", "SSE.Controllers.Toolbar.txtSymbol_forall": "Para todos", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "Mayor o igual a", "SSE.Controllers.Toolbar.txtSymbol_gg": "Mayor que", "SSE.Controllers.Toolbar.txtSymbol_greater": "Mayor que", "SSE.Controllers.Toolbar.txtSymbol_in": "Elemento de", "SSE.Controllers.Toolbar.txtSymbol_inc": "Incremento", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Infinito", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Flecha izquierda", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Flecha izquierda-derecha", "SSE.Controllers.Toolbar.txtSymbol_leq": "<PERSON>or o igual a", "SSE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON> que", "SSE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON> que", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "Menos más", "SSE.Controllers.Toolbar.txtSymbol_mu": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "No igual a", "SSE.Controllers.Toolbar.txtSymbol_ni": "Contiene como miembro", "SSE.Controllers.Toolbar.txtSymbol_not": "Signo de negación", "SSE.Controllers.Toolbar.txtSymbol_notexists": "No existe", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_o": "Ómicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Derivada parcial", "SSE.Controllers.Toolbar.txtSymbol_percent": "Po<PERSON>entaj<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_phi": "Fi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Más", "SSE.Controllers.Toolbar.txtSymbol_pm": "<PERSON><PERSON> menos", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proporcional a", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON>a", "SSE.Controllers.Toolbar.txtSymbol_qed": "Fin de la demostración", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Elipsis en diagonal de izquierda a derecha", "SSE.Controllers.Toolbar.txtSymbol_rho": "Ro", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> derecha", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Signo de radical", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Por lo tanto ", "SSE.Controllers.Toolbar.txtSymbol_theta": "Zeta", "SSE.Controllers.Toolbar.txtSymbol_times": "Signo de multiplicación", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Flecha hacia arriba", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ípsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Épsilon (variante)", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Variante fi", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Variante pi", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Variante ro", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Variante sigma", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Variante zeta", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Elipsis vertical", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Csi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Estilo de tabla oscuro", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Estilo de tabla claro", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Estilo de tabla medio", "SSE.Controllers.Toolbar.warnLongOperation": "La operación que está a punto de realizar podría tomar mucho tiempo para completar.<br>¿Está seguro que desea continuar?", "SSE.Controllers.Toolbar.warnMergeLostData": "En la celda unida permanecerán sólo los datos de la celda de la esquina superior izquierda.<br>Est<PERSON> seguro de que quiere continuar?", "SSE.Controllers.Viewport.textFreezePanes": "In<PERSON><PERSON>zar paneles", "SSE.Controllers.Viewport.textFreezePanesShadow": "Mostrar la sombra de paneles inmovilizados", "SSE.Controllers.Viewport.textHideFBar": "Ocultar barra de fórmula<PERSON>", "SSE.Controllers.Viewport.textHideGridlines": "Ocultar cuad<PERSON><PERSON>", "SSE.Controllers.Viewport.textHideHeadings": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Separador decimal", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Separador de miles", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Ajustes utilizados para reconocer los datos numéricos", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Calificador de texto", "SSE.Views.AdvancedSeparatorDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(ninguno)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Filtro personalizado", "SSE.Views.AutoFilterDialog.textAddSelection": "Agregar la selección actual al filtro", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Blanks}", "SSE.Views.AutoFilterDialog.textSelectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Seleccionar todos los resultados de la búsqueda", "SSE.Views.AutoFilterDialog.textWarning": "Aviso", "SSE.Views.AutoFilterDialog.txtAboveAve": "Sobre la media", "SSE.Views.AutoFilterDialog.txtBegins": "Empieza con...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Por debajo de la media", "SSE.Views.AutoFilterDialog.txtBetween": "Entre...", "SSE.Views.AutoFilterDialog.txtClear": "Bo<PERSON>r", "SSE.Views.AutoFilterDialog.txtContains": "Contiene...", "SSE.Views.AutoFilterDialog.txtEmpty": "Introducir filtro para celda", "SSE.Views.AutoFilterDialog.txtEnds": "Termina en...", "SSE.Views.AutoFilterDialog.txtEquals": "Igual...", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filtrar por color de celdas", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filtrar por color de la letra", "SSE.Views.AutoFilterDialog.txtGreater": "Mayor qué...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Mayor qué o igual a...", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Filtrar de etiqueta", "SSE.Views.AutoFilterDialog.txtLess": "<PERSON>os que...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Menos que o igual a...", "SSE.Views.AutoFilterDialog.txtNotBegins": "No empieza con...", "SSE.Views.AutoFilterDialog.txtNotBetween": "No está entre...", "SSE.Views.AutoFilterDialog.txtNotContains": "No contiene...", "SSE.Views.AutoFilterDialog.txtNotEnds": "No termina en...", "SSE.Views.AutoFilterDialog.txtNotEquals": "No es igual...", "SSE.Views.AutoFilterDialog.txtNumFilter": "Número de filtro", "SSE.Views.AutoFilterDialog.txtReapply": "Reaplicar", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Ordenar por el color de celdas", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Ordenar por color de la letra", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Ordenar de mayor a menor", "SSE.Views.AutoFilterDialog.txtSortLow2High": "<PERSON><PERSON><PERSON> a mayor ", "SSE.Views.AutoFilterDialog.txtSortOption": "Más opciones de ordenación...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Filtro de Texto", "SSE.Views.AutoFilterDialog.txtTitle": "Filtro", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Filtro de valor", "SSE.Views.AutoFilterDialog.warnFilterError": "Necesita la menos un campo de en el área Valores para aplicar el filtro de valor.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Us<PERSON> debe elegir por lo menos un valor", "SSE.Views.CellEditor.textManager": "Administrador de nombre", "SSE.Views.CellEditor.tipFormula": "Insertar función", "SSE.Views.CellRangeDialog.errorMaxRows": "¡ERROR! El número máximo de series de datos por gráfico es 225", "SSE.Views.CellRangeDialog.errorStockChart": "Orden de las filas incorrecto. Para crear un gráfico de cotizaciones introduzca los datos en la hoja de la forma siguiente:<br> precio de apertura, precio má<PERSON><PERSON>, precio mín<PERSON>, precio de cierre.", "SSE.Views.CellRangeDialog.txtEmpty": "Este campo es obligatorio", "SSE.Views.CellRangeDialog.txtInvalidRange": "¡ERROR!Rango de celdas inválido", "SSE.Views.CellRangeDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> rang<PERSON> de <PERSON>", "SSE.Views.CellSettings.strShrink": "Reducir para ajustar", "SSE.Views.CellSettings.strWrap": "Ajustar texto", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "Color de fondo", "SSE.Views.CellSettings.textBackground": "Color de fondo", "SSE.Views.CellSettings.textBorderColor": "Color", "SSE.Views.CellSettings.textBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textClearRule": "<PERSON><PERSON><PERSON> reglas", "SSE.Views.CellSettings.textColor": "Color de relleno", "SSE.Views.CellSettings.textColorScales": "Escalas de color", "SSE.Views.CellSettings.textCondFormat": "Formato condicional", "SSE.Views.CellSettings.textControl": "Control del texto", "SSE.Views.CellSettings.textDataBars": "Barras de datos", "SSE.Views.CellSettings.textDirection": "Dirección ", "SSE.Views.CellSettings.textFill": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textForeground": "Color de primer plano", "SSE.Views.CellSettings.textGradient": "Puntos de gradiente", "SSE.Views.CellSettings.textGradientColor": "Color", "SSE.Views.CellSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textIndent": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textItems": "Elementos", "SSE.Views.CellSettings.textLinear": "Lineal", "SSE.Views.CellSettings.textManageRule": "Admini<PERSON><PERSON> reglas", "SSE.Views.CellSettings.textNewRule": "Nueva regla", "SSE.Views.CellSettings.textNoFill": "<PERSON> relleno", "SSE.Views.CellSettings.textOrientation": "Orientación del texto", "SSE.Views.CellSettings.textPattern": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textPosition": "Posición", "SSE.Views.CellSettings.textRadial": "Radial", "SSE.Views.CellSettings.textSelectBorders": "Seleccione bordes que desea cambiar aplicando estilo seleccionado", "SSE.Views.CellSettings.textSelection": "Desde la selección actual", "SSE.Views.CellSettings.textThisPivot": "Desde esta tabla pivote", "SSE.Views.CellSettings.textThisSheet": "Desde esta hoja de cálculo", "SSE.Views.CellSettings.textThisTable": "Desde esta tabla", "SSE.Views.CellSettings.tipAddGradientPoint": "Agregar punto de degradado", "SSE.Views.CellSettings.tipAll": "Establecer borde exterior y todas las líneas interiores ", "SSE.Views.CellSettings.tipBottom": "E<PERSON><PERSON> s<PERSON>lo borde exterior inferior", "SSE.Views.CellSettings.tipDiagD": "<PERSON><PERSON><PERSON> borde diagonal abajo", "SSE.Views.CellSettings.tipDiagU": "<PERSON><PERSON><PERSON> borde diagonal hacia arriba", "SSE.Views.CellSettings.tipInner": "Establecer s<PERSON> l<PERSON> interiores", "SSE.Views.CellSettings.tipInnerHor": "Establecer sólo líneas horizontales interiores ", "SSE.Views.CellSettings.tipInnerVert": "Establecer sólo líneas verticales interiores", "SSE.Views.CellSettings.tipLeft": "<PERSON><PERSON><PERSON> s<PERSON> borde <PERSON> izquierdo", "SSE.Views.CellSettings.tipNone": "No establecer bordes", "SSE.Views.CellSettings.tipOuter": "<PERSON><PERSON><PERSON> s<PERSON> borde <PERSON>", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Eliminar gradiente de punto", "SSE.Views.CellSettings.tipRight": "<PERSON><PERSON><PERSON> s<PERSON>lo borde exterior derecho", "SSE.Views.CellSettings.tipTop": "Establecer s<PERSON><PERSON> borde exterior superior", "SSE.Views.ChartDataDialog.errorInFormula": "Hay un error en la fórmula ingresada.", "SSE.Views.ChartDataDialog.errorInvalidReference": "La referencia no es válida. Debe hacer referencia a una hoja de trabajo abierta.", "SSE.Views.ChartDataDialog.errorMaxPoints": "El número máximo de puntos en serie por gráfico es 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "El número máximo de serie de datos por tabla es 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "La referencia no es válida. Las referencias a títulos, valores, tamaños, o etiquetas de datos deben ser una sola celda, fila o columna.", "SSE.Views.ChartDataDialog.errorNoValues": "Para crear un gráfico, las series deben contener al menos un valor.", "SSE.Views.ChartDataDialog.errorStockChart": "Orden de las filas incorrecto. Para crear un gráfico de cotizaciones introduzca los datos en la hoja en el siguiente orden: precio de apertura, precio máxi<PERSON>, precio mín<PERSON>, precio de cierre.", "SSE.Views.ChartDataDialog.textAdd": "Agregar", "SSE.Views.ChartDataDialog.textCategory": "Etiquetas del eje horizontal (categoría)", "SSE.Views.ChartDataDialog.textData": "Rango de datos del gráfico", "SSE.Views.ChartDataDialog.textDelete": "Eliminar", "SSE.Views.ChartDataDialog.textDown": "Abajo", "SSE.Views.ChartDataDialog.textEdit": "<PERSON><PERSON>", "SSE.Views.ChartDataDialog.textInvalidRange": "<PERSON>ngo de celdas no válido", "SSE.Views.ChartDataDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textSeries": "Entradas de leyenda (series)", "SSE.Views.ChartDataDialog.textSwitch": "Cambiar fila/columna", "SSE.Views.ChartDataDialog.textTitle": "Datos del gráfico", "SSE.Views.ChartDataDialog.textUp": "Arriba", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Hay un error en la fórmula ingresada.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "La referencia no es válida. Debe hacer referencia a una hoja de trabajo abierta.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "El número máximo de puntos en serie por gráfico es 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "El número máximo de serie de datos por tabla es 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "La referencia no es válida. Las referencias a títulos, valores, tamaños, o etiquetas de datos deben ser una sola celda, fila o columna.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Para crear un gráfico, las series deben contener al menos un valor.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Orden de las filas incorrecto. Para crear un gráfico de cotizaciones introduzca los datos en la hoja en el orden siguiente: precio de apertura, precio máxi<PERSON>, precio mín<PERSON>, precio de cierre.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "<PERSON>ngo de celdas no válido", "SSE.Views.ChartDataRangeDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Rango de etiqueta de eje", "SSE.Views.ChartDataRangeDialog.txtChoose": "<PERSON><PERSON><PERSON>o", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Nombre de serie", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Etiquetas de eje", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Editar series", "SSE.Views.ChartDataRangeDialog.txtValues": "Valores", "SSE.Views.ChartDataRangeDialog.txtXValues": "Valores X", "SSE.Views.ChartDataRangeDialog.txtYValues": "Valores Y", "SSE.Views.ChartSettings.errorMaxRows": "El número máximo de series de datos por gráfico es de 255.", "SSE.Views.ChartSettings.strLineWeight": "Grosor de línea", "SSE.Views.ChartSettings.strSparkColor": "Color", "SSE.Views.ChartSettings.strTemplate": "Plantilla", "SSE.Views.ChartSettings.text3dDepth": "Profundidad (% de la base)", "SSE.Views.ChartSettings.text3dHeight": "Altura (% de la base)", "SSE.Views.ChartSettings.text3dRotation": "Rotación 3D", "SSE.Views.ChartSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "SSE.Views.ChartSettings.textAutoscale": "Escalado automático", "SSE.Views.ChartSettings.textBorderSizeErr": "El valor numérico es incorrecto.<br><PERSON>r favor, introduzca un valor de 0 a 1584 puntos.", "SSE.Views.ChartSettings.textChangeType": "Cambiar tipo", "SSE.Views.ChartSettings.textChartType": "Cambiar tipo de gráfico", "SSE.Views.ChartSettings.textDefault": "Rotación por defecto", "SSE.Views.ChartSettings.textDown": "Abajo", "SSE.Views.ChartSettings.textEditData": "Editar datos y ubicación", "SSE.Views.ChartSettings.textFirstPoint": "Primer punto", "SSE.Views.ChartSettings.textHeight": "Altura", "SSE.Views.ChartSettings.textHighPoint": "Punto alto", "SSE.Views.ChartSettings.textKeepRatio": "Proporciones constantes", "SSE.Views.ChartSettings.textLastPoint": "<PERSON><PERSON><PERSON>o", "SSE.Views.ChartSettings.textLeft": "Iz<PERSON>erda", "SSE.Views.ChartSettings.textLowPoint": "<PERSON>unto bajo", "SSE.Views.ChartSettings.textMarkers": "Marcadores", "SSE.Views.ChartSettings.textNarrow": "Campo de visión estrecho", "SSE.Views.ChartSettings.textNegativePoint": "Punto negativo", "SSE.Views.ChartSettings.textPerspective": "Perspectiva", "SSE.Views.ChartSettings.textRanges": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textRight": "Derecha", "SSE.Views.ChartSettings.textRightAngle": "Ejes en ángulo recto", "SSE.Views.ChartSettings.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textShow": "Mostrar", "SSE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSwitch": "Cambiar Fila/Columna", "SSE.Views.ChartSettings.textType": "Tipo", "SSE.Views.ChartSettings.textUp": "Arriba", "SSE.Views.ChartSettings.textWiden": "Campo de visión ancho", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textX": "Rotación X", "SSE.Views.ChartSettings.textY": "Rotación Y", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "¡ERROR! El máximo", "SSE.Views.ChartSettingsDlg.errorMaxRows": "¡ERROR! El número máximo de series de datos por gráfico es 225", "SSE.Views.ChartSettingsDlg.errorStockChart": "Orden de las filas incorrecto. Para compilar un gráfico de cotizaciones introduzca los datos en la hoja  de la forma siguiente:<br> precio de apertura, precio máxi<PERSON>, precio mín<PERSON>, precio de cierre.", "SSE.Views.ChartSettingsDlg.textAbsolute": "No mover, ni cambiar tamaño con celdas", "SSE.Views.ChartSettingsDlg.textAlt": "Texto alternativo", "SSE.Views.ChartSettingsDlg.textAltDescription": "Descripción", "SSE.Views.ChartSettingsDlg.textAltTip": "Representación de texto alternativa de la información sobre el objeto visual, que se leerá para las personas con deficiencia visual o deterioro cognitivo para ayudarles a entender mejor, que información contiene la imagen, autoforma, gráfica o tabla.", "SSE.Views.ChartSettingsDlg.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAuto": "Auto", "SSE.Views.ChartSettingsDlg.textAutoEach": "Automático para cada", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Intersección con el eje", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Parámetros de eje", "SSE.Views.ChartSettingsDlg.textAxisPos": "Posición de eje", "SSE.Views.ChartSettingsDlg.textAxisSettings": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>je", "SSE.Views.ChartSettingsDlg.textAxisTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBase": "Base", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Entre marcas de graduación", "SSE.Views.ChartSettingsDlg.textBillions": "Millardos", "SSE.Views.ChartSettingsDlg.textBottom": "Abajo ", "SSE.Views.ChartSettingsDlg.textCategoryName": "Nombre de categoría", "SSE.Views.ChartSettingsDlg.textCenter": "Al centro", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Elementos de gráfico y <br>leyenda de gráfico", "SSE.Views.ChartSettingsDlg.textChartTitle": "Título del gráfico", "SSE.Views.ChartSettingsDlg.textCross": "Intersección", "SSE.Views.ChartSettingsDlg.textCustom": "Personalizado", "SSE.Views.ChartSettingsDlg.textDataColumns": "en columnas", "SSE.Views.ChartSettingsDlg.textDataLabels": "Etiquetas de datos", "SSE.Views.ChartSettingsDlg.textDataRows": "en filas", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Mostrar leyenda", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Celdas ocultas y vacías", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Conectar puntos de datos con líneas", "SSE.Views.ChartSettingsDlg.textFit": "Ajustar al ancho", "SSE.Views.ChartSettingsDlg.textFixed": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textFormat": "Formato de etiqueta", "SSE.Views.ChartSettingsDlg.textGaps": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGridLines": "Líneas de cuadrícula", "SSE.Views.ChartSettingsDlg.textGroup": "Agrupar minigráficos", "SSE.Views.ChartSettingsDlg.textHide": "Ocultar", "SSE.Views.ChartSettingsDlg.textHideAxis": "O<PERSON>ltar eje", "SSE.Views.ChartSettingsDlg.textHigh": "Alto", "SSE.Views.ChartSettingsDlg.textHorAxis": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Eje horizontal secundario", "SSE.Views.ChartSettingsDlg.textHorizontal": "Horizontal ", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Cientos", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "En", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Abajo en el interior", "SSE.Views.ChartSettingsDlg.textInnerTop": "Arriba en el interior", "SSE.Views.ChartSettingsDlg.textInvalidRange": "¡ERROR!¡Rango de celdas inválido! ", "SSE.Views.ChartSettingsDlg.textLabelDist": "Distancia entre eje y etiqueta", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Intervalo entre etiquetas", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Parámetros de etiqueta", "SSE.Views.ChartSettingsDlg.textLabelPos": "Posición de etiqueta", "SSE.Views.ChartSettingsDlg.textLayout": "Diseño", "SSE.Views.ChartSettingsDlg.textLeft": "A la izquierda", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Superposición a la izquierda", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Inferior", "SSE.Views.ChartSettingsDlg.textLegendLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendPos": "Leyenda", "SSE.Views.ChartSettingsDlg.textLegendRight": "Derecho", "SSE.Views.ChartSettingsDlg.textLegendTop": "Superior", "SSE.Views.ChartSettingsDlg.textLines": "Líneas", "SSE.Views.ChartSettingsDlg.textLocationRange": "Rango de ubicación", "SSE.Views.ChartSettingsDlg.textLogScale": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLow": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "Principal", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Principal y menor", "SSE.Views.ChartSettingsDlg.textMajorType": "Tipo principal", "SSE.Views.ChartSettingsDlg.textManual": "Manualmente", "SSE.Views.ChartSettingsDlg.textMarkers": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Intervalo entre marcas", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON>or máxi<PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinorType": "<PERSON>ipo menor", "SSE.Views.ChartSettingsDlg.textMinValue": "<PERSON><PERSON> m<PERSON>", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Al lado de eje", "SSE.Views.ChartSettingsDlg.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Sin superposición", "SSE.Views.ChartSettingsDlg.textOneCell": "Mover sin cambiar tamaño con celdas", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Marcas de graduación", "SSE.Views.ChartSettingsDlg.textOut": "Fuera", "SSE.Views.ChartSettingsDlg.textOuterTop": "Arriba en el exterior", "SSE.Views.ChartSettingsDlg.textOverlay": "Superposición", "SSE.Views.ChartSettingsDlg.textReverse": "Valores en orden inverso", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Orden inverso", "SSE.Views.ChartSettingsDlg.textRight": "Derecho", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Superposición a la derecha", "SSE.Views.ChartSettingsDlg.textRotated": "Girado", "SSE.Views.ChartSettingsDlg.textSameAll": "Lo mismo para todo", "SSE.Views.ChartSettingsDlg.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSeparator": "Separador de etiquetas de datos", "SSE.Views.ChartSettingsDlg.textSeriesName": "Nombre de serie", "SSE.Views.ChartSettingsDlg.textShow": "Mostrar", "SSE.Views.ChartSettingsDlg.textShowBorders": "<PERSON><PERSON> bordes", "SSE.Views.ChartSettingsDlg.textShowData": "Mostrar datos en filas y columnas ocultas", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Mostrar celdas vacías como", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON> eje", "SSE.Views.ChartSettingsDlg.textShowValues": "Mostrar los valores del gráfico", "SSE.Views.ChartSettingsDlg.textSingle": "Minigráfico único", "SSE.Views.ChartSettingsDlg.textSmooth": "Suave", "SSE.Views.ChartSettingsDlg.textSnap": "Ajustar a la celda", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Rangos de minigráfico", "SSE.Views.ChartSettingsDlg.textStraight": "Recto", "SSE.Views.ChartSettingsDlg.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "Opciones de Marcadores", "SSE.Views.ChartSettingsDlg.textTitle": "Gráfico - Ajustes <PERSON>", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Minigráfico - Ajustes <PERSON>", "SSE.Views.ChartSettingsDlg.textTop": "Top", "SSE.Views.ChartSettingsDlg.textTrillions": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTwoCell": "Mover y cambiar tamaño con celdas", "SSE.Views.ChartSettingsDlg.textType": "Tipo", "SSE.Views.ChartSettingsDlg.textTypeData": "Tipo y datos", "SSE.Views.ChartSettingsDlg.textUnits": "Unidades de visualización", "SSE.Views.ChartSettingsDlg.textValue": "Valor", "SSE.Views.ChartSettingsDlg.textVertAxis": "<PERSON><PERSON> vertical", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Eje vertical secundario", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Título del eje X", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "T<PERSON><PERSON>lo del eje Y", "SSE.Views.ChartSettingsDlg.textZero": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.txtEmpty": "Este campo es obligatorio", "SSE.Views.ChartTypeDialog.errorComboSeries": "Para crear un gráfico combinado, seleccione al menos dos series de datos.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "El tipo de gráfico seleccionado requiere el eje secundario que está utilizando un gráfico existente. Seleccione otro tipo de gráfico.", "SSE.Views.ChartTypeDialog.textSecondary": "<PERSON><PERSON> secundario", "SSE.Views.ChartTypeDialog.textSeries": "Serie", "SSE.Views.ChartTypeDialog.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textTitle": "Tipo del gráfico", "SSE.Views.ChartTypeDialog.textType": "Tipo", "SSE.Views.CreatePivotDialog.textDataRange": "<PERSON><PERSON> de da<PERSON> de origen", "SSE.Views.CreatePivotDialog.textDestination": "Elegir dónde colocar la tabla", "SSE.Views.CreatePivotDialog.textExist": "Hoja de cálculo existente", "SSE.Views.CreatePivotDialog.textInvalidRange": "<PERSON><PERSON> de celdas inválido", "SSE.Views.CreatePivotDialog.textNew": "Hoja de cálculo nueva", "SSE.Views.CreatePivotDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CreatePivotDialog.textTitle": "C<PERSON>r tabla dinámica", "SSE.Views.CreatePivotDialog.txtEmpty": "Este campo es obligatorio", "SSE.Views.CreateSparklineDialog.textDataRange": "<PERSON><PERSON> de da<PERSON> de origen", "SSE.Views.CreateSparklineDialog.textDestination": "Elija dónde colocar los minigráficos", "SSE.Views.CreateSparklineDialog.textInvalidRange": "<PERSON><PERSON> de celdas inválido", "SSE.Views.CreateSparklineDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CreateSparklineDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.CreateSparklineDialog.txtEmpty": "Este campo es obligatorio", "SSE.Views.DataTab.capBtnGroup": "Agrupar", "SSE.Views.DataTab.capBtnTextCustomSort": "Orden personalizado", "SSE.Views.DataTab.capBtnTextDataValidation": "Validación de datos", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Eliminar duplicados", "SSE.Views.DataTab.capBtnTextToCol": "Texto en columnas", "SSE.Views.DataTab.capBtnUngroup": "Desagrupar", "SSE.Views.DataTab.capDataExternalLinks": "Enlaces externos", "SSE.Views.DataTab.capDataFromText": "Obtener datos", "SSE.Views.DataTab.mniFromFile": "Desde archivo TXT/CSV local", "SSE.Views.DataTab.mniFromUrl": "Desde la dirección web del archivo TXT/CSV", "SSE.Views.DataTab.mniFromXMLFile": "Desde XML local", "SSE.Views.DataTab.textBelow": "Filas resumen debajo del detalle", "SSE.Views.DataTab.textClear": "<PERSON><PERSON><PERSON> esque<PERSON>", "SSE.Views.DataTab.textColumns": "Desagrupar columnas", "SSE.Views.DataTab.textGroupColumns": "Agrupar columnas", "SSE.Views.DataTab.textGroupRows": "Agrupar filas", "SSE.Views.DataTab.textRightOf": "Columnas resumen a la derecha del detalle", "SSE.Views.DataTab.textRows": "Desagrupar filas", "SSE.Views.DataTab.tipCustomSort": "Orden personalizado", "SSE.Views.DataTab.tipDataFromText": "Obtener datos de archivo", "SSE.Views.DataTab.tipDataValidation": "Validación de datos", "SSE.Views.DataTab.tipExternalLinks": "Ver otros archivos a los que está vinculada esta hoja de cálculo", "SSE.Views.DataTab.tipGroup": "Agrupar rango de celdas", "SSE.Views.DataTab.tipRemDuplicates": "Eliminar filas duplicadas de la hoja", "SSE.Views.DataTab.tipToColumns": "Dividir texto de celda en columnas", "SSE.Views.DataTab.tipUngroup": "Desagrupar rango de celdas", "SSE.Views.DataValidationDialog.errorFormula": "El valor actual es un error. ¿Desea continuar?", "SSE.Views.DataValidationDialog.errorInvalid": "El valor introducido en el campo \"{0}\" no es válido.", "SSE.Views.DataValidationDialog.errorInvalidDate": "La fecha introducida en el campo \"{0}\" no es válida.", "SSE.Views.DataValidationDialog.errorInvalidList": "El origen de la lista debe ser una lista delimitada o una referencia a una sola fila o columna.", "SSE.Views.DataValidationDialog.errorInvalidTime": "La hora introducida en el campo \"{0}\" no es válida.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "El campo \"{1}\" debe ser mayor que o igual al campo \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Debe introducir un valor tanto en el campo \"{0}\" como en el campo \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Debe introducir un valor en el campo \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "No se puede encontrar uno de los rangos especificados.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Los valores negativos no pueden utilizarse en las condiciones \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "El campo \"{0}\" debe ser un valor numérico, una expresión numérica o referirse a una celda que contenga un valor numérico.", "SSE.Views.DataValidationDialog.strError": "<PERSON><PERSON><PERSON> de <PERSON>", "SSE.Views.DataValidationDialog.strInput": "Mensaje de entrada", "SSE.Views.DataValidationDialog.strSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textApply": "Aplicar estos cambios a todas las demás celdas con los mismos ajustes", "SSE.Views.DataValidationDialog.textCellSelected": "<PERSON>uando la celda está seleccionada, mostrar este mensaje de entrada", "SSE.Views.DataValidationDialog.textCompare": "Comparar con", "SSE.Views.DataValidationDialog.textData": "Datos", "SSE.Views.DataValidationDialog.textEndDate": "Fecha final", "SSE.Views.DataValidationDialog.textEndTime": "Hora final", "SSE.Views.DataValidationDialog.textError": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textIgnore": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textInput": "Mensaje de entrada", "SSE.Views.DataValidationDialog.textMax": "Máximo", "SSE.Views.DataValidationDialog.textMessage": "Men<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textShowDropDown": "Mostrar la lista desplegable en la celda", "SSE.Views.DataValidationDialog.textShowError": "Mostrar la alerta de error después de la introducción de datos no válidos", "SSE.Views.DataValidationDialog.textShowInput": "Mostrar el mensaje de entrada cuando la celda está seleccionada", "SSE.Views.DataValidationDialog.textSource": "Fuente", "SSE.Views.DataValidationDialog.textStartDate": "Fecha de inicio", "SSE.Views.DataValidationDialog.textStartTime": "Hora de inicio", "SSE.Views.DataValidationDialog.textStop": "Detener", "SSE.Views.DataValidationDialog.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textUserEnters": "Cuando el usuario introduce datos no válidos, mostrar esta alerta de error", "SSE.Views.DataValidationDialog.txtAny": "<PERSON><PERSON>quier valor", "SSE.Views.DataValidationDialog.txtBetween": "entre", "SSE.Views.DataValidationDialog.txtDate": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtDecimal": "Decimal", "SSE.Views.DataValidationDialog.txtElTime": "Tiempo transcurrido", "SSE.Views.DataValidationDialog.txtEndDate": "Fecha final", "SSE.Views.DataValidationDialog.txtEndTime": "Hora final", "SSE.Views.DataValidationDialog.txtEqual": "es igual a", "SSE.Views.DataValidationDialog.txtGreaterThan": "mayor que", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "mayor que o igual a", "SSE.Views.DataValidationDialog.txtLength": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtLessThan": "menor que", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "menor que o igual a", "SSE.Views.DataValidationDialog.txtList": "Lista", "SSE.Views.DataValidationDialog.txtNotBetween": "no está entre", "SSE.Views.DataValidationDialog.txtNotEqual": "no es igual a", "SSE.Views.DataValidationDialog.txtOther": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtStartDate": "Fecha de inicio", "SSE.Views.DataValidationDialog.txtStartTime": "Hora de inicio", "SSE.Views.DataValidationDialog.txtTextLength": "Longitud del texto", "SSE.Views.DataValidationDialog.txtTime": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtWhole": "Número entero", "SSE.Views.DigitalFilterDialog.capAnd": "Y", "SSE.Views.DigitalFilterDialog.capCondition1": "iguales", "SSE.Views.DigitalFilterDialog.capCondition10": "no termina con", "SSE.Views.DigitalFilterDialog.capCondition11": "contiene", "SSE.Views.DigitalFilterDialog.capCondition12": "no contiene", "SSE.Views.DigitalFilterDialog.capCondition2": "no es igual", "SSE.Views.DigitalFilterDialog.capCondition3": "es más grande que", "SSE.Views.DigitalFilterDialog.capCondition4": "es más grande o igual a ", "SSE.Views.DigitalFilterDialog.capCondition5": "es menor que", "SSE.Views.DigitalFilterDialog.capCondition6": "es menor o igual a ", "SSE.Views.DigitalFilterDialog.capCondition7": "empieza con", "SSE.Views.DigitalFilterDialog.capCondition8": "no empieza con", "SSE.Views.DigitalFilterDialog.capCondition9": "termina con", "SSE.Views.DigitalFilterDialog.capOr": "O", "SSE.Views.DigitalFilterDialog.textNoFilter": "sin filtro", "SSE.Views.DigitalFilterDialog.textShowRows": "Mostrar filas donde", "SSE.Views.DigitalFilterDialog.textUse1": "Use ? para presentar un caracter", "SSE.Views.DigitalFilterDialog.textUse2": "Use *  para presentar una serie de caracteres", "SSE.Views.DigitalFilterDialog.txtTitle": "Filtro personalizado", "SSE.Views.DocumentHolder.advancedEquationText": "Ajustes de la ecuación", "SSE.Views.DocumentHolder.advancedImgText": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> imagen", "SSE.Views.DocumentHolder.advancedShapeText": "<PERSON><PERSON><PERSON>s a<PERSON> de forma", "SSE.Views.DocumentHolder.advancedSlicerText": "Ajustes avanzados de segmentación de datos ", "SSE.Views.DocumentHolder.allLinearText": "Lineal (todos)", "SSE.Views.DocumentHolder.allProfText": "Profesional (todos)", "SSE.Views.DocumentHolder.bottomCellText": "Alinear en la parte inferior", "SSE.Views.DocumentHolder.bulletsText": "Viñetas y numeración", "SSE.Views.DocumentHolder.centerCellText": "Alinear al centro", "SSE.Views.DocumentHolder.chartDataText": "Seleccionar datos del gráfico", "SSE.Views.DocumentHolder.chartText": "<PERSON><PERSON><PERSON>s a<PERSON> de gráfico", "SSE.Views.DocumentHolder.chartTypeText": "Cambiar tipo de gráfico", "SSE.Views.DocumentHolder.currLinearText": "Lineal (actual)", "SSE.Views.DocumentHolder.currProfText": "Profesional (actual)", "SSE.Views.DocumentHolder.deleteColumnText": "Columna", "SSE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteTableText": "Tabla", "SSE.Views.DocumentHolder.direct270Text": "<PERSON><PERSON>r texto hacia arriba", "SSE.Views.DocumentHolder.direct90Text": "<PERSON><PERSON><PERSON> texto hacia abajo", "SSE.Views.DocumentHolder.directHText": "Horizontal ", "SSE.Views.DocumentHolder.directionText": "Dirección de texto", "SSE.Views.DocumentHolder.editChartText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.insertColumnLeftText": "Columna i<PERSON>erda", "SSE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON>na derecha", "SSE.Views.DocumentHolder.insertRowAboveText": "Fila de arriba", "SSE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectColumnText": "Toda la columna", "SSE.Views.DocumentHolder.selectDataText": "Datos de columna", "SSE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.selectTableText": "Tabla", "SSE.Views.DocumentHolder.strDelete": "Elimine la firma", "SSE.Views.DocumentHolder.strDetails": "Detalles de la firma", "SSE.Views.DocumentHolder.strSetup": "Preparación de la firma", "SSE.Views.DocumentHolder.strSign": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textAlign": "Alinear", "SSE.Views.DocumentHolder.textArrange": "Organizar", "SSE.Views.DocumentHolder.textArrangeBack": "Enviar al fondo", "SSE.Views.DocumentHolder.textArrangeBackward": "Enviar atrás", "SSE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON><PERSON> adelante", "SSE.Views.DocumentHolder.textArrangeFront": "Traer al primer plano", "SSE.Views.DocumentHolder.textAverage": "Promedio", "SSE.Views.DocumentHolder.textBullets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCount": "Contar", "SSE.Views.DocumentHolder.textCrop": "Recortar", "SSE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFit": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textEditPoints": "Modificar puntos", "SSE.Views.DocumentHolder.textEntriesList": "Seleccionar de la lista desplegable", "SSE.Views.DocumentHolder.textFlipH": "Voltear horizontalmente", "SSE.Views.DocumentHolder.textFlipV": "Voltear verticalmente", "SSE.Views.DocumentHolder.textFreezePanes": "In<PERSON><PERSON>zar paneles", "SSE.Views.DocumentHolder.textFromFile": "De archivo", "SSE.Views.DocumentHolder.textFromStorage": "Desde almacenamiento", "SSE.Views.DocumentHolder.textFromUrl": "De URL", "SSE.Views.DocumentHolder.textListSettings": "Ajustes de lista", "SSE.Views.DocumentHolder.textMacro": "Asignar macro", "SSE.Views.DocumentHolder.textMax": "Máx.", "SSE.Views.DocumentHolder.textMin": "<PERSON><PERSON>.", "SSE.Views.DocumentHolder.textMore": "Más funciones", "SSE.Views.DocumentHolder.textMoreFormats": "Otros formatos", "SSE.Views.DocumentHolder.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textNumbering": "Numeración", "SSE.Views.DocumentHolder.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON> imagen", "SSE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Girar 90° a la izquierda", "SSE.Views.DocumentHolder.textRotate90": "Girar 90° a la derecha", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Alinear en la parte inferior", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Alinear al centro", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Alinear a la izquierda", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Alinear al centro", "SSE.Views.DocumentHolder.textShapeAlignRight": "Alinear a la derecha", "SSE.Views.DocumentHolder.textShapeAlignTop": "Alinear en la parte superior", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "Viñetas de flecha", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Viñetas de marca de verificación", "SSE.Views.DocumentHolder.tipMarkersDash": "Viñetas g<PERSON>", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersFRound": "Viñetas redondas rellenas", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Viñetas cuadradas rellenas", "SSE.Views.DocumentHolder.tipMarkersHRound": "Viñetas redondas huecas", "SSE.Views.DocumentHolder.tipMarkersStar": "Viñetas de estrella", "SSE.Views.DocumentHolder.topCellText": "Alinear en la parte superior", "SSE.Views.DocumentHolder.txtAccounting": "Contabilidad", "SSE.Views.DocumentHolder.txtAddComment": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAddNamedRange": "Definir nombre", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "Ascendente", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Autoajustar ancho de <PERSON>a", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Autoajustar alto de fila", "SSE.Views.DocumentHolder.txtClear": "Limpiar", "SSE.Views.DocumentHolder.txtClearAll": "Todo", "SSE.Views.DocumentHolder.txtClearComments": "Comentarios", "SSE.Views.DocumentHolder.txtClearFormat": "Formato", "SSE.Views.DocumentHolder.txtClearHyper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Borrar grupos de minigráficos seleccionados", "SSE.Views.DocumentHolder.txtClearSparklines": "Borrar minigráficos seleccionados", "SSE.Views.DocumentHolder.txtClearText": "Texto", "SSE.Views.DocumentHolder.txtColumn": "Toda la columna", "SSE.Views.DocumentHolder.txtColumnWidth": "Ajustar ancho de <PERSON>a", "SSE.Views.DocumentHolder.txtCondFormat": "Formato condicional", "SSE.Views.DocumentHolder.txtCopy": "Copiar", "SSE.Views.DocumentHolder.txtCurrency": "Moneda", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "<PERSON><PERSON> de columna personalizado", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Altura de fila personalizada", "SSE.Views.DocumentHolder.txtCustomSort": "Orden personalizado", "SSE.Views.DocumentHolder.txtCut": "Cortar", "SSE.Views.DocumentHolder.txtDate": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDelete": "Bo<PERSON>r", "SSE.Views.DocumentHolder.txtDescending": "Descendente", "SSE.Views.DocumentHolder.txtDistribHor": "Distribuir horizontalmente", "SSE.Views.DocumentHolder.txtDistribVert": "Distribuir verticalmente", "SSE.Views.DocumentHolder.txtEditComment": "<PERSON><PERSON> comenta<PERSON>", "SSE.Views.DocumentHolder.txtFilter": "Filtro", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filtrar por color de celda", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filtrar por color de la letra", "SSE.Views.DocumentHolder.txtFilterValue": "Filtrar por valor de celda seleccionado", "SSE.Views.DocumentHolder.txtFormula": "Insertar función", "SSE.Views.DocumentHolder.txtFraction": "Fracción", "SSE.Views.DocumentHolder.txtGeneral": "General", "SSE.Views.DocumentHolder.txtGetLink": "Obtener el vínculo a este rango", "SSE.Views.DocumentHolder.txtGroup": "Agrupar", "SSE.Views.DocumentHolder.txtHide": "Ocultar", "SSE.Views.DocumentHolder.txtInsert": "Insertar", "SSE.Views.DocumentHolder.txtInsHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtNumber": "Número", "SSE.Views.DocumentHolder.txtNumFormat": "Formato de número", "SSE.Views.DocumentHolder.txtPaste": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtPercentage": "Po<PERSON>entaj<PERSON>", "SSE.Views.DocumentHolder.txtReapply": "Reaplicar", "SSE.Views.DocumentHolder.txtRefresh": "Actualizar", "SSE.Views.DocumentHolder.txtRow": "Toda la fila", "SSE.Views.DocumentHolder.txtRowHeight": "Ajustar altura de fila", "SSE.Views.DocumentHolder.txtScientific": "Scientífico", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON><PERSON><PERSON> celdas hacia abajo", "SSE.Views.DocumentHolder.txtShiftLeft": "Desplazar celdas a la izquierda", "SSE.Views.DocumentHolder.txtShiftRight": "Desp<PERSON>zar celdas a la derecha", "SSE.Views.DocumentHolder.txtShiftUp": "<PERSON><PERSON><PERSON><PERSON> celdas hacia arriba", "SSE.Views.DocumentHolder.txtShow": "Mostrar", "SSE.Views.DocumentHolder.txtShowComment": "Mostrar comentario", "SSE.Views.DocumentHolder.txtSort": "Ordenar", "SSE.Views.DocumentHolder.txtSortCellColor": "Superponer color de celda seleccionado", "SSE.Views.DocumentHolder.txtSortFontColor": "Superponer color de fuente seleccionado", "SSE.Views.DocumentHolder.txtSparklines": "Sparklines", "SSE.Views.DocumentHolder.txtText": "Texto", "SSE.Views.DocumentHolder.txtTextAdvanced": "<PERSON><PERSON><PERSON>s a<PERSON> párrafo", "SSE.Views.DocumentHolder.txtTime": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtUngroup": "Desagrupar", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "Alineación vertical", "SSE.Views.ExternalLinksDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textDelete": "<PERSON><PERSON><PERSON> enlaces", "SSE.Views.ExternalLinksDlg.textDeleteAll": "<PERSON><PERSON><PERSON> todos los enlaces", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textSource": "Origen", "SSE.Views.ExternalLinksDlg.textStatus": "Estado", "SSE.Views.ExternalLinksDlg.textUnknown": "Desconocido", "SSE.Views.ExternalLinksDlg.textUpdate": "Actualizar valores", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Actualizar todo", "SSE.Views.ExternalLinksDlg.textUpdating": "Actualizando...", "SSE.Views.ExternalLinksDlg.txtTitle": "Enlaces externos", "SSE.Views.FieldSettingsDialog.strLayout": "Diseño", "SSE.Views.FieldSettingsDialog.strSubtotals": "Subtotales", "SSE.Views.FieldSettingsDialog.textReport": "Formulario de informe", "SSE.Views.FieldSettingsDialog.textTitle": "Ajustes de campo", "SSE.Views.FieldSettingsDialog.txtAverage": "Promedio", "SSE.Views.FieldSettingsDialog.txtBlank": "Insertar filas en blanco después de cada elemento", "SSE.Views.FieldSettingsDialog.txtBottom": "Mostrar en la parte inferior del grupo", "SSE.Views.FieldSettingsDialog.txtCompact": "Compactar", "SSE.Views.FieldSettingsDialog.txtCount": "Contar", "SSE.Views.FieldSettingsDialog.txtCountNums": "Contar nú<PERSON>", "SSE.Views.FieldSettingsDialog.txtCustomName": "Nombre personalizado", "SSE.Views.FieldSettingsDialog.txtEmpty": "Mostrar elementos sin datos", "SSE.Views.FieldSettingsDialog.txtMax": "Máx.", "SSE.Views.FieldSettingsDialog.txtMin": "<PERSON><PERSON>.", "SSE.Views.FieldSettingsDialog.txtOutline": "Esquema", "SSE.Views.FieldSettingsDialog.txtProduct": "Producto", "SSE.Views.FieldSettingsDialog.txtRepeat": "Repetir etiquetas de elementos en cada fila", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Mostrar subtotales", "SSE.Views.FieldSettingsDialog.txtSourceName": "Nombre de origen:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtSummarize": "Funciones para subtotales", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabular", "SSE.Views.FieldSettingsDialog.txtTop": "Mostrar en la parte superior del grupo", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnBackCaption": "Abrir ubicación del archivo", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON>r nueva", "SSE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> como", "SSE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnFileOpenCaption": "Abrir", "SSE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnHistoryCaption": "Historial de versiones", "SSE.Views.FileMenu.btnInfoCaption": "Info sobre hoja de cálculo", "SSE.Views.FileMenu.btnPrintCaption": "Imprimir", "SSE.Views.FileMenu.btnProtectCaption": "Proteger", "SSE.Views.FileMenu.btnRecentFilesCaption": "Abrir reciente", "SSE.Views.FileMenu.btnRenameCaption": "Cambiar nombre", "SSE.Views.FileMenu.btnReturnCaption": "Volver a hoja de cálculo", "SSE.Views.FileMenu.btnRightsCaption": "Derechos de acceso", "SSE.Views.FileMenu.btnSaveAsCaption": "Guardar como", "SSE.Views.FileMenu.btnSaveCaption": "Guardar", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Guardar Copia como", "SSE.Views.FileMenu.btnSettingsCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnToEditCaption": "Editar hoja de cá<PERSON>culo", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Hoja de cálculo en blanco", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON>r nueva", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Aplicar", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Agregar autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Agregar texto", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplicación", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Cambiar derechos de acceso", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Comentario", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Última modificación por", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Última modificación", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Propietario", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Ubicación", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Personas que tienen derechos", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Etiquetas", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Subido", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Cambiar derechos de acceso", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Personas que tienen derechos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Aplicar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Modo de co-edición", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Utiliza el sistema de fechas de 1904", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Separador decimal", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Idioma del diccionario", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Hinting", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Idioma de fórmulas", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Ejemplo: SUMA; MIN; MAX; CONTAR", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "<PERSON><PERSON><PERSON> en MAYÚSCULAS", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "<PERSON><PERSON><PERSON> con números", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Ajustes de macros", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Mostrar el botón Opciones de pegado cuando se pegue contenido", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Estilo de referencia R1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Ajustes regionales", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Ejemplo:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Mostrar comentarios en la hoja", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Muestra los cambios de otros usuarios", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Mostrar comentarios resueltos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Estricto", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Tema de interfaz", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Separador de miles", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Unidad de medida", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Utilizar separadores basados en los ajustes regionales", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Valor de zoom predeterminado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Cada 10 minutos ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Cada 30 minutos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Cada 5 minutos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON><PERSON> hora", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Autorrecuperación", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Guardar automáticamente", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Desactivado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Guardar versiones intermedias", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Cada minuto", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Estilo <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Opciones de autocorrección...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Bieloruso", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Búlgaro", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Catalán", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Modo de caché predeterminado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Calculando", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centímetro", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Colaboración", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Checo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Alemán", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Editar y guardar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Griego", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Inglés", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Español", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Co-edición en tiempo real. Todos los cambios se guardan automáticamente", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Finlandés", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonesio", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Pulgada", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italiano", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Japonés", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Lao", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Letón", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "como OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Nativo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Noruego", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Neerlandés", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Polaco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Revisión", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Punt<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "<PERSON>ug<PERSON><PERSON> (Brasil)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON>ug<PERSON><PERSON> (Portugal)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Mostrar el botón Impresión Rápida en el encabezado del editor", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "El documento se imprimirá en la última impresora seleccionada o predeterminada", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Región", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Habil<PERSON>r todo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Habilitar todas las macros sin notificación ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Eslovaco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Esloveno", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "<PERSON>habili<PERSON> todo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Deshabilitar todas las macros sin notificación", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Utilice el botón \"Guardar\" para sincronizar los cambios que usted y los demás realicen", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Sueco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ucraniano", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Utilice la tecla Alt para navegar por la interfaz de usuario mediante el teclado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Utilice la tecla Opción para navegar por la interfaz de usuario mediante el teclado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnamita", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Mostrar notificación", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Deshabilitar todas las macros con notificación", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "como Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Área de trabajo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Chino", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Aviso", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Con contraseña", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Proteger ho<PERSON> de c<PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Con firma", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Editar hoja de cá<PERSON>culo", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "La edición eliminará las firmas de la hoja de cálculo<br>¿Está seguro de que quiere continuar?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Esta hoja de cálculo se ha protegido con una contraseña", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Esta hoja de cálculo debe firmarse.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Se han agregado firmas válidas a la hoja de cálculo. La hoja de cálculo está protegida contra la edición.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Algunas de las firmas digitales en la hoja de cálculo son inválidas o no se pudieron verificar. La hoja de cálculo está protegida y no se puede editar.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Ver firmas", "SSE.Views.FormatRulesEditDlg.fillColor": "Color de relleno", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Advertencia", "SSE.Views.FormatRulesEditDlg.text2Scales": "Escala de 2 colores", "SSE.Views.FormatRulesEditDlg.text3Scales": "Escala de 3 colores", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Todos los bordes", "SSE.Views.FormatRulesEditDlg.textAppearance": "Apariencia de la barra", "SSE.Views.FormatRulesEditDlg.textApply": "Aplicar al rango", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automático", "SSE.Views.FormatRulesEditDlg.textAxis": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Dirección de barra", "SSE.Views.FormatRulesEditDlg.textBold": "Negrita", "SSE.Views.FormatRulesEditDlg.textBorder": "<PERSON>rde", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Color de los bordes", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Bordes inferiores", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "No se puede agregar el formato condicional.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Punto medio de celda", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Bordes verticales internos", "SSE.Views.FormatRulesEditDlg.textClear": "Bo<PERSON>r", "SSE.Views.FormatRulesEditDlg.textColor": "Color de texto", "SSE.Views.FormatRulesEditDlg.textContext": "Contexto", "SSE.Views.FormatRulesEditDlg.textCustom": "Personalizado", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Borde diagonal descendente", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Borde diagonal ascendente", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Escriba una fórmula válida.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "La formula que ha introducido no evalúa un número, fecha, hora o cadena.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Escriba un valor.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "El valor que ha especificado no es un número, fecha, hora o cadena válidos.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "El valor del {0} debe ser mayor que el valor del {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Escriba un número entre {0} y {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormat": "Formato", "SSE.Views.FormatRulesEditDlg.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textGradient": "Gradiente", "SSE.Views.FormatRulesEditDlg.textIconLabel": "cuando {0} {1} y", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "cuando {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "cuando el valor es", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Uno o varios rangos de datos de icono se superponen.<br>Ajuste los valores de los rangos para que no se superpongan.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Estilo de <PERSON>", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Bordes internos", "SSE.Views.FormatRulesEditDlg.textInvalid": "<PERSON><PERSON> de datos inválido", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "¡ERROR! Rango de celdas inválido", "SSE.Views.FormatRulesEditDlg.textItalic": "Cursiva", "SSE.Views.FormatRulesEditDlg.textItem": "Elemento", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "De izquierda a derecha", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "<PERSON><PERSON> izqui<PERSON>", "SSE.Views.FormatRulesEditDlg.textLongBar": "Barra más larga", "SSE.Views.FormatRulesEditDlg.textMaximum": "Máximo", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Punto máxi<PERSON>", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Bordes horizontales internos", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Punto medio", "SSE.Views.FormatRulesEditDlg.textMinimum": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMinpoint": "<PERSON><PERSON><PERSON> m<PERSON>", "SSE.Views.FormatRulesEditDlg.textNegative": "Negativo", "SSE.Views.FormatRulesEditDlg.textNewColor": "Color personalizado", "SSE.Views.FormatRulesEditDlg.textNoBorders": "<PERSON> bordes", "SSE.Views.FormatRulesEditDlg.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Uno o varios valores especificados no son un porcentaje válido.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "El valor {0} especificado no es un porcentaje válido.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Uno o varios valores especificados no son un percentil válido.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "El valor {0} especificado no es un percentil válido.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Bordes externos", "SSE.Views.FormatRulesEditDlg.textPercent": "<PERSON>r ciento", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentil", "SSE.Views.FormatRulesEditDlg.textPosition": "Posición", "SSE.Views.FormatRulesEditDlg.textPositive": "Positivo", "SSE.Views.FormatRulesEditDlg.textPresets": "Preestablecidos", "SSE.Views.FormatRulesEditDlg.textPreview": "Vista previa", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "No se pueden utilizar referencias relativas en los criterios de formato condicional para las escalas de color, las barras de datos y los conjuntos de iconos.", "SSE.Views.FormatRulesEditDlg.textReverse": "Invertir criterio de ordenación de iconos", "SSE.Views.FormatRulesEditDlg.textRight2Left": "De derecha a izquierda", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Bordes derechos", "SSE.Views.FormatRulesEditDlg.textRule": "Regla", "SSE.Views.FormatRulesEditDlg.textSameAs": "Igual que positivo", "SSE.Views.FormatRulesEditDlg.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textShortBar": "Barra más corta", "SSE.Views.FormatRulesEditDlg.textShowBar": "Mostrar solo la barra", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Mostrar icono únicamente", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Este tipo de referencia no se puede utilizar en una fórmula de formato condicional.<br><PERSON>bie la referencia a una sola celda o utilice la referencia con una función de la hoja de cálculo, como =SUMA(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textStrikeout": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSubscript": "Subíndice", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Superíndice", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Bordes superiores", "SSE.Views.FormatRulesEditDlg.textUnderline": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Formato de número", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Contabilidad", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Moneda", "SSE.Views.FormatRulesEditDlg.txtDate": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Este campo es obligatorio", "SSE.Views.FormatRulesEditDlg.txtFraction": "Fracción", "SSE.Views.FormatRulesEditDlg.txtGeneral": "General", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "Sin icono", "SSE.Views.FormatRulesEditDlg.txtNumber": "Número", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Po<PERSON>entaj<PERSON>", "SSE.Views.FormatRulesEditDlg.txtScientific": "Scientífico", "SSE.Views.FormatRulesEditDlg.txtText": "Texto", "SSE.Views.FormatRulesEditDlg.txtTime": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Editar regla de formato", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Nueva regla de formato", "SSE.Views.FormatRulesManagerDlg.guestText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.lockText": "Bloqueado", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 desv. est. por encima del promedio", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 desv. est. por debajo del promedio", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 desv. est. por encima del promedio", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 desv. est. por debajo del promedio", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 desv. est. por encima del promedio", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 desv. est. por debajo del promedio", "SSE.Views.FormatRulesManagerDlg.textAbove": "Por encima del promedio", "SSE.Views.FormatRulesManagerDlg.textApply": "Aplicar a", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "El valor de celda comienza por", "SSE.Views.FormatRulesManagerDlg.textBelow": "Por debajo del promedio", "SSE.Views.FormatRulesManagerDlg.textBetween": "está comprendido entre {0} y {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Escala de color escalonada", "SSE.Views.FormatRulesManagerDlg.textContains": "El valor de celda contiene", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "La celda contiene un valor en blanco", "SSE.Views.FormatRulesManagerDlg.textContainsError": "La celda contiene un error", "SSE.Views.FormatRulesManagerDlg.textDelete": "Eliminar", "SSE.Views.FormatRulesManagerDlg.textDown": "Mover regla hacia abajo", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Duplicar valores", "SSE.Views.FormatRulesManagerDlg.textEdit": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEnds": "El valor de celda termina con", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Mayor o igual que el promedio", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "<PERSON>or o igual que el promedio", "SSE.Views.FormatRulesManagerDlg.textFormat": "Formato", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Conjunto de iconos", "SSE.Views.FormatRulesManagerDlg.textNew": "Nuevo", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "no está comprendido entre {0} y {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "El valor de celda no contiene", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "La celda no contiene un valor en blanco", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "La celda no contiene ningún error", "SSE.Views.FormatRulesManagerDlg.textRules": "Reg<PERSON>", "SSE.Views.FormatRulesManagerDlg.textScope": "Mostrar reglas de formato para", "SSE.Views.FormatRulesManagerDlg.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textSelection": "Selección actual", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Esta tabla pivote", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Esta hoja de cálculo", "SSE.Views.FormatRulesManagerDlg.textThisTable": "Esta tabla", "SSE.Views.FormatRulesManagerDlg.textUnique": "Valores únicos", "SSE.Views.FormatRulesManagerDlg.textUp": "Mover regla hacia arriba", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Este elemento se está editando por otro usuario.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Formato condicional", "SSE.Views.FormatSettingsDialog.textCategory": "Categoría", "SSE.Views.FormatSettingsDialog.textDecimal": "Decimal", "SSE.Views.FormatSettingsDialog.textFormat": "Formato", "SSE.Views.FormatSettingsDialog.textLinked": "Vinculado al origen", "SSE.Views.FormatSettingsDialog.textSeparator": "Usar un separador de 1000", "SSE.Views.FormatSettingsDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textTitle": "Formato de número", "SSE.Views.FormatSettingsDialog.txtAccounting": "Contabilidad", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON><PERSON><PERSON><PERSON> (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Сentésimas (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "Dieciseisavos (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Mitades (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "Cuartos (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Octavos (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Moneda", "SSE.Views.FormatSettingsDialog.txtCustom": "Personalizado", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Por favor, introduzca el formato de número personalizado con cuidado. El Editor de hojas de cálculo no comprueba los formatos personalizados para detectar errores que puedan afectar al archivo xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtFraction": "Fracción", "SSE.Views.FormatSettingsDialog.txtGeneral": "General", "SSE.Views.FormatSettingsDialog.txtNone": "Ninguna", "SSE.Views.FormatSettingsDialog.txtNumber": "Número", "SSE.Views.FormatSettingsDialog.txtPercentage": "Po<PERSON>entaj<PERSON>", "SSE.Views.FormatSettingsDialog.txtSample": "Ejemplo:", "SSE.Views.FormatSettingsDialog.txtScientific": "Scientífico", "SSE.Views.FormatSettingsDialog.txtText": "Texto", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtUpto1": "<PERSON><PERSON> un dígito (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "<PERSON><PERSON>í<PERSON> (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "<PERSON><PERSON> tres dígitos (131/135)", "SSE.Views.FormulaDialog.sDescription": "Descripción", "SSE.Views.FormulaDialog.textGroupDescription": "Seleccionar grupo de función", "SSE.Views.FormulaDialog.textListDescription": "Seleccionar función", "SSE.Views.FormulaDialog.txtRecommended": "Recomendado", "SSE.Views.FormulaDialog.txtSearch": "Buscar", "SSE.Views.FormulaDialog.txtTitle": "Insertar función", "SSE.Views.FormulaTab.textAutomatic": "Automático", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Calcular la hoja actual", "SSE.Views.FormulaTab.textCalculateWorkbook": "Calcular libro de trabajo", "SSE.Views.FormulaTab.textManual": "Manualmente", "SSE.Views.FormulaTab.tipCalculate": "Calcular", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Calcular todo el libro de trabajo", "SSE.Views.FormulaTab.tipWatch": "Añadir celdas a la lista de la ventana Inspección", "SSE.Views.FormulaTab.txtAdditional": "Adicional", "SSE.Views.FormulaTab.txtAutosum": "Autosuma", "SSE.Views.FormulaTab.txtAutosumTip": "<PERSON><PERSON>", "SSE.Views.FormulaTab.txtCalculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormula": "Función", "SSE.Views.FormulaTab.txtFormulaTip": "Insertar función", "SSE.Views.FormulaTab.txtMore": "Más funciones", "SSE.Views.FormulaTab.txtRecent": "Usados recientemente", "SSE.Views.FormulaTab.txtWatch": "Ventana Inspección", "SSE.Views.FormulaWizard.textAny": "cualquier", "SSE.Views.FormulaWizard.textArgument": "Argumento", "SSE.Views.FormulaWizard.textFunction": "Función", "SSE.Views.FormulaWizard.textFunctionRes": "Resultado de la función", "SSE.Views.FormulaWizard.textHelp": "Ayuda sobre esta función", "SSE.Views.FormulaWizard.textLogical": "lógico", "SSE.Views.FormulaWizard.textNoArgs": "Esta función no tiene argumentos", "SSE.Views.FormulaWizard.textNumber": "número", "SSE.Views.FormulaWizard.textRef": "referencia", "SSE.Views.FormulaWizard.textText": "texto", "SSE.Views.FormulaWizard.textTitle": "Argumentos de función", "SSE.Views.FormulaWizard.textValue": "Resultado de la fórmula", "SSE.Views.HeaderFooterDialog.textAlign": "Alinear con márgenes de página", "SSE.Views.HeaderFooterDialog.textAll": "Todas las páginas", "SSE.Views.HeaderFooterDialog.textBold": "Negrita", "SSE.Views.HeaderFooterDialog.textCenter": "Al centro", "SSE.Views.HeaderFooterDialog.textColor": "Color de texto", "SSE.Views.HeaderFooterDialog.textDate": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Primera página diferente", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Páginas impares y pares diferentes", "SSE.Views.HeaderFooterDialog.textEven": "Página par", "SSE.Views.HeaderFooterDialog.textFileName": "Nombre de archivo", "SSE.Views.HeaderFooterDialog.textFirst": "Primera página", "SSE.Views.HeaderFooterDialog.textFooter": "Pie de página", "SSE.Views.HeaderFooterDialog.textHeader": "Encabezado", "SSE.Views.HeaderFooterDialog.textInsert": "Insertar", "SSE.Views.HeaderFooterDialog.textItalic": "Cursiva", "SSE.Views.HeaderFooterDialog.textLeft": "A la izquierda", "SSE.Views.HeaderFooterDialog.textMaxError": "El texto es demasiado largo. Reduzca el número de caracteres usados.", "SSE.Views.HeaderFooterDialog.textNewColor": "Color personalizado", "SSE.Views.HeaderFooterDialog.textOdd": "<PERSON><PERSON>gina impar", "SSE.Views.HeaderFooterDialog.textPageCount": "Número de páginas", "SSE.Views.HeaderFooterDialog.textPageNum": "Número de página", "SSE.Views.HeaderFooterDialog.textPresets": "Preestablecidos", "SSE.Views.HeaderFooterDialog.textRight": "A la derecha", "SSE.Views.HeaderFooterDialog.textScale": "Escalar con documento", "SSE.Views.HeaderFooterDialog.textSheet": "Nombre de hoja", "SSE.Views.HeaderFooterDialog.textStrikeout": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textSubscript": "Subíndice", "SSE.Views.HeaderFooterDialog.textSuperscript": "Superíndice", "SSE.Views.HeaderFooterDialog.textTime": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTitle": "Ajustes de encabezado / pie de página", "SSE.Views.HeaderFooterDialog.textUnderline": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.tipFontName": "Fuente", "SSE.Views.HeaderFooterDialog.tipFontSize": "Tamaño de fuente", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Mostrar", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Enlace a", "SSE.Views.HyperlinkSettingsDialog.strRange": "Ra<PERSON>", "SSE.Views.HyperlinkSettingsDialog.strSheet": "Hoja", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Copiar ", "SSE.Views.HyperlinkSettingsDialog.textDefault": "<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Introduzca título aquí", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Introduzca enlace aquí", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Introduzca informacíon sobre herramientas aquí", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Enlace externo", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Obtener enlace", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Rango de datos interno", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "¡ERROR!¡Rango de celdas inválido!", "SSE.Views.HyperlinkSettingsDialog.textNames": "Nombres definidos", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Hojas", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Información en pantalla", "SSE.Views.HyperlinkSettingsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Este campo es obligatorio", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Este campo debe ser una URL en el formato \"http://www.example.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Este campo está limitado a 2083 caracteres", "SSE.Views.ImageSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "SSE.Views.ImageSettings.textCrop": "Recortar", "SSE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFit": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropToShape": "Recortar a la forma", "SSE.Views.ImageSettings.textEdit": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textEditObject": "<PERSON>ar objeto", "SSE.Views.ImageSettings.textFlip": "Volteo", "SSE.Views.ImageSettings.textFromFile": "De archivo", "SSE.Views.ImageSettings.textFromStorage": "Desde almacenamiento", "SSE.Views.ImageSettings.textFromUrl": "De URL", "SSE.Views.ImageSettings.textHeight": "Altura", "SSE.Views.ImageSettings.textHint270": "Girar 90° a la izquierda", "SSE.Views.ImageSettings.textHint90": "Girar 90° a la derecha", "SSE.Views.ImageSettings.textHintFlipH": "Volteo Horizontal", "SSE.Views.ImageSettings.textHintFlipV": "Volteo Vertical", "SSE.Views.ImageSettings.textInsert": "<PERSON><PERSON><PERSON><PERSON><PERSON> imagen", "SSE.Views.ImageSettings.textKeepRatio": "Proporciones constantes", "SSE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textRecentlyUsed": "Usados recientemente", "SSE.Views.ImageSettings.textRotate90": "Girar 90°", "SSE.Views.ImageSettings.textRotation": "Rotación", "SSE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "No mover, ni cambiar tamaño con celdas", "SSE.Views.ImageSettingsAdvanced.textAlt": "Texto alternativo", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Descripción", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Representación de texto alternativa de la información sobre el objeto visual, que se leerá para las personas con deficiencia visual o deterioro cognitivo para ayudarles a entender mejor, que información contiene la imagen, autoforma, gráfica o tabla.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Volteado", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontalmente", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Mover sin cambiar tamaño con celdas", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rotación", "SSE.Views.ImageSettingsAdvanced.textSnap": "Ajustar a la celda", "SSE.Views.ImageSettingsAdvanced.textTitle": "Imagen - <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Mover y cambiar tamaño con celdas", "SSE.Views.ImageSettingsAdvanced.textVertically": "Verticalmente", "SSE.Views.ImportFromXmlDialog.textDestination": "<PERSON>ja dónde colocar los datos", "SSE.Views.ImportFromXmlDialog.textExist": "Hoja de cálculo existente", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "<PERSON>ngo de celdas no válido", "SSE.Views.ImportFromXmlDialog.textNew": "Hoja de cálculo nueva", "SSE.Views.ImportFromXmlDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImportFromXmlDialog.textTitle": "Importar datos", "SSE.Views.ImportFromXmlDialog.txtEmpty": "Este campo es obligatorio", "SSE.Views.LeftMenu.tipAbout": "Acerca de programa", "SSE.Views.LeftMenu.tipChat": "Cha<PERSON>", "SSE.Views.LeftMenu.tipComments": "Comentarios", "SSE.Views.LeftMenu.tipFile": "Archivo", "SSE.Views.LeftMenu.tipPlugins": "Plugins", "SSE.Views.LeftMenu.tipSearch": "Buscar", "SSE.Views.LeftMenu.tipSpellcheck": "Сorrección ortográfica", "SSE.Views.LeftMenu.tipSupport": "Feedback y Soporte", "SSE.Views.LeftMenu.txtDeveloper": "MODO DE DESARROLLO", "SSE.Views.LeftMenu.txtEditor": "Editor de hojas de cálculo", "SSE.Views.LeftMenu.txtLimit": "Limitar acceso", "SSE.Views.LeftMenu.txtTrial": "MODO DE PRUEBA", "SSE.Views.LeftMenu.txtTrialDev": "Modo de programador de prueba", "SSE.Views.MacroDialog.textMacro": "Nombre de macro", "SSE.Views.MacroDialog.textTitle": "Asignar macro", "SSE.Views.MainSettingsPrint.okButtonText": "Guardar", "SSE.Views.MainSettingsPrint.strBottom": "Inferior", "SSE.Views.MainSettingsPrint.strLandscape": "Horizontal", "SSE.Views.MainSettingsPrint.strLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "Vertical", "SSE.Views.MainSettingsPrint.strPrint": "Imprimir", "SSE.Views.MainSettingsPrint.strPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strRight": "Derecho", "SSE.Views.MainSettingsPrint.strTop": "Superior", "SSE.Views.MainSettingsPrint.textActualSize": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustom": "Personalizado", "SSE.Views.MainSettingsPrint.textCustomOptions": "Opciones personalizadas", "SSE.Views.MainSettingsPrint.textFitCols": "Caber todas las columnas en una página", "SSE.Views.MainSettingsPrint.textFitPage": "Caber la hoja en una página", "SSE.Views.MainSettingsPrint.textFitRows": "Caber Todas las Filas en una Página", "SSE.Views.MainSettingsPrint.textPageOrientation": "Orientación de página", "SSE.Views.MainSettingsPrint.textPageScaling": "Escala", "SSE.Views.MainSettingsPrint.textPageSize": "Tamaño de página", "SSE.Views.MainSettingsPrint.textPrintGrid": "Imprimir <PERSON>", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Imprimir títulos de filas y columnas", "SSE.Views.MainSettingsPrint.textRepeat": "Repetir...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Repetir columnas a la izquierda", "SSE.Views.MainSettingsPrint.textRepeatTop": "Repetir filas en la parte superior", "SSE.Views.MainSettingsPrint.textSettings": "<PERSON><PERSON><PERSON><PERSON> para", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Los rangos con nombre existentes no pueden ser editados y los nuevos no se pueden crear<br>en este momento ya que algunos de ellos están editándose.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Nombre definido", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Aviso", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Libro de trabajo", "SSE.Views.NamedRangeEditDlg.textDataRange": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textExistName": "¡Error! Banda con este nombre ya existe", "SSE.Views.NamedRangeEditDlg.textInvalidName": "El nombre debe comenzar con una letra o un guión bajo y no debe contener caracteres no válidos.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "¡Error! Alcance de celdas no válido", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ERROR! This element is being edited by another user.", "SSE.Views.NamedRangeEditDlg.textName": "Nombre", "SSE.Views.NamedRangeEditDlg.textReservedName": "El nombre que está tratando de usar ya se hace referencia en las fórmulas de celda. Por favor seleccione otro nombre.", "SSE.Views.NamedRangeEditDlg.textScope": "Alcance", "SSE.Views.NamedRangeEditDlg.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Este campo es obligatorio", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Editar nombre", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Nuevo nombre", "SSE.Views.NamedRangePasteDlg.textNames": "Rangos con nombre", "SSE.Views.NamedRangePasteDlg.txtTitle": "<PERSON><PERSON>ar nombre", "SSE.Views.NameManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.guestText": "Visitante", "SSE.Views.NameManagerDlg.lockText": "Bloqueado", "SSE.Views.NameManagerDlg.textDataRange": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDelete": "Eliminar", "SSE.Views.NameManagerDlg.textEdit": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEmpty": "No se han creado ningunas bandas nombradas todavía.<br> <PERSON><PERSON> por lo menos una banda nombrada y aparecerá en este campo.", "SSE.Views.NameManagerDlg.textFilter": "Filtro", "SSE.Views.NameManagerDlg.textFilterAll": "Todo", "SSE.Views.NameManagerDlg.textFilterDefNames": "Nombres definidos", "SSE.Views.NameManagerDlg.textFilterSheet": "Nombres en el ámbito de la hoja", "SSE.Views.NameManagerDlg.textFilterTableNames": "nombres de tablas", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Nombres en el ámbito del libro", "SSE.Views.NameManagerDlg.textNew": "Nuevo", "SSE.Views.NameManagerDlg.textnoNames": "No se han encontrado ningunas bandas nombradas que coincidan con su filtro.", "SSE.Views.NameManagerDlg.textRanges": "Rangos con nombre", "SSE.Views.NameManagerDlg.textScope": "Alcance", "SSE.Views.NameManagerDlg.textWorkbook": "Libro de trabajo", "SSE.Views.NameManagerDlg.tipIsLocked": "Este elemento está siendo editado por otro usuario.", "SSE.Views.NameManagerDlg.txtTitle": "Administrador de nombre", "SSE.Views.NameManagerDlg.warnDelete": "¿Esta seguro/a de que quiere borrar el nombre {0}?", "SSE.Views.PageMarginsDialog.textBottom": "Inferior", "SSE.Views.PageMarginsDialog.textLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textRight": "Derecho", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Superior", "SSE.Views.ParagraphSettings.strLineHeight": "Espaciado de línea", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Espaciado de Párafo ", "SSE.Views.ParagraphSettings.strSpacingAfter": "Después", "SSE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "SSE.Views.ParagraphSettings.textAt": "En", "SSE.Views.ParagraphSettings.textAtLeast": "Por lo menos", "SSE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textExact": "Exacto", "SSE.Views.ParagraphSettings.txtAutoText": "Auto", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Las pestañas especificadas aparecerán en este campo", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Doble tachado", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Reti<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Espaciado de línea", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Derecho", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Después", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Especial", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Por", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Letra ", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Sangría y espaciado", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON><PERSON> pequeñas", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Subíndice", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Sobreíndice", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Pestaña", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Alineación", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Espacia<PERSON> entre caracteres", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Pestaña predeterminada", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Efectos", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Exactamente", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Primera línea", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Suspendido", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Justificado", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(ninguno)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Eliminar", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Eliminar todo", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Especificar", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Al centro", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posición de tab", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Derecho", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Párrafo - <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "es igual a", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "no termina con", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "contiene", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "no contiene", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "entre", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "no está entre", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "no es igual a", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "es mayor que", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "es mayor o igual a ", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "es menor que", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "es menor o igual a ", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "empieza con", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "no empieza con", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "termina con", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Mostrar elementos para los que la etiqueta:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Mostrar elementos para los que:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Use ? para presentar un caracter", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Use *  para presentar una serie de caracteres", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "y", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Filtrar por etiqueta", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Filtro de valor", "SSE.Views.PivotGroupDialog.textAuto": "Auto", "SSE.Views.PivotGroupDialog.textBy": "Por", "SSE.Views.PivotGroupDialog.textDays": "Días", "SSE.Views.PivotGroupDialog.textEnd": "Terminar en", "SSE.Views.PivotGroupDialog.textError": "Este campo debe ser un valor numérico", "SSE.Views.PivotGroupDialog.textGreaterError": "El número final debe ser mayor que el número inicial.", "SSE.Views.PivotGroupDialog.textHour": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMonth": "Meses", "SSE.Views.PivotGroupDialog.textNumDays": "Número de días", "SSE.Views.PivotGroupDialog.textQuart": "Trimestres", "SSE.Views.PivotGroupDialog.textSec": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textStart": "Comenzar en", "SSE.Views.PivotGroupDialog.textYear": "años", "SSE.Views.PivotGroupDialog.txtTitle": "Agrupación", "SSE.Views.PivotSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "SSE.Views.PivotSettings.textColumns": "Columnas", "SSE.Views.PivotSettings.textFields": "Seleccionar campos", "SSE.Views.PivotSettings.textFilters": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "Valores", "SSE.Views.PivotSettings.txtAddColumn": "Agregar a columnas", "SSE.Views.PivotSettings.txtAddFilter": "Agregar a filtros", "SSE.Views.PivotSettings.txtAddRow": "Agregar a filas", "SSE.Views.PivotSettings.txtAddValues": "Agregar a valores", "SSE.Views.PivotSettings.txtFieldSettings": "Ajustes de campo", "SSE.Views.PivotSettings.txtMoveBegin": "Mover al principio", "SSE.Views.PivotSettings.txtMoveColumn": "Mover a columnas", "SSE.Views.PivotSettings.txtMoveDown": "Mover hacia abajo", "SSE.Views.PivotSettings.txtMoveEnd": "Mover al final", "SSE.Views.PivotSettings.txtMoveFilter": "Mover a filtros", "SSE.Views.PivotSettings.txtMoveRow": "Mover a filas", "SSE.Views.PivotSettings.txtMoveUp": "Mover hacia arriba", "SSE.Views.PivotSettings.txtMoveValues": "Mover a valores", "SSE.Views.PivotSettings.txtRemove": "Eliminar campo", "SSE.Views.PivotSettingsAdvanced.strLayout": "Nombre y diseño", "SSE.Views.PivotSettingsAdvanced.textAlt": "Texto alternativo", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Descripción", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Representación de texto alternativa de la información sobre el objeto visual, que se leerá para las personas con deficiencia visual o deterioro cognitivo para ayudarles a entender mejor, que información contiene la imagen, autoforma, gráfica o tabla.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Ajusta automáticamente el ancho de la columna al actualizar", "SSE.Views.PivotSettingsAdvanced.textDataRange": "<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Origen de datos", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Mostrar campos en área de filtro de informe", "SSE.Views.PivotSettingsAdvanced.textDown": "Hacia abajo, luego horizontalmente", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Totales generales", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Encabezados de campo", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "¡ERROR!¡Rango de celdas inválido! ", "SSE.Views.PivotSettingsAdvanced.textOver": "Horizontalmente, luego hacia abajo", "SSE.Views.PivotSettingsAdvanced.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Mostrar para columnas", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Mostrar encabezados de campo para filas y columnas", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Mostrar para filas", "SSE.Views.PivotSettingsAdvanced.textTitle": "Tabla dinámica - <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Campos de filtro de informe por columna", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Campos de filtro de informe por fila", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Este campo es obligatorio", "SSE.Views.PivotSettingsAdvanced.txtName": "Nombre", "SSE.Views.PivotTable.capBlankRows": "Filas en blanco", "SSE.Views.PivotTable.capGrandTotals": "Totales", "SSE.Views.PivotTable.capLayout": "Diseño de informe", "SSE.Views.PivotTable.capSubtotals": "Subtotales", "SSE.Views.PivotTable.mniBottomSubtotals": "Mostrar todos los subtotales en la parte inferior del grupo", "SSE.Views.PivotTable.mniInsertBlankLine": "Insertar línea en blanco después de cada elemento", "SSE.Views.PivotTable.mniLayoutCompact": "Mostrar de forma compacta", "SSE.Views.PivotTable.mniLayoutNoRepeat": "No repetir todas las etiquetas de elementos", "SSE.Views.PivotTable.mniLayoutOutline": "Mostrar en forma de esquema", "SSE.Views.PivotTable.mniLayoutRepeat": "Repetir todas las etiquetas de elementos", "SSE.Views.PivotTable.mniLayoutTabular": "Mostrar en forma tabular", "SSE.Views.PivotTable.mniNoSubtotals": "No mostrar subtotales", "SSE.Views.PivotTable.mniOffTotals": "Desactivado para filas y columnas", "SSE.Views.PivotTable.mniOnColumnsTotals": "Activado solo para columnas", "SSE.Views.PivotTable.mniOnRowsTotals": "Activado solo para filas", "SSE.Views.PivotTable.mniOnTotals": "Activado para filas y columnas", "SSE.Views.PivotTable.mniRemoveBlankLine": "Quitar línea en blanco después de cada elemento", "SSE.Views.PivotTable.mniTopSubtotals": "Mostrar todos los subtotales en la parte superior del grupo", "SSE.Views.PivotTable.textColBanded": "Columnas con bandas", "SSE.Views.PivotTable.textColHeader": "Títulos de columnas", "SSE.Views.PivotTable.textRowBanded": "Filas con bandas", "SSE.Views.PivotTable.textRowHeader": "Encabezados de fila", "SSE.Views.PivotTable.tipCreatePivot": "Introducir tabla pivote", "SSE.Views.PivotTable.tipGrandTotals": "Mostrar u ocultar totales", "SSE.Views.PivotTable.tipRefresh": "Actualizar la información del origen de los datos", "SSE.Views.PivotTable.tipRefreshCurrent": "Actualizar la información del origen de datos para la tabla actual", "SSE.Views.PivotTable.tipSelect": "Seleccione toda la tabla de pivote", "SSE.Views.PivotTable.tipSubtotals": "Mostrar u ocultar subtotales", "SSE.Views.PivotTable.txtCreate": "Insertar tabla", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Personalizado", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Oscuro", "SSE.Views.PivotTable.txtGroupPivot_Light": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Medio", "SSE.Views.PivotTable.txtPivotTable": "Tabla Dinámica", "SSE.Views.PivotTable.txtRefresh": "Actualizar", "SSE.Views.PivotTable.txtRefreshAll": "Actualizar todo", "SSE.Views.PivotTable.txtSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Estilo de tabla dinámica: oscuro", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Estilo de tabla dinámica: claro", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Estilo de tabla dinámica: medio", "SSE.Views.PrintSettings.btnDownload": "Guardar y descargar", "SSE.Views.PrintSettings.btnPrint": "Guardar e imprimir", "SSE.Views.PrintSettings.strBottom": "Inferior", "SSE.Views.PrintSettings.strLandscape": "Horizontal", "SSE.Views.PrintSettings.strLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "Vertical", "SSE.Views.PrintSettings.strPrint": "Imprimir", "SSE.Views.PrintSettings.strPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strRight": "Derecho", "SSE.Views.PrintSettings.strShow": "Mostrar", "SSE.Views.PrintSettings.strTop": "Superior", "SSE.Views.PrintSettings.textActualSize": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textAllSheets": "Todas las hojas", "SSE.Views.PrintSettings.textCurrentSheet": "<PERSON><PERSON> actual", "SSE.Views.PrintSettings.textCustom": "Personalizado", "SSE.Views.PrintSettings.textCustomOptions": "Opciones personalizadas", "SSE.Views.PrintSettings.textFitCols": "Ajustar todas las columnas en una página", "SSE.Views.PrintSettings.textFitPage": "Ajustar la hoja en una página", "SSE.Views.PrintSettings.textFitRows": "Ajustar todas las filas en una página", "SSE.Views.PrintSettings.textHideDetails": "<PERSON><PERSON><PERSON><PERSON> de<PERSON><PERSON>", "SSE.Views.PrintSettings.textIgnore": "Omitir el área de impresión", "SSE.Views.PrintSettings.textLayout": "Diseño", "SSE.Views.PrintSettings.textPageOrientation": "Orientación de página", "SSE.Views.PrintSettings.textPageScaling": "Escala", "SSE.Views.PrintSettings.textPageSize": "Tamaño de página", "SSE.Views.PrintSettings.textPrintGrid": "<PERSON><PERSON><PERSON><PERSON> cuadric<PERSON>", "SSE.Views.PrintSettings.textPrintHeadings": "Imprimir títulos de filas y columnas", "SSE.Views.PrintSettings.textPrintRange": "Área de impresión", "SSE.Views.PrintSettings.textRange": "Ra<PERSON>", "SSE.Views.PrintSettings.textRepeat": "Repetir...", "SSE.Views.PrintSettings.textRepeatLeft": "Repetir columnas a la izquierda", "SSE.Views.PrintSettings.textRepeatTop": "Repetir filas en la parte superior", "SSE.Views.PrintSettings.textSelection": "Selección ", "SSE.Views.PrintSettings.textSettings": "<PERSON>justes de hoja", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON> de<PERSON>les", "SSE.Views.PrintSettings.textShowGrid": "Mostrar líneas de cuadrícula", "SSE.Views.PrintSettings.textShowHeadings": "Mostrar títulos de filas y columnas", "SSE.Views.PrintSettings.textTitle": "Opciones de impresión", "SSE.Views.PrintSettings.textTitlePDF": "Ajustes de PDF", "SSE.Views.PrintTitlesDialog.textFirstCol": "Primera columna", "SSE.Views.PrintTitlesDialog.textFirstRow": "Primera fila", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Columnas inmovilizadas", "SSE.Views.PrintTitlesDialog.textFrozenRows": "<PERSON><PERSON> ", "SSE.Views.PrintTitlesDialog.textInvalidRange": "¡ERROR!¡Rango de celdas inválido! ", "SSE.Views.PrintTitlesDialog.textLeft": "Repetir columnas a la izquierda", "SSE.Views.PrintTitlesDialog.textNoRepeat": "No repetir", "SSE.Views.PrintTitlesDialog.textRepeat": "Repetir...", "SSE.Views.PrintTitlesDialog.textSelectRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>o", "SSE.Views.PrintTitlesDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textTop": "Repetir filas en la parte superior", "SSE.Views.PrintWithPreview.txtActualSize": "Tamaño real", "SSE.Views.PrintWithPreview.txtAllSheets": "Todas las hojas", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Aplicar a todas las hojas", "SSE.Views.PrintWithPreview.txtBottom": "Abajo ", "SSE.Views.PrintWithPreview.txtCurrentSheet": "<PERSON><PERSON> actual", "SSE.Views.PrintWithPreview.txtCustom": "Personalizado", "SSE.Views.PrintWithPreview.txtCustomOptions": "Opciones personalizadas", "SSE.Views.PrintWithPreview.txtEmptyTable": "No hay nada para imprimir porque la tabla está vacía", "SSE.Views.PrintWithPreview.txtFitCols": "Ajustar todas las columnas en una página", "SSE.Views.PrintWithPreview.txtFitPage": "Ajustar la hoja en una página", "SSE.Views.PrintWithPreview.txtFitRows": "Ajustar todas las filas en una página", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Cuadrículas y encabezados", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Ajustes de encabezado / pie de página", "SSE.Views.PrintWithPreview.txtIgnore": "Omitir el área de impresión", "SSE.Views.PrintWithPreview.txtLandscape": "Horizontal", "SSE.Views.PrintWithPreview.txtLeft": "A la izquierda", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtOf": "de {0}", "SSE.Views.PrintWithPreview.txtPage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Número de página no válido", "SSE.Views.PrintWithPreview.txtPageOrientation": "Orientación de la página", "SSE.Views.PrintWithPreview.txtPageSize": "Tamaño de la página", "SSE.Views.PrintWithPreview.txtPortrait": "Vertical", "SSE.Views.PrintWithPreview.txtPrint": "Imprimir", "SSE.Views.PrintWithPreview.txtPrintGrid": "Imprimir líneas de cuadrícula", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Imprimir títulos de filas y columnas", "SSE.Views.PrintWithPreview.txtPrintRange": "Área de impresión", "SSE.Views.PrintWithPreview.txtPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtRepeat": "Repetir...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Repetir columnas a la izquierda", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Repetir filas en la parte superior", "SSE.Views.PrintWithPreview.txtRight": "A la derecha", "SSE.Views.PrintWithPreview.txtSave": "Guardar", "SSE.Views.PrintWithPreview.txtScaling": "Escala", "SSE.Views.PrintWithPreview.txtSelection": "Selección ", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Ajustes de la hoja", "SSE.Views.PrintWithPreview.txtSheet": "Hoja: {0}", "SSE.Views.PrintWithPreview.txtTop": "Arriba", "SSE.Views.ProtectDialog.textExistName": "¡ERROR! El rango con ese título ya existe", "SSE.Views.ProtectDialog.textInvalidName": "El título del rango debe comenzar con una letra y sólo puede contener letras, números y espacios.", "SSE.Views.ProtectDialog.textInvalidRange": "¡ERROR! Rango de celdas no válido", "SSE.Views.ProtectDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtAllow": "Permitir a todos los usuarios de esta hoja", "SSE.Views.ProtectDialog.txtAutofilter": "Usar Autofiltro", "SSE.Views.ProtectDialog.txtDelCols": "Eliminar columnas", "SSE.Views.ProtectDialog.txtDelRows": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtEmpty": "Este campo es obligatorio", "SSE.Views.ProtectDialog.txtFormatCells": "Aplicar formato a celdas", "SSE.Views.ProtectDialog.txtFormatCols": "Aplicar formato a columnas", "SSE.Views.ProtectDialog.txtFormatRows": "Aplicar formato a filas", "SSE.Views.ProtectDialog.txtIncorrectPwd": "La contraseña de confirmación no es idéntica", "SSE.Views.ProtectDialog.txtInsCols": "Insertar columnas", "SSE.Views.ProtectDialog.txtInsHyper": "Insertar <PERSON>", "SSE.Views.ProtectDialog.txtInsRows": "Insertar filas", "SSE.Views.ProtectDialog.txtObjs": "<PERSON><PERSON> objet<PERSON>", "SSE.Views.ProtectDialog.txtOptional": "opcional", "SSE.Views.ProtectDialog.txtPassword": "Contraseña", "SSE.Views.ProtectDialog.txtPivot": "Utilizar tabla y gráfico dinámicos", "SSE.Views.ProtectDialog.txtProtect": "Proteger", "SSE.Views.ProtectDialog.txtRange": "Ra<PERSON>", "SSE.Views.ProtectDialog.txtRangeName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRepeat": "<PERSON><PERSON>r con<PERSON>", "SSE.Views.ProtectDialog.txtScen": "<PERSON>ar es<PERSON>", "SSE.Views.ProtectDialog.txtSelLocked": "Seleccionar celdas bloqueadas", "SSE.Views.ProtectDialog.txtSelUnLocked": "Seleccionar celdas desbloque<PERSON>s", "SSE.Views.ProtectDialog.txtSheetDescription": "Evite los cambios no deseados de otros limitando su capacidad de edición.", "SSE.Views.ProtectDialog.txtSheetTitle": "Proteger hoja", "SSE.Views.ProtectDialog.txtSort": "Ordenar", "SSE.Views.ProtectDialog.txtWarning": "Precaución: Si pierde u olvida su contraseña, no podrá recuperarla. Guárdelo en un lugar seguro.", "SSE.Views.ProtectDialog.txtWBDescription": "Para evitar que otros usuarios vean las hojas de cálculo ocultas, a<PERSON><PERSON>, m<PERSON><PERSON>, eliminen u oculten hojas de cálculo y cambien el nombre de las mismas, puede proteger la estructura de su libro con una contraseña.", "SSE.Views.ProtectDialog.txtWBTitle": "Proteger la estructura del Libro", "SSE.Views.ProtectRangesDlg.guestText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.lockText": "Bloqueado", "SSE.Views.ProtectRangesDlg.textDelete": "Eliminar", "SSE.Views.ProtectRangesDlg.textEdit": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEmpty": "No hay rangos permitidos para la edición.", "SSE.Views.ProtectRangesDlg.textNew": "Nuevo", "SSE.Views.ProtectRangesDlg.textProtect": "Proteger hoja", "SSE.Views.ProtectRangesDlg.textPwd": "Contraseña", "SSE.Views.ProtectRangesDlg.textRange": "Ra<PERSON>", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Rangos desbloqueados por una contraseña cuando la hoja está protegida (esto funciona sólo para las celdas bloqueadas)", "SSE.Views.ProtectRangesDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Este elemento se está editando por otro usuario.", "SSE.Views.ProtectRangesDlg.txtEditRange": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.txtNewRange": "Nuevo rango", "SSE.Views.ProtectRangesDlg.txtNo": "No", "SSE.Views.ProtectRangesDlg.txtTitle": "Permitir a los usuarios editar rangos", "SSE.Views.ProtectRangesDlg.txtYes": "Sí", "SSE.Views.ProtectRangesDlg.warnDelete": "¿Esta seguro/a de que quiere borrar el nombre {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Columnas", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Para eliminar los valores duplicados, seleccione una o más columnas que contienen duplicados.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Mis datos tienen encabezados", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Eliminar duplicados", "SSE.Views.RightMenu.txtCellSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtChartSettings": "Ajustes de gráfico", "SSE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON><PERSON><PERSON> imagen", "SSE.Views.RightMenu.txtParagraphSettings": "<PERSON><PERSON><PERSON><PERSON> de p<PERSON>fo", "SSE.Views.RightMenu.txtPivotSettings": "Ajustes de tabla de pivote", "SSE.Views.RightMenu.txtSettings": "Ajustes comunes", "SSE.Views.RightMenu.txtShapeSettings": "Ajustes de forma", "SSE.Views.RightMenu.txtSignatureSettings": "Configuración de firma", "SSE.Views.RightMenu.txtSlicerSettings": "Ajustes de segmentación de datos", "SSE.Views.RightMenu.txtSparklineSettings": "Ajustes de Sparkline", "SSE.Views.RightMenu.txtTableSettings": "Ajustes de la tabla", "SSE.Views.RightMenu.txtTextArtSettings": "Ajustes de galería de texto", "SSE.Views.ScaleDialog.textAuto": "Auto", "SSE.Views.ScaleDialog.textError": "El valor introducido es incorrecto.", "SSE.Views.ScaleDialog.textFewPages": "p<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textFitTo": "Ajustar a", "SSE.Views.ScaleDialog.textHeight": "Altura", "SSE.Views.ScaleDialog.textManyPages": "p<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textOnePage": "p<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textScaleTo": "Ajustar a", "SSE.Views.ScaleDialog.textTitle": "Ajustes de escala", "SSE.Views.ScaleDialog.textWidth": "<PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "El valor máximo para este campo es {0}", "SSE.Views.SetValueDialog.txtMinText": "El valor mínimo para este campo es {0}", "SSE.Views.ShapeSettings.strBackground": "Color de fondo", "SSE.Views.ShapeSettings.strChange": "Cambiar autoforma", "SSE.Views.ShapeSettings.strColor": "Color", "SSE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strForeground": "Color de primer plano", "SSE.Views.ShapeSettings.strPattern": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strShadow": "Mostrar sombra", "SSE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strStroke": "Lín<PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Opacidad ", "SSE.Views.ShapeSettings.strType": "Type", "SSE.Views.ShapeSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "El valor numérico es incorrecto.<br><PERSON>r favor, introduzca un valor de 0 a 1584 puntos.", "SSE.Views.ShapeSettings.textColor": "Color de relleno", "SSE.Views.ShapeSettings.textDirection": "Dirección ", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON> patr<PERSON>", "SSE.Views.ShapeSettings.textFlip": "Volteo", "SSE.Views.ShapeSettings.textFromFile": "De archivo", "SSE.Views.ShapeSettings.textFromStorage": "Desde almacenamiento", "SSE.Views.ShapeSettings.textFromUrl": "De URL", "SSE.Views.ShapeSettings.textGradient": "Puntos de gradiente", "SSE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textHint270": "Girar 90° a la izquierda", "SSE.Views.ShapeSettings.textHint90": "Girar 90° a la derecha", "SSE.Views.ShapeSettings.textHintFlipH": "Volteo Horizontal", "SSE.Views.ShapeSettings.textHintFlipV": "Volteo Vertical", "SSE.Views.ShapeSettings.textImageTexture": "Imagen o textura", "SSE.Views.ShapeSettings.textLinear": "Lineal", "SSE.Views.ShapeSettings.textNoFill": "<PERSON> relleno", "SSE.Views.ShapeSettings.textOriginalSize": "Tamaño original", "SSE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textPosition": "Posición", "SSE.Views.ShapeSettings.textRadial": "Radial", "SSE.Views.ShapeSettings.textRecentlyUsed": "Usados recientemente", "SSE.Views.ShapeSettings.textRotate90": "Girar 90°", "SSE.Views.ShapeSettings.textRotation": "Rotación", "SSE.Views.ShapeSettings.textSelectImage": "Seleccionar imagen", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStretch": "<PERSON>st<PERSON><PERSON>", "SSE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textTexture": "De textura", "SSE.Views.ShapeSettings.textTile": "Mosaico", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Agregar punto de degradado", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Eliminar gradiente de punto", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "Cartón", "SSE.Views.ShapeSettings.txtDarkFabric": "Tela oscura", "SSE.Views.ShapeSettings.txtGrain": "Grano", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "Papel gris", "SSE.Views.ShapeSettings.txtKnit": "Tejid<PERSON>", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "Sin línea", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Columnas", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Espaciado del texto", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "No mover, ni cambiar tamaño con celdas", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Texto alternativo", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Descripción", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Representación de texto alternativa de la información sobre el objeto visual, que se leerá para las personas con deficiencia visual o deterioro cognitivo para ayudarles a entender mejor, que información contiene la imagen, autoforma, gráfica o tabla.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Autoajustar", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Tam<PERSON>ño inicial", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON><PERSON> inici<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Biselado", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Inferior", "SSE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON> de letra", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Número de columnas", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Tamaño final", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Estilo final", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Plano", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Volteado", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Altura", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontalmente", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Tipo de combinación", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Proporciones constantes", "SSE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON>stilo <PERSON>", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Mover sin cambiar tamaño con celdas", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Permitir que el texto desborde la forma", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Ajustar tamaño de la forma al texto", "SSE.Views.ShapeSettingsAdvanced.textRight": "Derecho", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rotación", "SSE.Views.ShapeSettingsAdvanced.textRound": "Redondeado", "SSE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Ajustar a la celda", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Espacio entre columnas", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Cuadrado", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Cuadro de texto", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Forma - <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTop": "Superior", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Mover y cambiar tamaño con celdas", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Verticalmente", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Grosores y flechas", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Aviso", "SSE.Views.SignatureSettings.strDelete": "Elimine la firma", "SSE.Views.SignatureSettings.strDetails": "Detalles de la firma", "SSE.Views.SignatureSettings.strInvalid": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strRequested": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSetup": "Preparación de la firma", "SSE.Views.SignatureSettings.strSign": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSignature": "Firma", "SSE.Views.SignatureSettings.strSigner": "Firmante", "SSE.Views.SignatureSettings.strValid": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.txtContinueEditing": "<PERSON><PERSON> de todas maneras", "SSE.Views.SignatureSettings.txtEditWarning": "La edición eliminará las firmas de la hoja de cálculo<br>¿Está seguro de que quiere continuar?", "SSE.Views.SignatureSettings.txtRemoveWarning": "¿Desea eliminar esta firma?<br> No se puede deshacer.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Esta hoja de cálculo debe firmarse.", "SSE.Views.SignatureSettings.txtSigned": "Se han agregado firmas válidas a la hoja de cálculo. La hoja de cálculo está protegida contra la edición.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Algunas de las firmas digitales en la hoja de cálculo son inválidas o no se pudieron verificar. La hoja de cálculo está protegida y no se puede editar.", "SSE.Views.SlicerAddDialog.textColumns": "Columnas", "SSE.Views.SlicerAddDialog.txtTitle": "Insertar segmentaciones de datos", "SSE.Views.SlicerSettings.strHideNoData": "Ocultar elementos sin datos", "SSE.Views.SlicerSettings.strIndNoData": "Indicar visualmente los elementos sin datos", "SSE.Views.SlicerSettings.strShowDel": "Mostrar elementos eliminados del origen de datos", "SSE.Views.SlicerSettings.strShowNoData": "Mostrar elementos sin datos al final", "SSE.Views.SlicerSettings.strSorting": "Ordenar y filtrar", "SSE.Views.SlicerSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "SSE.Views.SlicerSettings.textAsc": "Ascendente", "SSE.Views.SlicerSettings.textAZ": "De A a Z", "SSE.Views.SlicerSettings.textButtons": "Botones", "SSE.Views.SlicerSettings.textColumns": "Columnas", "SSE.Views.SlicerSettings.textDesc": "Descendente", "SSE.Views.SlicerSettings.textHeight": "Altura", "SSE.Views.SlicerSettings.textHor": "Horizontal ", "SSE.Views.SlicerSettings.textKeepRatio": "Proporciones constantes", "SSE.Views.SlicerSettings.textLargeSmall": "de mayor a menor", "SSE.Views.SlicerSettings.textLock": "Deshabilitar cambiar tamaño or mover", "SSE.Views.SlicerSettings.textNewOld": "de más recientes a más antiguos", "SSE.Views.SlicerSettings.textOldNew": "de más antiguos a más recientes", "SSE.Views.SlicerSettings.textPosition": "Posición", "SSE.Views.SlicerSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textSmallLarge": "de menor a mayor", "SSE.Views.SlicerSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textVert": "Vertical", "SSE.Views.SlicerSettings.textWidth": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "De Z a A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Botones", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Columnas", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Altura", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Ocultar elementos sin datos", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Indicar visualmente los elementos sin datos", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Referencias", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Mostrar elementos eliminados del origen de datos", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Mostrar encabezado", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Mostrar elementos sin datos al final", "SSE.Views.SlicerSettingsAdvanced.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Ordenar y filtrar", "SSE.Views.SlicerSettingsAdvanced.strStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "<PERSON><PERSON><PERSON> y ta<PERSON>", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "No mover, ni cambiar tamaño con celdas", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Texto alternativo", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Descripción", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Representación de texto alternativa de la información sobre el objeto visual, que se leerá para las personas con deficiencia visual o deterioro cognitivo para ayudarles a entender mejor, que información contiene la imagen, autoforma, gráfica o tabla.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Ascendente", "SSE.Views.SlicerSettingsAdvanced.textAZ": "De A a Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Descendente", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Nombre para utilizar en fórmulas", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Encabezado", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Proporciones constantes", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "de mayor a menor", "SSE.Views.SlicerSettingsAdvanced.textName": "Nombre", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "de más recientes a más antiguos", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "de más antiguos a más recientes", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Mover sin cambiar tamaño con celdas", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "de menor a mayor", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Ajustar a la celda", "SSE.Views.SlicerSettingsAdvanced.textSort": "Ordenar", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Nombre de origen", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Segmentación de datos - Ajustes avanzados", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Mover y cambiar tamaño con celdas", "SSE.Views.SlicerSettingsAdvanced.textZA": "De Z a A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Este campo es obligatorio", "SSE.Views.SortDialog.errorEmpty": "Todos los criterios de clasificación deben tener una columna o fila especificada.", "SSE.Views.SortDialog.errorMoreOneCol": "Se ha seleccionado más de una columna.", "SSE.Views.SortDialog.errorMoreOneRow": "Se ha seleccionado más de una fila.", "SSE.Views.SortDialog.errorNotOriginalCol": "La columna que ha seleccionado no está en el rango original seleccionado.", "SSE.Views.SortDialog.errorNotOriginalRow": "La fila que ha seleccionado no está en el rango seleccionado originalmente. ", "SSE.Views.SortDialog.errorSameColumnColor": "%1 está siendo clasificado por el mismo color más de una vez. Elimine los criterios de clasificación duplicados y vuelva a intentarlo.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 está siendo ordenado por valores más de una vez.<br>Elimine los criterios de clasificación duplicados y vuelva a intentarlo.", "SSE.Views.SortDialog.textAdd": "Agregar nivel", "SSE.Views.SortDialog.textAsc": "Ascendente", "SSE.Views.SortDialog.textAuto": "Automático", "SSE.Views.SortDialog.textAZ": "De A a Z", "SSE.Views.SortDialog.textBelow": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textCellColor": "Color de la celda", "SSE.Views.SortDialog.textColumn": "Columna", "SSE.Views.SortDialog.textCopy": "<PERSON><PERSON><PERSON> nivel", "SSE.Views.SortDialog.textDelete": "Eliminar nivel", "SSE.Views.SortDialog.textDesc": "Descendente", "SSE.Views.SortDialog.textDown": "Mover el nivel hacia abajo", "SSE.Views.SortDialog.textFontColor": "Color de letra", "SSE.Views.SortDialog.textLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textMoreCols": "(<PERSON><PERSON><PERSON>...)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON><PERSON>...)", "SSE.Views.SortDialog.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOptions": "Opciones", "SSE.Views.SortDialog.textOrder": "Ordenar", "SSE.Views.SortDialog.textRight": "Derecho", "SSE.Views.SortDialog.textRow": "<PERSON><PERSON>", "SSE.Views.SortDialog.textSort": "Ordenar según", "SSE.Views.SortDialog.textSortBy": "Ordenar por", "SSE.Views.SortDialog.textThenBy": "<PERSON><PERSON> por", "SSE.Views.SortDialog.textTop": "Superior", "SSE.Views.SortDialog.textUp": "Mover el nivel hacia arriba", "SSE.Views.SortDialog.textValues": "Valores", "SSE.Views.SortDialog.textZA": "De Z a A", "SSE.Views.SortDialog.txtInvalidRange": "<PERSON><PERSON> de celdas inválido.", "SSE.Views.SortDialog.txtTitle": "Ordenar", "SSE.Views.SortFilterDialog.textAsc": "Ascendente (de A a Z) por", "SSE.Views.SortFilterDialog.textDesc": "Descendente (de Z a A) por", "SSE.Views.SortFilterDialog.txtTitle": "Ordenar", "SSE.Views.SortOptionsDialog.textCase": "Sensible a las mayúsculas y minúsculas", "SSE.Views.SortOptionsDialog.textHeaders": "Mis datos tienen encabezados", "SSE.Views.SortOptionsDialog.textLeftRight": "Ordenar de izquierda a derecha", "SSE.Views.SortOptionsDialog.textOrientation": "Orientación ", "SSE.Views.SortOptionsDialog.textTitle": "Opciones de ordenación", "SSE.Views.SortOptionsDialog.textTopBottom": "Ordenar de arriba hacia abajo", "SSE.Views.SpecialPasteDialog.textAdd": "Agregar", "SSE.Views.SpecialPasteDialog.textAll": "Todo", "SSE.Views.SpecialPasteDialog.textBlanks": "Saltar blancos", "SSE.Views.SpecialPasteDialog.textColWidth": "Anchos de columna", "SSE.Views.SpecialPasteDialog.textComments": "Comentarios", "SSE.Views.SpecialPasteDialog.textDiv": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFFormat": "Fórmulas y formato", "SSE.Views.SpecialPasteDialog.textFNFormat": "Fórmulas y formatos de número", "SSE.Views.SpecialPasteDialog.textFormats": "Formatos", "SSE.Views.SpecialPasteDialog.textFormulas": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.SpecialPasteDialog.textFWidth": "Fórmulas y anchos de columna", "SSE.Views.SpecialPasteDialog.textMult": "Multiplicar", "SSE.Views.SpecialPasteDialog.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textOperation": "Operación", "SSE.Views.SpecialPasteDialog.textPaste": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textSub": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textTitle": "Pegado especial", "SSE.Views.SpecialPasteDialog.textTranspose": "Transponer", "SSE.Views.SpecialPasteDialog.textValues": "Valores", "SSE.Views.SpecialPasteDialog.textVFormat": "Valores y formato", "SSE.Views.SpecialPasteDialog.textVNFormat": "Valores y formatos de número", "SSE.Views.SpecialPasteDialog.textWBorders": "<PERSON>do excepto bordes", "SSE.Views.Spellcheck.noSuggestions": "No hay sugerencias", "SSE.Views.Spellcheck.textChange": "Cambiar", "SSE.Views.Spellcheck.textChangeAll": "<PERSON><PERSON><PERSON> todo", "SSE.Views.Spellcheck.textIgnore": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnoreAll": "<PERSON><PERSON><PERSON> todo", "SSE.Views.Spellcheck.txtAddToDictionary": "Agregar al diccionario", "SSE.Views.Spellcheck.txtClosePanel": "Ce<PERSON>r corrección ortográfica", "SSE.Views.Spellcheck.txtComplete": "La corrección ortográfica ha sido completada", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Idioma del diccionario", "SSE.Views.Spellcheck.txtNextTip": "Ir a la siguiente palabra", "SSE.Views.Spellcheck.txtSpelling": "Ortografía", "SSE.Views.Statusbar.CopyDialog.itemCopyToEnd": "(Copiar al final)", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Mover al final)", "SSE.Views.Statusbar.CopyDialog.textCopyBefore": "Copiar antes de hoja", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "<PERSON><PERSON><PERSON><PERSON> de hoja", "SSE.Views.Statusbar.filteredRecordsText": "Registros filtrados: {0} de {1}", "SSE.Views.Statusbar.filteredText": "<PERSON>do de filtro", "SSE.Views.Statusbar.itemAverage": "Promedio", "SSE.Views.Statusbar.itemCopy": "Copiar", "SSE.Views.Statusbar.itemCount": "Contar", "SSE.Views.Statusbar.itemDelete": "Eliminar", "SSE.Views.Statusbar.itemHidden": "Ocultado", "SSE.Views.Statusbar.itemHide": "Ocultar", "SSE.Views.Statusbar.itemInsert": "Insertar", "SSE.Views.Statusbar.itemMaximum": "Máximo", "SSE.Views.Statusbar.itemMinimum": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMove": "Mover", "SSE.Views.Statusbar.itemProtect": "Proteger", "SSE.Views.Statusbar.itemRename": "Cambiar nombre", "SSE.Views.Statusbar.itemStatus": "Guardando estado", "SSE.Views.Statusbar.itemSum": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemTabColor": "Color de tab", "SSE.Views.Statusbar.itemUnProtect": "Quitar la protección", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Hoja de Cálculo con tal nombre ya existe", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "El nombre de una hoja no puede contener los siguientes caracteres \\/*?[]: o el carácter ' como primer o último carácter", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Nombre de hoja", "SSE.Views.Statusbar.selectAllSheets": "Seleccionar todas las hojas", "SSE.Views.Statusbar.sheetIndexText": "Hoja {0} de {1}", "SSE.Views.Statusbar.textAverage": "Promedio", "SSE.Views.Statusbar.textCount": "Contar", "SSE.Views.Statusbar.textMax": "Máx.", "SSE.Views.Statusbar.textMin": "<PERSON><PERSON>.", "SSE.Views.Statusbar.textNewColor": "Color personalizado", "SSE.Views.Statusbar.textNoColor": "Sin color", "SSE.Views.Statusbar.textSum": "<PERSON><PERSON>", "SSE.Views.Statusbar.tipAddTab": "Agregar hoja de cálculo", "SSE.Views.Statusbar.tipFirst": "<PERSON><PERSON><PERSON><PERSON> hasta la primera hoja", "SSE.Views.Statusbar.tipLast": "<PERSON><PERSON><PERSON><PERSON> hasta la última hoja", "SSE.Views.Statusbar.tipListOfSheets": "Lista de hojas", "SSE.Views.Statusbar.tipNext": "Desplazar la lista de hoja a la derecha", "SSE.Views.Statusbar.tipPrev": "Desplazar la lista de hoja a la izquierda", "SSE.Views.Statusbar.tipZoomFactor": "Zoom", "SSE.Views.Statusbar.tipZoomIn": "Acercar", "SSE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.ungroupSheets": "Desag<PERSON><PERSON> hojas", "SSE.Views.Statusbar.zoomText": "Zoom {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "No se puede realizar la operación para el rango de celdas seleccionado.<br>Seleccione un rango de datos uniforme diferente del existente y vuelva a intentarlo.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "La operación no se pudo completar para el rango de celdas seleccionado.<br>Seleccione un rango de modo que la primera fila de la tabla esté en la misma fila <br> y la tabla resultante se superponga a la actual.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "La operación no se pudo completar para el rango de celdas seleccionado.<br>Seleccione un rango que no incluye otras tablas.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Fórmulas de matriz con celdas múltiples no están permitidas en tablas.", "SSE.Views.TableOptionsDialog.txtEmpty": "Este campo es obligatorio", "SSE.Views.TableOptionsDialog.txtFormat": "Crear tabla", "SSE.Views.TableOptionsDialog.txtInvalidRange": "¡ERROR!¡Rango de celdas inválido! ", "SSE.Views.TableOptionsDialog.txtNote": "Los encabezados deben permanecer en la misma fila y el rango de la tabla resultante debe superponerse sobre el rango de la tabla original.", "SSE.Views.TableOptionsDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteColumnText": "Bo<PERSON>r columna", "SSE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON> fila", "SSE.Views.TableSettings.deleteTableText": "Borrar tabla", "SSE.Views.TableSettings.insertColumnLeftText": "Insertar columna a la izquierda", "SSE.Views.TableSettings.insertColumnRightText": "Insertar columna a la derecha", "SSE.Views.TableSettings.insertRowAboveText": "Insertar fila arriba", "SSE.Views.TableSettings.insertRowBelowText": "Insertar fila abajo", "SSE.Views.TableSettings.notcriticalErrorTitle": "Aviso", "SSE.Views.TableSettings.selectColumnText": "Seleccionar toda la columna", "SSE.Views.TableSettings.selectDataText": "Seleccionar da<PERSON> de columna", "SSE.Views.TableSettings.selectRowText": "Seleccionar fila", "SSE.Views.TableSettings.selectTableText": "Seleccionar tabla", "SSE.Views.TableSettings.textActions": "Acciones de tabla", "SSE.Views.TableSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "SSE.Views.TableSettings.textBanded": "Con bandas", "SSE.Views.TableSettings.textColumns": "Columnas", "SSE.Views.TableSettings.textConvertRange": "Convertir al intervalo ", "SSE.Views.TableSettings.textEdit": "Filas y columnas", "SSE.Views.TableSettings.textEmptyTemplate": "Sin plantillas", "SSE.Views.TableSettings.textExistName": "¡ERROR! Una gama con tal nombre ya existe", "SSE.Views.TableSettings.textFilter": "Botón de filtro", "SSE.Views.TableSettings.textFirst": "primero", "SSE.Views.TableSettings.textHeader": "Encabezado", "SSE.Views.TableSettings.textInvalidName": "¡ERROR! nombre de la tabla inválido", "SSE.Views.TableSettings.textIsLocked": "Este elemento está editándose por otro usuario.", "SSE.Views.TableSettings.textLast": "Último", "SSE.Views.TableSettings.textLongOperation": "Operación larga", "SSE.Views.TableSettings.textPivot": "Insertar tabla dinámica", "SSE.Views.TableSettings.textRemDuplicates": "Eliminar duplicados", "SSE.Views.TableSettings.textReservedName": "El nombre que está tratando de usar ya se hace referencia en las fórmulas de celda. Por favor seleccione otro nombre.", "SSE.Views.TableSettings.textResize": "Tamaño de tabla", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSlicer": "Insertar segmentación de datos", "SSE.Views.TableSettings.textTableName": "Nombre de la tabla", "SSE.Views.TableSettings.textTemplate": "Seleccionar de plantilla", "SSE.Views.TableSettings.textTotal": "Total", "SSE.Views.TableSettings.warnLongOperation": "La operación que está a punto de realizar podría tomar mucho tiempo para completar.<br>¿Está seguro que desea continuar?", "SSE.Views.TableSettingsAdvanced.textAlt": "Texto alternativo", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Descripción", "SSE.Views.TableSettingsAdvanced.textAltTip": "Representación de texto alternativa de la información sobre el objeto visual, que se leerá para las personas con deficiencia visual o deterioro cognitivo para ayudarles a entender mejor, que información contiene la imagen, autoforma, gráfico o tabla.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textTitle": "Tabla - <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Custom": "Personalizado", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Dark": "Oscuro", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Light": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.txtGroupTable_Medium": "Medio", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleDark": "Estilo de tabla oscuro", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleLight": "Estilo de tabla claro", "SSE.Views.TableSettingsAdvanced.txtTable_TableStyleMedium": "Estilo de tabla medio", "SSE.Views.TextArtSettings.strBackground": "Color de fondo", "SSE.Views.TextArtSettings.strColor": "Color", "SSE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strForeground": "Color de primer plano", "SSE.Views.TextArtSettings.strPattern": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strStroke": "Lín<PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Opacidad ", "SSE.Views.TextArtSettings.strType": "Type", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "El valor numérico es incorrecto.<br><PERSON>r favor, introduzca un valor de 0 a 1584 puntos.", "SSE.Views.TextArtSettings.textColor": "Color de relleno", "SSE.Views.TextArtSettings.textDirection": "Direction", "SSE.Views.TextArtSettings.textEmptyPattern": "No Pattern", "SSE.Views.TextArtSettings.textFromFile": "De archivo", "SSE.Views.TextArtSettings.textFromUrl": "De URL", "SSE.Views.TextArtSettings.textGradient": "Puntos de gradiente", "SSE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textImageTexture": "Imagen o textura", "SSE.Views.TextArtSettings.textLinear": "Lineal", "SSE.Views.TextArtSettings.textNoFill": "<PERSON> relleno", "SSE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textPosition": "Posición", "SSE.Views.TextArtSettings.textRadial": "Radial", "SSE.Views.TextArtSettings.textSelectTexture": "Select", "SSE.Views.TextArtSettings.textStretch": "<PERSON>st<PERSON><PERSON>", "SSE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTemplate": "Plantilla", "SSE.Views.TextArtSettings.textTexture": "De textura", "SSE.Views.TextArtSettings.textTile": "Mosaico", "SSE.Views.TextArtSettings.textTransform": "Transformar", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Agregar punto de degradado", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Eliminar gradiente de punto", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "Cartón", "SSE.Views.TextArtSettings.txtDarkFabric": "Tela oscura", "SSE.Views.TextArtSettings.txtGrain": "Grano", "SSE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "Papel gris", "SSE.Views.TextArtSettings.txtKnit": "Tejid<PERSON>", "SSE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtNoBorders": "Sin línea", "SSE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtWood": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnAddComment": "Agregar comentario", "SSE.Views.Toolbar.capBtnColorSchemas": "Combinación de colores", "SSE.Views.Toolbar.capBtnComment": "Comentario", "SSE.Views.Toolbar.capBtnInsHeader": "Encabezado/Pie de página", "SSE.Views.Toolbar.capBtnInsSlicer": "Segmentación de datos", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Símbolo", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageOrient": "Orientación ", "SSE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintArea": "Área de impresión", "SSE.Views.Toolbar.capBtnPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnScale": "Ajustar área de impresión", "SSE.Views.Toolbar.capImgAlign": "Alineación", "SSE.Views.Toolbar.capImgBackward": "Enviar hacia atrás", "SSE.Views.Toolbar.capImgForward": "<PERSON><PERSON><PERSON> adelante", "SSE.Views.Toolbar.capImgGroup": "Grupo", "SSE.Views.Toolbar.capInsertChart": "Diagrama", "SSE.Views.Toolbar.capInsertEquation": "Ecuación", "SSE.Views.Toolbar.capInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertImage": "Imagen", "SSE.Views.Toolbar.capInsertShape": "Forma", "SSE.Views.Toolbar.capInsertSpark": "Minigráfico", "SSE.Views.Toolbar.capInsertTable": "Tabla", "SSE.Views.Toolbar.capInsertText": "Cuadro de texto", "SSE.Views.Toolbar.capInsertTextart": "Galería de texto", "SSE.Views.Toolbar.mniImageFromFile": "Imagen desde archivo", "SSE.Views.Toolbar.mniImageFromStorage": "Imagen de Almacenamiento", "SSE.Views.Toolbar.mniImageFromUrl": "Imagen desde url", "SSE.Views.Toolbar.textAddPrintArea": "Agregar al área de impresión", "SSE.Views.Toolbar.textAlignBottom": "<PERSON><PERSON>r abajo", "SSE.Views.Toolbar.textAlignCenter": "Alinear al centro", "SSE.Views.Toolbar.textAlignJust": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignLeft": "Alinear a la izquierda", "SSE.Views.Toolbar.textAlignMiddle": "Alinear al medio", "SSE.Views.Toolbar.textAlignRight": "Alinear a la derecha", "SSE.Views.Toolbar.textAlignTop": "Alinear arriba", "SSE.Views.Toolbar.textAllBorders": "Todos los bordes", "SSE.Views.Toolbar.textAuto": "Auto", "SSE.Views.Toolbar.textAutoColor": "Automático", "SSE.Views.Toolbar.textBold": "Negrita", "SSE.Views.Toolbar.textBordersColor": "Color de borde", "SSE.Views.Toolbar.textBordersStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBottom": "Inferior: ", "SSE.Views.Toolbar.textBottomBorders": "Bordes inferiores", "SSE.Views.Toolbar.textCenterBorders": "Bordes verticales internos", "SSE.Views.Toolbar.textClearPrintArea": "Borrar área de impresión", "SSE.Views.Toolbar.textClearRule": "<PERSON><PERSON><PERSON> reglas", "SSE.Views.Toolbar.textClockwise": "<PERSON><PERSON>ulo descendente", "SSE.Views.Toolbar.textColorScales": "Escalas de color", "SSE.Views.Toolbar.textCounterCw": "<PERSON><PERSON><PERSON> ascendente", "SSE.Views.Toolbar.textCustom": "Personalizado", "SSE.Views.Toolbar.textDataBars": "Barras de datos", "SSE.Views.Toolbar.textDelLeft": "Desplazar celdas a la izquierda", "SSE.Views.Toolbar.textDelUp": "<PERSON><PERSON><PERSON><PERSON> celdas hacia arriba", "SSE.Views.Toolbar.textDiagDownBorder": "Borde diagonal descendente", "SSE.Views.Toolbar.textDiagUpBorder": "Borde diagonal ascendente", "SSE.Views.Toolbar.textDone": "<PERSON><PERSON>", "SSE.Views.Toolbar.textEditVA": "<PERSON><PERSON>", "SSE.Views.Toolbar.textEntireCol": "Toda la columna", "SSE.Views.Toolbar.textEntireRow": "Toda la fila", "SSE.Views.Toolbar.textFewPages": "p<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textHeight": "Altura", "SSE.Views.Toolbar.textHideVA": "Ocultar á<PERSON> visible", "SSE.Views.Toolbar.textHorizontal": "Texto horizontal", "SSE.Views.Toolbar.textInsDown": "<PERSON><PERSON><PERSON><PERSON> celdas hacia abajo", "SSE.Views.Toolbar.textInsideBorders": "Bordes internos", "SSE.Views.Toolbar.textInsRight": "Desp<PERSON>zar celdas a la derecha", "SSE.Views.Toolbar.textItalic": "Cursiva", "SSE.Views.Toolbar.textItems": "Elementos", "SSE.Views.Toolbar.textLandscape": "Horizontal", "SSE.Views.Toolbar.textLeft": "Izquierdo: ", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON> izqui<PERSON>", "SSE.Views.Toolbar.textManageRule": "Admini<PERSON><PERSON> reglas", "SSE.Views.Toolbar.textManyPages": "p<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsLast": "Último personalizado", "SSE.Views.Toolbar.textMarginsNarrow": "Estrecho", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "Amplio", "SSE.Views.Toolbar.textMiddleBorders": "Bordes horizontales internos", "SSE.Views.Toolbar.textMoreFormats": "Otros formatos", "SSE.Views.Toolbar.textMorePages": "<PERSON>ás páginas", "SSE.Views.Toolbar.textNewColor": "Color personalizado", "SSE.Views.Toolbar.textNewRule": "Nueva regla", "SSE.Views.Toolbar.textNoBorders": "<PERSON> bordes", "SSE.Views.Toolbar.textOnePage": "p<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textOutBorders": "Bordes externos", "SSE.Views.Toolbar.textPageMarginsCustom": "<PERSON><PERSON><PERSON><PERSON> personaliza<PERSON>", "SSE.Views.Toolbar.textPortrait": "Vertical", "SSE.Views.Toolbar.textPrint": "Imprimir", "SSE.Views.Toolbar.textPrintGridlines": "<PERSON><PERSON><PERSON><PERSON> cuadric<PERSON>", "SSE.Views.Toolbar.textPrintHeadings": "Imprimir <PERSON>", "SSE.Views.Toolbar.textPrintOptions": "Opciones de impresión", "SSE.Views.Toolbar.textRight": "Derecho: ", "SSE.Views.Toolbar.textRightBorders": "Bordes derechos", "SSE.Views.Toolbar.textRotateDown": "<PERSON><PERSON><PERSON> texto hacia abajo", "SSE.Views.Toolbar.textRotateUp": "<PERSON><PERSON>r texto hacia arriba", "SSE.Views.Toolbar.textScale": "Escala", "SSE.Views.Toolbar.textScaleCustom": "Personalizado", "SSE.Views.Toolbar.textSelection": "Desde la selección actual", "SSE.Views.Toolbar.textSetPrintArea": "Establecer <PERSON> de impresión", "SSE.Views.Toolbar.textShowVA": "Muestra el área visible", "SSE.Views.Toolbar.textStrikeout": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSubscript": "Subíndice", "SSE.Views.Toolbar.textSubSuperscript": "Subíndice/superíndice", "SSE.Views.Toolbar.textSuperscript": "Sobreíndice", "SSE.Views.Toolbar.textTabCollaboration": "Colaboración", "SSE.Views.Toolbar.textTabData": "Datos", "SSE.Views.Toolbar.textTabFile": "Archivo", "SSE.Views.Toolbar.textTabFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabHome": "<PERSON><PERSON>o", "SSE.Views.Toolbar.textTabInsert": "Insertar", "SSE.Views.Toolbar.textTabLayout": "Diseño", "SSE.Views.Toolbar.textTabProtect": "Protección", "SSE.Views.Toolbar.textTabView": "Vista", "SSE.Views.Toolbar.textThisPivot": "Desde esta tabla pivote", "SSE.Views.Toolbar.textThisSheet": "Desde esta hoja de cálculo", "SSE.Views.Toolbar.textThisTable": "Desde esta tabla", "SSE.Views.Toolbar.textTop": "Superior:", "SSE.Views.Toolbar.textTopBorders": "Bordes superiores", "SSE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textVertical": "Texto vertical", "SSE.Views.Toolbar.textWidth": "<PERSON><PERSON>", "SSE.Views.Toolbar.textZoom": "Zoom", "SSE.Views.Toolbar.tipAlignBottom": "Alinear en la parte inferior", "SSE.Views.Toolbar.tipAlignCenter": "Alinear al centro", "SSE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignLeft": "Alinear a la izquierda", "SSE.Views.Toolbar.tipAlignMiddle": "Alinear al medio", "SSE.Views.Toolbar.tipAlignRight": "Alinear a la derecha", "SSE.Views.Toolbar.tipAlignTop": "Alinear en la parte superior", "SSE.Views.Toolbar.tipAutofilter": "Ordenar y filtrar", "SSE.Views.Toolbar.tipBack": "Atrás", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipChangeChart": "Cambiar tipo de gráfico", "SSE.Views.Toolbar.tipClearStyle": "Limpiar", "SSE.Views.Toolbar.tipColorSchemas": "Cambiar combinación de colores", "SSE.Views.Toolbar.tipCondFormat": "Formato condicional", "SSE.Views.Toolbar.tipCopy": "Copiar", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> est<PERSON>", "SSE.Views.Toolbar.tipCut": "Cortar", "SSE.Views.Toolbar.tipDecDecimal": "Disminuir decimales", "SSE.Views.Toolbar.tipDecFont": "<PERSON><PERSON><PERSON> ta<PERSON> de <PERSON>ra", "SSE.Views.Toolbar.tipDeleteOpt": "Eliminar celdas", "SSE.Views.Toolbar.tipDigStyleAccounting": "Estilo de la Contabilidad", "SSE.Views.Toolbar.tipDigStyleCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStylePercent": "<PERSON>st<PERSON> por ciento", "SSE.Views.Toolbar.tipEditChart": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipEditChartData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipEditChartType": "Cambiar tipo de gráfico", "SSE.Views.Toolbar.tipEditHeader": "Editar encabezado o pie de página", "SSE.Views.Toolbar.tipFontColor": "Color de letra", "SSE.Views.Toolbar.tipFontName": "Letra ", "SSE.Views.Toolbar.tipFontSize": "Tam<PERSON>ño de <PERSON>", "SSE.Views.Toolbar.tipHAlighOle": "Alineación horizontal", "SSE.Views.Toolbar.tipImgAlign": "Alinear objetos", "SSE.Views.Toolbar.tipImgGroup": "Agrupar objetos", "SSE.Views.Toolbar.tipIncDecimal": "Aumentar decimales", "SSE.Views.Toolbar.tipIncFont": "Aumentar tamaño de letra", "SSE.Views.Toolbar.tipInsertChart": "Insertar gráfico", "SSE.Views.Toolbar.tipInsertChartSpark": "Insertar gráfico", "SSE.Views.Toolbar.tipInsertEquation": "Insertar ecuación", "SSE.Views.Toolbar.tipInsertHorizontalText": "Insertar cuadro de texto horizontal", "SSE.Views.Toolbar.tipInsertHyperlink": "Agre<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertImage": "Insertar imagen", "SSE.Views.Toolbar.tipInsertOpt": "Insertar celdas", "SSE.Views.Toolbar.tipInsertShape": "Insertar autoforma", "SSE.Views.Toolbar.tipInsertSlicer": "Insertar segmentación de datos", "SSE.Views.Toolbar.tipInsertSmartArt": "Insertar SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Insertar minigráfico", "SSE.Views.Toolbar.tipInsertSymbol": "Insertar symboló", "SSE.Views.Toolbar.tipInsertTable": "Insertar tabla", "SSE.Views.Toolbar.tipInsertText": "Insertar cuadro de texto", "SSE.Views.Toolbar.tipInsertTextart": "Insertar Galería de Texto", "SSE.Views.Toolbar.tipInsertVerticalText": "Insertar cuadro de texto vertical", "SSE.Views.Toolbar.tipMerge": "Combinar y centrar", "SSE.Views.Toolbar.tipNone": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipNumFormat": "Formato de número", "SSE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPageOrient": "Orientación de página", "SSE.Views.Toolbar.tipPageSize": "Tamaño de página", "SSE.Views.Toolbar.tipPaste": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrColor": "Color de relleno", "SSE.Views.Toolbar.tipPrint": "Imprimir", "SSE.Views.Toolbar.tipPrintArea": "Área de impresión", "SSE.Views.Toolbar.tipPrintQuick": "Impresión rápida", "SSE.Views.Toolbar.tipPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSave": "Guardar", "SSE.Views.Toolbar.tipSaveCoauth": "Guarde los cambios para que otros usuarios los puedan ver.", "SSE.Views.Toolbar.tipScale": "Ajustar área de impresión", "SSE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "SSE.Views.Toolbar.tipSendBackward": "Enviar hacia atrás", "SSE.Views.Toolbar.tipSendForward": "<PERSON><PERSON><PERSON> adelante", "SSE.Views.Toolbar.tipSynchronize": "El documento ha sido cambiado por otro usuario. Por favor haga clic para guardar sus cambios y recargue las actualizaciones.", "SSE.Views.Toolbar.tipTextFormatting": "Más herramientas de formato de texto", "SSE.Views.Toolbar.tipTextOrientation": "Orientación ", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipVAlighOle": "Alineación vertical", "SSE.Views.Toolbar.tipVisibleArea": "Área visible", "SSE.Views.Toolbar.tipWrap": "Ajustar texto", "SSE.Views.Toolbar.txtAccounting": "Contabilidad", "SSE.Views.Toolbar.txtAdditional": "Adicional", "SSE.Views.Toolbar.txtAscending": "Ascendente", "SSE.Views.Toolbar.txtAutosumTip": "Sumatoria", "SSE.Views.Toolbar.txtCellStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearAll": "Todo", "SSE.Views.Toolbar.txtClearComments": "Comentarios", "SSE.Views.Toolbar.txtClearFilter": "Limpiar filtro", "SSE.Views.Toolbar.txtClearFormat": "Formato", "SSE.Views.Toolbar.txtClearFormula": "Función", "SSE.Views.Toolbar.txtClearHyper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearText": "Texto", "SSE.Views.Toolbar.txtCurrency": "Moneda", "SSE.Views.Toolbar.txtCustom": "Personalizado", "SSE.Views.Toolbar.txtDate": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtDateTime": "<PERSON><PERSON> y hora", "SSE.Views.Toolbar.txtDescending": "Descendente", "SSE.Views.Toolbar.txtDollar": "$ Dólar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Exponencial", "SSE.Views.Toolbar.txtFilter": "Filtro", "SSE.Views.Toolbar.txtFormula": "Insertar función", "SSE.Views.Toolbar.txtFraction": "Fracción", "SSE.Views.Toolbar.txtFranc": "CHF Franco Suizo", "SSE.Views.Toolbar.txtGeneral": "General", "SSE.Views.Toolbar.txtInteger": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtManageRange": "Administrador de nombre", "SSE.Views.Toolbar.txtMergeAcross": "Combinar horizontalmente", "SSE.Views.Toolbar.txtMergeCells": "Comb<PERSON><PERSON> celdas", "SSE.Views.Toolbar.txtMergeCenter": "Unir y centrar", "SSE.Views.Toolbar.txtNamedRange": "Bandas nombradas", "SSE.Views.Toolbar.txtNewRange": "Definir nombre", "SSE.Views.Toolbar.txtNoBorders": "<PERSON> bordes", "SSE.Views.Toolbar.txtNumber": "Número", "SSE.Views.Toolbar.txtPasteRange": "<PERSON><PERSON>ar nombre", "SSE.Views.Toolbar.txtPercentage": "Po<PERSON>entaj<PERSON>", "SSE.Views.Toolbar.txtPound": "£ Libra", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON>lo", "SSE.Views.Toolbar.txtScheme1": "Oficina", "SSE.Views.Toolbar.txtScheme10": "Intermedio", "SSE.Views.Toolbar.txtScheme11": "Metro", "SSE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme13": "Opulento", "SSE.Views.Toolbar.txtScheme14": "Mirador", "SSE.Views.Toolbar.txtScheme15": "Origen", "SSE.Views.Toolbar.txtScheme16": "Papel", "SSE.Views.Toolbar.txtScheme17": "Solsticio", "SSE.Views.Toolbar.txtScheme18": "Técnico", "SSE.Views.Toolbar.txtScheme19": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScheme2": "Escala de grises", "SSE.Views.Toolbar.txtScheme20": "Urbano", "SSE.Views.Toolbar.txtScheme21": "Brío", "SSE.Views.Toolbar.txtScheme22": "Nueva oficina", "SSE.Views.Toolbar.txtScheme3": "Vértice", "SSE.Views.Toolbar.txtScheme4": "Aspect<PERSON>", "SSE.Views.Toolbar.txtScheme5": "Civil", "SSE.Views.Toolbar.txtScheme6": "Concurrencia", "SSE.Views.Toolbar.txtScheme7": "Equidad ", "SSE.Views.Toolbar.txtScheme8": "F<PERSON>jo", "SSE.Views.Toolbar.txtScheme9": "Fundición", "SSE.Views.Toolbar.txtScientific": "Scientífico", "SSE.Views.Toolbar.txtSearch": "Buscar", "SSE.Views.Toolbar.txtSort": "Ordenar", "SSE.Views.Toolbar.txtSortAZ": "Clasificar por el orden ascendente", "SSE.Views.Toolbar.txtSortZA": "Clasificar por el orden descendente", "SSE.Views.Toolbar.txtSpecial": "Especial", "SSE.Views.Toolbar.txtTableTemplate": "Formatear como plantilla de tabla", "SSE.Views.Toolbar.txtText": "Texto", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtUnmerge": "<PERSON><PERSON><PERSON> celdas", "SSE.Views.Toolbar.txtYen": "¥ Yen", "SSE.Views.Top10FilterDialog.textType": "Mostrar", "SSE.Views.Top10FilterDialog.txtBottom": "Inferior", "SSE.Views.Top10FilterDialog.txtBy": "por", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "<PERSON>r ciento", "SSE.Views.Top10FilterDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 de Autofiltro", "SSE.Views.Top10FilterDialog.txtTop": "Superior", "SSE.Views.Top10FilterDialog.txtValueTitle": "Filtro de los 10 mejores", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Ajustes de campo de valor", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Promedio", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Campo base", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Elemento base", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 de %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Contar", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Contar nú<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Nombre personalizado", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Diferencia de", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Máx.", "SSE.Views.ValueFieldSettingsDialog.txtMin": "<PERSON><PERSON>.", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "Sin cálculo", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Porcentaje de", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Diferencia de porcentaje de", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Porcentaje de columnas", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRow": "Porcentaje del total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "Porcentaje de <PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Producto", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Total en", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Mostrar valores como", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Nombre de origen:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "<PERSON><PERSON><PERSON> campo de valor por", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.guestText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.lockText": "Bloqueado", "SSE.Views.ViewManagerDlg.textDelete": "Eliminar", "SSE.Views.ViewManagerDlg.textDuplicate": "Duplicar", "SSE.Views.ViewManagerDlg.textEmpty": "Aún no se han creado vistas.", "SSE.Views.ViewManagerDlg.textGoTo": "Ir a vista", "SSE.Views.ViewManagerDlg.textLongName": "Escriba un nombre que tenga menos de 128 caracteres.", "SSE.Views.ViewManagerDlg.textNew": "Nuevo", "SSE.Views.ViewManagerDlg.textRename": "Cambiar nombre", "SSE.Views.ViewManagerDlg.textRenameError": "El nombre de la vista no debe estar vacío.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Cambiar el nombre de la vista", "SSE.Views.ViewManagerDlg.textViews": "Vistas de hoja", "SSE.Views.ViewManagerDlg.tipIsLocked": "Este elemento está siendo editado por otro usuario.", "SSE.Views.ViewManagerDlg.txtTitle": "Administrador de vista de hoja", "SSE.Views.ViewManagerDlg.warnDeleteView": "Está tratando de eliminar la vista actualmente habilitada '%1'. ¿Cerrar esta vista y eliminarla?", "SSE.Views.ViewTab.capBtnFreeze": "<PERSON><PERSON>ar paneles", "SSE.Views.ViewTab.capBtnSheetView": "Vista de hoja", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Mostrar siempre la barra de herramientas", "SSE.Views.ViewTab.textClose": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Combinar las barras de hoja y de estado", "SSE.Views.ViewTab.textCreate": "Nuevo", "SSE.Views.ViewTab.textDefault": "Predeterminado", "SSE.Views.ViewTab.textFormula": "Barra de fórmulas", "SSE.Views.ViewTab.textFreezeCol": "Inmovilizar primera columna", "SSE.Views.ViewTab.textFreezeRow": "<PERSON><PERSON><PERSON>zar fila superior", "SSE.Views.ViewTab.textGridlines": "Líneas de cuadrícula", "SSE.Views.ViewTab.textHeadings": "Encabezados", "SSE.Views.ViewTab.textInterfaceTheme": "Tema de la interfaz", "SSE.Views.ViewTab.textLeftMenu": "Panel izquierdo", "SSE.Views.ViewTab.textManager": "Administrador de vista", "SSE.Views.ViewTab.textRightMenu": "Panel derecho", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Mostrar la sombra de paneles inmovilizados", "SSE.Views.ViewTab.textUnFreeze": "<PERSON><PERSON><PERSON> paneles", "SSE.Views.ViewTab.textZeros": "Mostrar ceros", "SSE.Views.ViewTab.textZoom": "Zoom", "SSE.Views.ViewTab.tipClose": "Cerrar vista de hoja", "SSE.Views.ViewTab.tipCreate": "Crear vista de hoja", "SSE.Views.ViewTab.tipFreeze": "<PERSON><PERSON>ar paneles", "SSE.Views.ViewTab.tipInterfaceTheme": "Tema de la interfaz", "SSE.Views.ViewTab.tipSheetView": "Vista de hoja", "SSE.Views.WatchDialog.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textAdd": "Agregar inspección", "SSE.Views.WatchDialog.textBook": "Libro", "SSE.Views.WatchDialog.textCell": "<PERSON><PERSON>", "SSE.Views.WatchDialog.textDelete": "Eliminar inspección", "SSE.Views.WatchDialog.textDeleteAll": "<PERSON><PERSON><PERSON> todo", "SSE.Views.WatchDialog.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textName": "Nombre", "SSE.Views.WatchDialog.textSheet": "Hoja", "SSE.Views.WatchDialog.textValue": "Valor", "SSE.Views.WatchDialog.txtTitle": "Ventana Inspección", "SSE.Views.WBProtection.hintAllowRanges": "<PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.hintProtectSheet": "Proteger hoja", "SSE.Views.WBProtection.hintProtectWB": "Proteger libro", "SSE.Views.WBProtection.txtAllowRanges": "<PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtHiddenFormula": "Fórmulas ocultas", "SSE.Views.WBProtection.txtLockedCell": "<PERSON><PERSON> blo<PERSON>", "SSE.Views.WBProtection.txtLockedShape": "Forma bloqueada", "SSE.Views.WBProtection.txtLockedText": "Bloquear texto", "SSE.Views.WBProtection.txtProtectSheet": "Proteger hoja", "SSE.Views.WBProtection.txtProtectWB": "Proteger libro", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Introduzca una contraseña para quitarle la protección a la hoja", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Desproteger hoja", "SSE.Views.WBProtection.txtWBUnlockDescription": "Introduzca una contraseña para quitarle la protección al libro", "SSE.Views.WBProtection.txtWBUnlockTitle": "Desproteger libro"}