{"DATE": "DATA", "DATEDIF": "DATEDIF", "DATEVALUE": "DATA.VALOR", "DAY": "DIA", "DAYS": "DIAS", "DAYS360": "DIAS360", "EDATE": "DATAM", "EOMONTH": "FIMMÊS", "HOUR": "HORA", "ISOWEEKNUM": "NUMSEMANAISO", "MINUTE": "MINUTO", "MONTH": "MÊS", "NETWORKDAYS": "DIATRABALHOTOTAL", "NETWORKDAYS.INTL": "DIATRABALHOTOTAL.INTL", "NOW": "AGORA", "SECOND": "SEGUNDO", "TIME": "TEMPO", "TIMEVALUE": "VALOR.TEMPO", "TODAY": "HOJE", "WEEKDAY": "DIA.SEMANA", "WEEKNUM": "NÚMSEMANA", "WORKDAY": "DIATRABALHO", "WORKDAY.INTL": "DIATRABALHO.INTL", "YEAR": "ANO", "YEARFRAC": "FRAÇÃOANO", "BESSELI": "BESSELI", "BESSELJ": "BESSELJ", "BESSELK": "BESSELK", "BESSELY": "BESSELY", "BIN2DEC": "BINADEC", "BIN2HEX": "BINAHEX", "BIN2OCT": "BINAOCT", "BITAND": "BIT.E", "BITLSHIFT": "BITDESL.ESQ", "BITOR": "BIT.OU", "BITRSHIFT": "BITDESL.DIR", "BITXOR": "BIT.XOU", "COMPLEX": "COMPLEXO", "CONVERT": "CONVERTER", "DEC2BIN": "DECABIN", "DEC2HEX": "DECAHEX", "DEC2OCT": "DECAOCT", "DELTA": "DELTA", "ERF": "FUNCERRO", "ERF.PRECISE": "FUNCERRO.PRECISO", "ERFC": "FUNCERROCOMPL", "ERFC.PRECISE": "FUNCERROCOMPL.PRECISO", "GESTEP": "DEGRAU", "HEX2BIN": "HEXABIN", "HEX2DEC": "HEXADEC", "HEX2OCT": "HEXAOCT", "IMABS": "IMABS", "IMAGINARY": "IMAGINÁRIO", "IMARGUMENT": "IMARG", "IMCONJUGATE": "IMCONJ", "IMCOS": "IMCOS", "IMCOSH": "IMCOSH", "IMCOT": "IMCOT", "IMCSC": "IMCSC", "IMCSCH": "IMCSCH", "IMDIV": "IMDIV", "IMEXP": "IMEXP", "IMLN": "IMLN", "IMLOG10": "IMLOG10", "IMLOG2": "IMLOG2", "IMPOWER": "IMPOT", "IMPRODUCT": "IMPROD", "IMREAL": "IMREAL", "IMSEC": "IMSEC", "IMSECH": "IMSECH", "IMSIN": "IMSENO", "IMSINH": "IMSENOH", "IMSQRT": "IMRAIZ", "IMSUB": "IMSUBTR", "IMSUM": "IMSOMA", "IMTAN": "IMTAN", "OCT2BIN": "OCTABIN", "OCT2DEC": "OCTADEC", "OCT2HEX": "OCTAHEX", "DAVERAGE": "BDMÉDIA", "DCOUNT": "BDCONTAR", "DCOUNTA": "BDCONTAR.VAL", "DGET": "BDOBTER", "DMAX": "BDMÁX", "DMIN": "BDMÍN", "DPRODUCT": "BDMULTIPL", "DSTDEV": "BDDESVPAD", "DSTDEVP": "BDDESVPADP", "DSUM": "BDSOMA", "DVAR": "BDVAR", "DVARP": "BDVARP", "CHAR": "CARÁT", "CLEAN": "LIMPARB", "CODE": "CÓDIGO", "CONCATENATE": "CONCATENAR", "CONCAT": "CONCAT", "DOLLAR": "MOEDA", "EXACT": "EXATO", "FIND": "LOCALIZAR", "FINDB": "FINDB", "FIXED": "FIXA", "LEFT": "ESQUERDA", "LEFTB": "LEFTB", "LEN": "NÚM.CARAT", "LENB": "LENB", "LOWER": "MINÚSCULAS", "MID": "SEG.TEXTO", "MIDB": "MIDB", "NUMBERVALUE": "VALOR.NÚMERO", "PROPER": "INICIAL.MAIÚSCULA", "REPLACE": "SUBSTITUIR", "REPLACEB": "REPLACEB", "REPT": "REPETIR", "RIGHT": "DIREITA", "RIGHTB": "RIGHTB", "SEARCH": "PROCURAR", "SEARCHB": "SEARCHB", "SUBSTITUTE": "SUBST", "T": "T", "T.TEST": "TESTE.T", "TEXT": "TEXTO", "TEXTJOIN": "UNIRTEXTO", "TREND": "TENDÊNCIA", "TRIM": "COMPACTAR", "TRIMMEAN": "MÉDIA.INTERNA", "TTEST": "TESTET", "UNICHAR": "UNICARÁT", "UNICODE": "UNICODE", "UPPER": "MAIÚSCULAS", "VALUE": "VALOR", "AVEDEV": "DESV.MÉDIO", "AVERAGE": "MÉDIA", "AVERAGEA": "MÉDIAA", "AVERAGEIF": "MÉDIA.SE", "AVERAGEIFS": "MÉDIA.SE.S", "BETADIST": "DISTBETA", "BETAINV": "BETA.ACUM.INV", "BETA.DIST": "DIST.BETA", "BETA.INV": "INV.BETA", "BINOMDIST": "DISTRBINOM", "BINOM.DIST": "DISTR.BINOM", "BINOM.DIST.RANGE": "DIST.BINOM.INTERVALO", "BINOM.INV": "INV.BINOM", "CHIDIST": "DIST.CHI", "CHIINV": "INV.CHI", "CHITEST": "TESTE.CHI", "CHISQ.DIST": "DIST.CHIQ", "CHISQ.DIST.RT": "DIST.CHIQ.DIR", "CHISQ.INV": "INV.CHIQ", "CHISQ.INV.RT": "INV.CHIQ.DIR", "CHISQ.TEST": "TESTE.CHIQ", "CONFIDENCE": "INT.CONFIANÇA", "CONFIDENCE.NORM": "INT.CONFIANÇA.NORM", "CONFIDENCE.T": "INT.CONFIANÇA.T", "CORREL": "CORREL", "COUNT": "CONTAR", "COUNTA": "CONTAR.VAL", "COUNTBLANK": "CONTAR.VAZIO", "COUNTIF": "CONTAR.SE", "COUNTIFS": "CONTAR.SE.S", "COVAR": "COVAR", "COVARIANCE.P": "COVARIÂNCIA.P", "COVARIANCE.S": "COVARIÂNCIA.S", "CRITBINOM": "CRIT.BINOM", "DEVSQ": "DESVQ", "EXPON.DIST": "DIST.EXPON", "EXPONDIST": "DISTEXPON", "FDIST": "DISTF", "FINV": "INVF", "FTEST": "TESTEF", "F.DIST": "DIST.F", "F.DIST.RT": "DIST.F.DIR", "F.INV": "INV.F", "F.INV.RT": "INV.F.DIR", "F.TEST": "TESTE.F", "FISHER": "FISHER", "FISHERINV": "FISHERINV", "FORECAST": "PREVISÃO", "FORECAST.ETS": "PREVISÃO.ETS", "FORECAST.ETS.CONFINT": "PREVISÃO.ETS.CONFINT", "FORECAST.ETS.SEASONALITY": "PREVISÃO.ETS.SAZONALIDADE", "FORECAST.ETS.STAT": "PREVISÃO.ETS.ESTATÍSTICA", "FORECAST.LINEAR": "PREVISÃO.LINEAR", "FREQUENCY": "FREQUÊNCIA", "GAMMA": "GAMA", "GAMMADIST": "DISTGAMA", "GAMMA.DIST": "DIST.GAMA", "GAMMAINV": "INVGAMA", "GAMMA.INV": "INV.GAMA", "GAMMALN": "LNGAMA", "GAMMALN.PRECISE": "LNGAMA.PRECISO", "GAUSS": "GAUSS", "GEOMEAN": "MÉDIA.GEOMÉTRICA", "GROWTH": "CRESCIMENTO", "HARMEAN": "MÉDIA.HARMÓNICA", "HYPGEOM.DIST": "DIST.HIPGEOM", "HYPGEOMDIST": "DIST.HIPERGEOM", "INTERCEPT": "INTERCETAR", "KURT": "CURT", "LARGE": "MAIOR", "LINEST": "PROJ.LIN", "LOGEST": "PROJ.LOG", "LOGINV": "INVLOG", "LOGNORM.DIST": "DIST.NORMLOG", "LOGNORM.INV": "INV.NORMALLOG", "LOGNORMDIST": "DIST.NORMALLOG", "MAX": "MÁXIMO", "MAXA": "MÁXIMOA", "MAXIFS": "MÁXIMO.SE.S", "MEDIAN": "MED", "MIN": "MÍNIMO", "MINA": "MÍNIMOA", "MINIFS": "MÍNIMO.SE.S", "MODE": "MODA", "MODE.MULT": "MODO.MÚLT", "MODE.SNGL": "MODO.SIMPLES", "NEGBINOM.DIST": "DIST.BINOM.NEG", "NEGBINOMDIST": "DIST.BIN.NEG", "NORM.DIST": "DIST.NORMAL", "NORM.INV": "INV.NORMAL", "NORM.S.DIST": "DIST.S.NORM", "NORM.S.INV": "INV.S.NORM", "NORMDIST": "DIST.NORM", "NORMINV": "INV.NORM", "NORMSDIST": "DIST.NORMP", "NORMSINV": "INV.NORMP", "PEARSON": "PEARSON", "PERCENTILE": "PERCENTIL", "PERCENTILE.EXC": "PERCENTIL.EXC", "PERCENTILE.INC": "PERCENTIL.INC", "PERCENTRANK": "ORDEM.PERCENTUAL", "PERCENTRANK.EXC": "ORDEM.PERCENTUAL.EXC", "PERCENTRANK.INC": "ORDEM.PERCENTUAL.INC", "PERMUT": "PERMUTAR", "PERMUTATIONA": "PERMUTAR.R", "PHI": "PHI", "POISSON": "POISSON", "POISSON.DIST": "DIST.POISSON", "PROB": "PROB", "QUARTILE": "QUARTIL", "QUARTILE.INC": "QUARTIL.INC", "QUARTILE.EXC": "QUARTIL.EXC", "RANK.AVG": "ORDEM.MÉD", "RANK.EQ": "ORDEM.EQ", "RANK": "ORDEM", "RSQ": "RQUAD", "SKEW": "DISTORÇÃO", "SKEW.P": "DISTORÇÃO.P", "SLOPE": "DECLIVE", "SMALL": "MENOR", "STANDARDIZE": "NORMALIZAR", "STDEV": "DESVPAD", "STDEV.P": "DESVPAD.P", "STDEV.S": "DESVPAD.S", "STDEVA": "DESVPADA", "STDEVP": "DESVPADP", "STDEVPA": "DESVPADPA", "STEYX": "EPADYX", "TDIST": "DISTT", "TINV": "INVT", "T.DIST": "DIST.T", "T.DIST.2T": "DIST.T.2C", "T.DIST.RT": "DIST.T.DIR", "T.INV": "INV.T", "T.INV.2T": "INV.T.2C", "VAR": "VAR", "VAR.P": "VAR.P", "VAR.S": "VAR.S", "VARA": "VARA", "VARP": "VARP", "VARPA": "VARPA", "WEIBULL": "WEIBULL", "WEIBULL.DIST": "DIST.WEIBULL", "Z.TEST": "TESTE.Z", "ZTEST": "TESTEZ", "ACCRINT": "JUROSACUM", "ACCRINTM": "JUROSACUMV", "AMORDEGRC": "AMORDEGRC", "AMORLINC": "AMORLINC", "COUPDAYBS": "CUPDIASINLIQ", "COUPDAYS": "CUPDIAS", "COUPDAYSNC": "CUPDIASPRÓX", "COUPNCD": "CUPDATAPRÓX", "COUPNUM": "CUPNÚM", "COUPPCD": "CUPDATAANT", "CUMIPMT": "PGTOJURACUM", "CUMPRINC": "PGTOCAPACUM", "DB": "BD", "DDB": "BDD", "DISC": "DESC", "DOLLARDE": "MOEDADEC", "DOLLARFR": "MOEDAFRA", "DURATION": "DURAÇÃO", "EFFECT": "EFETIVA", "FV": "VF", "FVSCHEDULE": "VFPLANO", "INTRATE": "TAXAJUROS", "IPMT": "IPGTO", "IRR": "TIR", "ISPMT": "É.PGTO", "MDURATION": "MDURAÇÃO", "MIRR": "MTIR", "NOMINAL": "NOMINAL", "NPER": "NPER", "NPV": "VAL", "ODDFPRICE": "PREÇOPRIMINC", "ODDFYIELD": "LUCROPRIMINC", "ODDLPRICE": "PREÇOÚLTINC", "ODDLYIELD": "LUCROÚLTINC", "PDURATION": "PDURAÇÃO", "PMT": "PGTO", "PPMT": "PPGTO", "PRICE": "PREÇO", "PRICEDISC": "PREÇODESC", "PRICEMAT": "PREÇOVENC", "PV": "VA", "RATE": "TAXA", "RECEIVED": "RECEBER", "RRI": "DEVOLVERTAXAJUROS", "SLN": "AMORT", "SYD": "AMORTD", "TBILLEQ": "OTN", "TBILLPRICE": "OTNVALOR", "TBILLYIELD": "OTNLUCRO", "VDB": "BDV", "XIRR": "XTIR", "XNPV": "XVAL", "YIELD": "LUCRO", "YIELDDISC": "LUCRODESC", "YIELDMAT": "LUCROVENC", "ABS": "ABS", "ACOS": "ACOS", "ACOSH": "ACOSH", "ACOT": "ACOT", "ACOTH": "ACOTH", "AGGREGATE": "AGREGAR", "ARABIC": "ÁRABE", "ASC": "ASC", "ASIN": "ASEN", "ASINH": "ASENH", "ATAN": "ATAN", "ATAN2": "ATAN2", "ATANH": "ATANH", "BASE": "BASE", "CEILING": "ARRED.EXCESSO", "CEILING.MATH": "ARRED.EXCESSO.MAT", "CEILING.PRECISE": "CEILING.PRESIZE", "COMBIN": "COMBIN", "COMBINA": "COMBIN.R", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "CSC", "CSCH": "CSCH", "DECIMAL": "DECIMAL", "DEGREES": "GRAUS", "ECMA.CEILING": "ECMA.CEILING", "EVEN": "PAR", "EXP": "EXP", "FACT": "FATORIAL", "FACTDOUBLE": "FATDUPLO", "FLOOR": "ARRED.DEFEITO", "FLOOR.PRECISE": "FLOOR.PRECISE", "FLOOR.MATH": "ARRED.DEFEITO.MAT", "GCD": "MDC", "INT": "INT", "ISO.CEILING": "ISO.CEILING", "LCM": "MMC", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "MATRIZ.DETERM", "MINVERSE": "MATRIZ.INVERSA", "MMULT": "MATRIZ.MULT", "MOD": "RESTO", "MROUND": "MARRED", "MULTINOMIAL": "POLINOMIAL", "MUNIT": "UNIDM", "ODD": "ÍMPAR", "PI": "PI", "POWER": "POTÊNCIA", "PRODUCT": "PRODUTO", "QUOTIENT": "QUOCIENTE", "RADIANS": "RADIANOS", "RAND": "ALEATÓRIO", "RANDARRAY": "MATRIZALEATÓRIA", "RANDBETWEEN": "ALEATÓRIOENTRE", "ROMAN": "ROMANO", "ROUND": "ARRED", "ROUNDDOWN": "ARRED.PARA.BAIXO", "ROUNDUP": "ARRED.PARA.CIMA", "SEC": "SEC", "SECH": "SECH", "SERIESSUM": "SOMASÉRIE", "SIGN": "SINAL", "SIN": "SEN", "SINH": "SENH", "SQRT": "RAIZQ", "SQRTPI": "RAIZPI", "SUBTOTAL": "SUBTOTAL", "SUM": "SOMA", "SUMIF": "SOMA.SE", "SUMIFS": "SOMA.SE.S", "SUMPRODUCT": "SOMARPRODUTO", "SUMSQ": "SOMARQUAD", "SUMX2MY2": "SOMAX2DY2", "SUMX2PY2": "SOMAX2SY2", "SUMXMY2": "SOMAXMY2", "TAN": "TAN", "TANH": "TANH", "TRUNC": "TRUNCAR", "ADDRESS": "ENDEREÇO", "CHOOSE": "SELECIONAR", "COLUMN": "COL", "COLUMNS": "COLS", "FORMULATEXT": "FÓRMULA.TEXTO", "HLOOKUP": "PROCH", "HYPERLINK": "HIPERLIGAÇÃO", "INDEX": "ÍNDICE", "INDIRECT": "INDIRETO", "LOOKUP": "PROC", "MATCH": "CORRESP", "OFFSET": "DESLOCAMENTO", "ROW": "LIN", "ROWS": "LINS", "TRANSPOSE": "TRANSPOR", "UNIQUE": "EXCLUSIVOS", "VLOOKUP": "PROCV", "XLOOKUP": "PROCX", "CELL": "CELL", "ERROR.TYPE": "TIPO.ERRO", "ISBLANK": "É.CÉL.VAZIA", "ISERR": "É.ERROS", "ISERROR": "É.ERRO", "ISEVEN": "ÉPAR", "ISFORMULA": "É.FORMULA", "ISLOGICAL": "É.LÓGICO", "ISNA": "É.NÃO.DISP", "ISNONTEXT": "É.NÃO.TEXTO", "ISNUMBER": "É.NÚM", "ISODD": "ÉÍMPAR", "ISREF": "É.REF", "ISTEXT": "É.TEXTO", "N": "N", "NA": "NÃO.DISP", "SHEET": "FOLHA", "SHEETS": "FOLHAS", "TYPE": "TIPO", "AND": "E", "FALSE": "FALSO", "IF": "SE", "IFS": "SE.S", "IFERROR": "SE.ERRO", "IFNA": "SEND", "NOT": "NÃO", "OR": "OU", "SWITCH": "PARÂMETRO", "TRUE": "VERDADEIRO", "XOR": "XOU", "TEXTBEFORE": "TEXTOANTES", "TEXTAFTER": "TEXTODEPOIS", "TEXTSPLIT": "DIVIDIRTEXTO", "WRAPROWS": "MOLDARLINS", "VSTACK": "JUNTARV", "HSTACK": "JUNTARH", "CHOOSEROWS": "ESCOLHERLINS", "CHOOSECOLS": "ESCOLHERCOLS", "TOCOL": "PARACOL", "TOROW": "PARALIN", "WRAPCOLS": "MOLDARCOLS", "TAKE": "INCLUIR", "DROP": "EXCLUIR", "LocalFormulaOperands": {"StructureTables": {"h": "Headers", "d": "Data", "a": "All", "tr": "This row", "t": "Totals"}, "CONST_TRUE_FALSE": {"t": "TRUE", "f": "FALSE"}, "CONST_ERROR": {"nil": "#NULL!", "div": "#DIV/0!", "value": "#VALUE!", "ref": "#REF!", "name": "#NAME\\?", "num": "#NUM!", "na": "#N/A", "getdata": "#GETTING_DATA", "uf": "#UNSUPPORTED_FUNCTION!"}}}