﻿<!DOCTYPE html>
<html>
	<head>
		<title>WAHRSCHBEREICH-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>WAHRSCHBEREICH-Funktion</h1>
			<p>Die Funktion <b>WAHRSCHBEREICH</b> gehört zur Gruppe der statistischen Funktionen. Sie gibt die Wahrscheinlichkeit für ein von zwei Werten eingeschlossenes Intervall zurück.</p>
			<p>Die Formelsyntax der Funktion <b>WAHRSCHBEREICH</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>WAHRSCHBEREICH(Beob_Werte; Beob_Wahrsch;[Untergrenze];[Obergrenze])</em></b></p> 
			<p><em>Dabei gilt:</em></p>
				<p style="text-indent: 50px;"><b><em>Beob_Werte</em></b> ist der Bereich von Realisationen der Zufallsvariablen, denen Wahrscheinlichkeiten zugeordnet sind.</p>
				<p style="text-indent: 50px;"><b><em>Beob_Wahrsch</em></b> ist eine Menge von Wahrscheinlichkeiten, verknüpft mit den Werten in <b><em>Beob_Werte</em></b>, wobei der ausgewählte Zellbereich numerische Werte enthält, die größer als 0, aber kleiner als 1 sind. Die Summe der Werte in <b><em>Beob_Wahrsch</em></b> sollte gleich 1 sein, sonst gibt die Funktion den Fehlerwert <b>#Zahl!</b> zurück.</p>
				<p class="note"><b>Hinweis</b>: <b><em>Beob_Werte</em></b> sollte die gleiche Anzahl von Elementen enthalten wie <b><em>Beob_Wahrsch</em></b>.</p>
				<p style="text-indent: 50px;"><b><em>Untergrenze</em></b> ist die untere Grenze der Werte, deren Wahrscheinlichkeit berechnet werden soll.</p>
				<p style="text-indent: 50px;"><b><em>Obergrenze</em></b> ist die obere Grenze der Werte, deren Wahrscheinlichkeit berechnet werden soll. Dieses Argument ist optional. Fehlt das Argument Obergrenze, gibt die Funktion die Wahrscheinlichkeit für den Wert <b><em>Untergrenze</em></b> zurück.</p>
			<p>Anwendung der Funktion <b>WAHRSCHBEREICH</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Statistische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>WAHRSCHBEREICH</b>:</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="WAHRSCHBEREICH-Funktion" src="../images/prob.png" /></p>
		</div>
	</body>
</html>