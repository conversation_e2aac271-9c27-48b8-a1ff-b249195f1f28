var indexes = 
[
   {
        "id": "HelpfulHints/About.htm", 
        "title": "À propos de l'Éditeur de Présentations", 
        "body": "Éditeur de Présentations est une application en ligne qui vous permet de parcourir et de modifier des présentations dans votre navigateur . En utilisant l'Éditeur de Présentations, vous pouvez effectuer différentes opérations d'édition comme avec n'importe quel éditeur de bureau, imprimer les présentations modifiées en gardant la mise en forme ou les télécharger sur votre disque dur au format PPTX, PDF, ODP, POTX, PDF/A, OTP. Pour afficher la version actuelle du logiciel, le numéro de build et les informations de licence dans la version en ligne, cliquez sur l'icône dans la barre latérale gauche. Pour afficher la version actuelle du logiciel et les informations de licence dans la version de bureau pour Windows, sélectionnez l'élément de menu À propos dans la barre latérale gauche de la fenêtre principale du programme. Dans la version de bureau pour Mac OS, accédez au menu ONLYOFFICE en haut de l'écran et sélectionnez l'élément de menu À propos d'ONLYOFFICE."
    },
   {
        "id": "HelpfulHints/AdvancedSettings.htm", 
        "title": "Paramètres avancés de l'Éditeur de Présentations", 
        "body": "Éditeur de Présentations vous permet de modifier ses paramètres avancés. Pour y accéder, ouvrez l'onglet Fichier de la barre d'outils supérieure et sélectionnez l'option Paramètres avancés.... Les paramètres avancés sont les suivants : Édition et enregistrement Enregistrement automatique est utilisé dans la version en ligne pour activer/désactiver l'enregistrement automatique des modifications que vous effectuez pendant l'édition. Récupération automatique est utilisée dans la version de bureau pour activer/désactiver l'option qui permet de récupérer automatiquement les présentations en cas de fermeture inattendue du programme. Afficher le bouton \"Options de collage\" lorsque le contenu est collé. L'icône correspondante sera affichée lorsque vous collez le contenu à la présentation. Collaboration Le Mode de co-édition permet de sélectionner le mode d'affichage des modifications apportées à la présentation lors de la co-édition. Rapide (par défaut). Les utilisateurs qui participent à la co-édition de la présentation verront les changements en temps réel une fois qu'ils sont faits par d'autres utilisateurs. Strict. Tous les changements apparaîtront seulement après avoir cliqué sur l'icône Enregistrer pour vous informer qu'il y a des changements effectués par d'autres utilisateurs. Vérification The Vérification de l'orthographe sert à activer/désactiver l'option de vérification de l'orthographe. Ignorer les mots en MAJUSCULES. Les mots tapés en majuscules sont ignorés lors de la vérification de l'orthographe. Ignorer les mots contenant des chiffres. Les mots contenant des chiffres sont ignorés lors de la vérification de l'orthographe. Le menu Options d'auto-correction... permet d'acceder aux paramètres d'auto-correction tels que remplacement au cours de la frappe, fonctions de reconnaissance, mise en forme automatique etc. Espace de travail L'option Guides d'alignement est utilisée pour activer/désactiver les guides d'alignement qui apparaissent lorsque vous déplacez des objets et vous permettent de les positionner précisément sur la diapositive. L'option Hiéroglyphes est utilisée pour activer/désactiver l'affichage des hiéroglyphes. L'option Utiliser la touche Alt pour naviguer dans l'interface utilisateur à l'aide du clavier est utilisée pour activer l'utilisation de la touche Alt / Option à un raccourci clavier. L'option Thème d'interface permet de modifier les jeux de couleurs de l'interface d'éditeur. L'option Identique à système rend le thème d'interface de l'éditeur identique à celui de votre système. Le mode Claire comprend l'affichage des éléments de l'interface utilisateur en couleurs standards bleu, blanc et gris claire à contraste réduit et est destiné à un travail de jour. Le mode Claire classique comprend l'affichage en couleurs standards bleu, blanc et gris claire. Le mode Sombre comprend l'affichage en tons sombres noir, gris foncé et gris claire destinés à un travail de nuit. Le mode Contraste sombre comprend l'affichage des éléments de l'interface utilisateur en couleurs noir, gris foncé et blanc à plus de contraste et est destiné à mettre en surbrillance la zone de travail du fichier. Remarque : En plus des thèmes de l'interface disponibles Claire, Classique claire, Sombre et Contraste sombre il est possible de personnaliser ONLYOFFICE editors en utilisant votre propre couleur de thème. Pour en savoir plus, veuillez consulter ces instructions. L'option Unité de mesure sert à spécifier les unités de mesure utilisées sur les règles et dans les fenêtres de paramètres pour les paramètres tels que largeur, hauteur, espacement, marges etc. Vous pouvez choisir l'option Centimètre, Point ou Pouce. L'option Valeur du zoom par défaut sert à définir la valeur de zoom par défaut en la sélectionnant de la liste des options disponibles de 50% à 500%. Vous pouvez également choisir l'option Ajuster à la diapositive ou Ajuster à la largeur. L'option Hinting de la police sert à sélectionner le type d'affichage de la police dans l'Éditeur de Présentations. Choisissez Comme Windows si vous aimez la façon dont les polices sont habituellement affichées sous Windows, c'est-à-dire en utilisant la police de Windows. Choisissez Comme OS X si vous aimez la façon dont les polices sont habituellement affichées sous Mac, c'est-à-dire sans hinting. Choisissez Natif si vous voulez que votre texte soit affiché avec les hintings intégrés dans les fichiers de polices. Mise en cache par défaut - sert à sélectionner cache de police. Il n'est pas recommandé de désactiver ce mode-ci sans raison évidente. C'est peut être utile dans certains cas, par exemple les problèmes d'accélération matérielle activé sous Google Chrome. Éditeur de Présentations gère deux modes de mise en cache : Dans le premier mode de mise en cache, chaque lettre est mise en cache comme une image indépendante. Dans le deuxième mode de mise en cache, l'image d'une certaine taille est sélectionnée avec les lettres dynamiques et avec de l'allocation/libération de la mémoire mise en place. La deuxième image est créée s'il n'y a pas de mémoire suffisante etc. Le Mode de mise en cache par défaut est activé en fonction du navigateur utilisé : Avec la mise en cache par défaut activée, dans Internet Explorer (v. 9, 10, 11) le deuxième mode de mise en cache est utilisé, le premier mode de mise en cache est utilisé dans les autres navigateurs. Avec la mise en cache par défaut désactivée, dans Internet Explorer (v. 9, 10, 11) le premier mode de mise en cache est utilisé, le deuxième mode de mise en cache est utilisé dans les autres navigateurs. L'option Réglages macros s'utilise pour définir l'affichage des macros avec notification. Choisissez Désactivez tout pour désactiver toutes les macros dans votre présentation. Choisissez Montrer la notification pour afficher les notifications lorsque des macros sont présentes dans une présentation. Choisissez Activer tout pour exécuter automatiquement toutes les macros dans une présentation. Pour enregistrer toutes les modifications, cliquez sur le bouton Appliquer."
    },
   {
        "id": "HelpfulHints/CollaborativeEditing.htm", 
        "title": "Collaborer sur une présentation en temps réel", 
        "body": "Éditeur de Présentations permet de gérer le flux de travail continu par l'ensemble de l'équipe : partager des fichiers et des dossiers, communiquer directement depuis l'éditeur, laisser des commentaires pour des fragments de la présentation nécessitant la participation d'une tierce personne, sauvegarder des versions de la présentation pour une utilisation ultérieure. Dans l'Éditeur de Présentations il y a deux modes de collaborer sur des présentations en temps réel : Rapide et Strict. Vous pouvez basculer entre les modes depuis Paramètres avancés. Il est également possible de choisir le mode voulu à l'aide de l'icône Mode de coédition dans l'onglet Collaboration de la barre d'outils supérieure : Le nombre d'utilisateurs qui travaillent sur la présentation actuelle est spécifié sur le côté droit de l'en-tête de l'éditeur - . Pour afficher les personnes qui travaillent sur le fichier, cliquez sur cette icône pour ouvrir le panneau Chat avec la liste complète affichée. Mode Rapide Le mode Rapide est utilisé par défaut et affiche les modifications effectuées par d'autres utilisateurs en temps réel. Lorsque vous co-éditez une présentation en mode Rapide, la possibilité de Rétablir la dernière opération annulée n'est pas disponible. Ce mode affichera les actions et les noms des co-auteurs. Lorsqu'une présentation est en cours de modification par plusieurs utilisateurs simultanément dans le mode Strict, les objets modifiés sont marqués avec des lignes pointillées de couleurs différentes. Pour voir qui est en train d'éditer le fichier au présent, placez le curseur de la souris sur cette icône - les noms des utilisateurs seront affichés dans la fenêtre contextuelle. Mode Strict Le mode Strict est sélectionné pour masquer les modifications d'autres utilisateurs jusqu'à ce que vous cliquiez sur l'icône Enregistrer pour enregistrer vos propres modifications et accepter les modifications apportées par d'autres utilisateurs. Lorsqu'une présentation est en cours de modification par plusieurs utilisateurs simultanément dans le mode Strict, les objets modifiés sont marqués avec des lignes pointillées de couleurs différentes. Le contour de l'objet modifié est en ligne pointillée. Les lignes pointillées rouges indiquent des objets qui sont en train d'être édités par d'autres utilisateurs. Dès que l'un des utilisateurs sauvegarde ses modifications en cliquant sur l'icône , les autres verront une note dans la barre d'état indiquant qu'il y a des mises à jour. Pour enregistrer les modifications apportées et récupérer les mises à jour de vos co-auteurs cliquez sur l'icône dans le coin supérieur gauche de la barre supérieure. Les mises à jour seront marquées pour vous aider à contrôler ce qui a été exactement modifié. Mode Live Viewer Le mode Live Viewer est utilisé pour voir les modifications apportées par les autres utilisateurs en temps réel lorsque la présentation est ouverte par un utilisateur ayant les droits d'accès Lecture seule. Pour le bon fonctionnement du mode assurez-vous que la case à cocher Afficher les modifications apportées par d'autres utilisateurs est active dans les Paramètres avancés de l'éditeur. Utilisateurs anonymes Les utilisateurs du portail qui ne sont pas enregistrés et n'ont pas du profil sont les utilisateurs anonymes qui quand même peuvent collaborer sur des documents. Pour affecter le nom à un utilisateur anonyme, ce dernier doit saisir le nom préféré dans le coin supérieur droit de l'écran lorsque l'on accède au document pour la première fois. Activez l'option \"Ne plus poser cette question\" pour enregistrer ce nom-ci."
    },
   {
        "id": "HelpfulHints/Commenting.htm", 
        "title": "Commentaires", 
        "body": "Éditeur de Présentations permet de gérer le flux de travail continu par l'ensemble de l'équipe : partager des fichiers et dossiers,, collaborer sur présentations en temps réel, communiquer directement depuis l'éditeur, sauvegarder des versions de la présentation pour une utilisation ultérieure. Dans l'Éditeur de Présentations vous pouvez laisser les commentaires pour le contenue de présentations sans le modifier. Contrairement au messages de chat, les commentaires sont stockés jusqu'à ce que vous décidiez de les supprimer. Laisser et répondre aux commentaires Pour laisser un commentaire sur un objet (zone de texte, forme etc.) : sélectionnez l'objet où vous pensez qu'il y a une erreur ou un problème, passez à l'onglet Insertion ou Collaboration sur la barre d'outils supérieure et cliquer sur le bouton Commentaires ou faites un clic droit sur l'objet et sélectionnez Ajouter un commentaire dans le menu, saisissez le texte nécessaire, cliquez sur le bouton Ajouter commentaire/Ajouter. L'objet que vous commenter sera marqué par l'icône . Pour lire le commentaire, il suffit de cliquer sur cette icône. Pour ajouter un commentaire à une diapositive, sélectionnez la diapositive et utilisez le bouton Commentaires sous l'onglet Insertion ou Collaboration de la barre d'outils supérieure. Le commentaire ajouté sera affiché dans le coin supérieur gauche de la diapositive. Pour créer un commentaire de niveau présentation qui n'est pas lié à un objet ou une diapositive spécifique, cliquez sur l'icône dans la barre latérale gauche pour ouvrir le panneau Commentaires et utilisez le lien Ajouter un commentaire au document. Les commentaires au niveau de la présentation sont affichés sur le panneau Commentaires. Les commentaires relatifs aux objets et aux diapositives sont également disponibles sur ce panneau. Tout autre utilisateur peut répondre au commentaire ajouté en posant une question ou en faisant référence au travail fait. Pour ce faire, il suffit de cliquer sur le lien Ajouter une réponse situé au-dessous du commentaire, saisissez votre réponse dans le champ de saisie et appuyez sur le bouton Répondre. Si vous utilisez le mode de co-édition Strict, les nouveaux commentaires ajoutés par d'autres utilisateurs ne seront visibles qu'après un clic sur l'icône dans le coin supérieur gauche de la barre supérieure. Gérer les commentaires Vous pouvez gérer les commentaires ajoutés en utilisant les icônes de la bulle de commentaire ou sur le panneau gauche Commentaires : trier les commentaires ajoutés en cliquant sur l'icône : par date : Plus récent ou Plus ancien. par auteur : Auteur de A à Z ou Auteur de Z à Z Filtrer par groupe : Tout ou sélectionnez un groupe de la liste. Cette option de trie n'est disponible que si votre version prend en charge cette fonctionnalité. modifier le commentaire actuel en cliquant sur l'icône, supprimer le commentaire actuel en cliquant sur l'icône, fermer la discussion actuelle en cliquant sur l'icône si la tâche ou le problème décrit dans votre commentaire est résolu, après quoi la discussion ouverte par votre commentaire reçoit le statut résolu. Pour l'ouvrir à nouveau, cliquez sur l'icône , si vous souhaitez gérer plusieurs commentaires à la fois, ouvrez le menu contextuel Résoudre sous l'onglet Collaboration. Sélectionnez l'une des options disponibles : résoudre les commentaires actuels, marquer mes commentaires comme résolus ou marquer tous les commentaires comme résolus dans la présentation. Ajouter les mentions Ce n'est qu'au contenu d'une présentation que vous pouvez ajouter des mentions et pas à la présentation la-même. Lorsque vous ajoutez un commentaire sur lequel vous voulez attirer l'attention d'une personne, vous pouvez utiliser la fonction de mentions et envoyer une notification par courrier électronique ou Talk. Pour ajouter une mention, Saisissez \"+\" ou \"@\" n'importe où dans votre commentaire, alors la liste de tous les utilisateurs du portail s'affichera. Pour faire la recherche plus rapide, tapez les premières lettres du prénom de la personne, la liste propose les suggestions au cours de la frappe. Puis sélectionnez le nom souhaité dans la liste. Si la personne mentionnée n'a pas l'autorisation d'ouvrir ce fichier, la fenêtre Paramètres de partage va apparaître. Par défaut, un document est partagé en Lecture seule. Modifiez la permission, le cas échéant. Cliquez sur OK. La personne mentionnée recevra une notification par courrier électronique informant que son nom est mentionné dans un commentaire. La personne recevra encore une notification lorsque un fichier commenté est partagé. Supprimer des commentaires Pour supprimer les commentaires, cliquez sur le bouton Supprimer sous l'onglet Collaboration dans la barre d'outils supérieure, sélectionnez l'option convenable du menu : Supprimer les commentaires actuels à supprimer la sélection du moment. Toutes les réponses qui déjà ont été ajoutées seront supprimés aussi. Supprimer mes commentaires à supprimer vos commentaire et laisser les commentaires d'autres. Toutes les réponses qui déjà ont été ajoutées à vos commentaires seront supprimés aussi. Supprimer tous les commentaires sert à supprimer tous les commentaires de la présentation. Pour fermer le panneau avec les commentaires, cliquez sur l'icône de la barre latérale de gauche encore une fois."
    },
   {
        "id": "HelpfulHints/KeyboardShortcuts.htm", 
        "title": "Raccourcis clavier", 
        "body": "Raccourcis clavier pour les touches d'accès Utiliser les raccourcis clavier pour faciliter et accélérer l'accès à l'Éditeur de Présentations sans l'aide de la souris. Appuyez sur la touche Altpour activer toutes les touches d'accès pour l'en-tête, la barre d'outils supérieure, les barres latérales droite et gauche et la barre d'état. Appuyez sur la lettre qui correspond à l'élément dont vous avez besoin. D'autres suggestions de touches peuvent s'afficher en fonction de la touche que vous appuyez. Les premières suggestions de touches se cachent lorsque les suggestions supplémentaires s'affichent. Par exemple, pour accéder à l'onglet Insertion, appuyez sur la touche Alt pour activer les primaires suggestions de touches d'accès. Appuyez sur la lettre I pour accéder à l'onglet Insertion et activer tous les raccourcis clavier disponibles sous cet onglet. Appuyez sur la lettre qui correspond à l'élément que vous allez paramétrer. Appuyez sur la touche Alt pour désactiver toutes les suggestions de touches d'accès ou appuyez sur Échap pour revenir aux suggestions de touches précédentes. Trouverez ci-dessous les raccourcis clavier les plus courants : Windows/Linux Mac OS En travaillant sur la présentation Ouvrir le panneau \"Fichier\" Alt+F ^ Ctrl+⌥ Option+F Ouvrir le panneau Fichier pour enregistrer, télécharger, imprimer la présentation actuelle, voir ses informations, créer une nouvelle présentation ou ouvrir une présentation existatante, accéder à l'aide de l'Éditeur de Présentations ou aux paramètres avancés. Ouvrir la boîte de dialogue « Rechercher » Ctrl+F ^ Ctrl+F, &#8984; Cmd+F Ouvrir la fenêtre Recherche pour commencer à chercher un caractère/mot/phrase dans la présentation actuelle. Ouvir le panneau \"Commentaires\" Ctrl+⇧ Maj+H ^ Ctrl+⇧ Maj+H, &#8984; Cmd+⇧ Maj+H Ouvrir le volet Commentaires pour ajouter votre commentaire ou pour répondre aux commentaires des autres utilisateurs. Ouvrir le champ de commentaires Alt+H &#8984; Cmd+⌥ Option+A Ouvrir un champ de saisie où vous pouvez ajouter le texte de votre commentaire. Ouvrir le panneau \"Chat\" Alt+Q ^ Ctrl+⌥ Option+Q Ouvrir le panneau Chat et envoyer un message. Enregistrer la présentation Ctrl+S ^ Ctrl+S, &#8984; Cmd+S Enregistrer toutes les modifications dans la présentation en cours d'édition dans l'Éditeur de Présentations. Le fichier actif sera enregistré avec son nom de fichier actuel, son emplacement et son format de fichier. Imprimer la présentation Ctrl+P ^ Ctrl+P, &#8984; Cmd+P Imprimer la présentation avec l'une des imprimantes disponibles ou l'enregistrer sous forme de fichier. Enregistrer (Télécharger comme) Ctrl+⇧ Maj+S ^ Ctrl+⇧ Maj+S, &#8984; Cmd+⇧ Maj+S Ouvrir le panneau Télécharger comme pour enregistrer la présentation en cours d'édition sur le disque dur de l'ordinateur dans un des formats pris en charge : PPTX, PDF, ODP, POTX, PDF/A, OTP. Plein écran F11 Passer à l'affichage en plein écran pour adapter l'Éditeur de Présentations à votre écran. Menu d'aide F1 F1 Ouvrir le menu Aide de l'Éditeur de Présentations. Ouvrir un fichier existant (Desktop Editors) Ctrl+O L'onglet Ouvrir fichier local de Desktop Editors, ouvre la boîte de dialogue standard qui permet de sélectionner un fichier existant. Fermer un fichier (Desktop Editors) Ctrl+W, Ctrl+F4 ^ Ctrl+W, &#8984; Cmd+W Fermer la fenêtre de présentation actuelle dans Desktop Editors. Menu contextuel de l'élément ⇧ Maj+F10 ⇧ Maj+F10 Ouvrir le menu contextuel de l'élément sélectionné. Réinitialiser le niveau de zoom Ctrl+0 ^ Ctrl+0 or &#8984; Cmd+0 Réinitialiser le niveau de zoom de la présentation actuelle par défaut pour Ajuster à la diapositive. Navigation Première diapositive Début Début, Fn+← Passer à la première diapositive de la présentation actuelle. Dernière diapositive Fin Fin, Fn+→ Passer à la dernière diapositive de la présentation actuelle. Diapositive suivante Pg. suiv Pg. suiv, Fn+↓ Passer à la diapositive suivante de la présentation actuelle. Diapositive précédente Pg. préc Pg. préc, Fn+↑ Passer à la diapositive précédente de la présentation actuelle. Zoom avant Ctrl++ ^ Ctrl+=, &#8984; Cmd+= Agrandir la présentation en cours d'édition. Zoom arrière Ctrl+- ^ Ctrl+-, &#8984; Cmd+- Effectuer un zoom arrière de la présentation en cours d'édition. Naviguer entre les contrôles dans un dialogue modal ↹ Tab/⇧ Maj+↹ Tab ↹ Tab/⇧ Maj+↹ Tab Naviguer entre les contrôles pour mettre en évidence le contrôle précédent ou suivant dans les dialogues modaux. Exécuter des actions sur des diapositives Nouvelle diapositive Ctrl+M ^ Ctrl+M, &#8984; Cmd+M Créer une nouvelle diapositive et l'ajouter après la diapositive sélectionnée dans la liste. Dupliquer la diapositive Ctrl+D &#8984; Cmd+D Dupliquer la diapositive sélectionnée dans la liste. Déplacer la diapositive vers le haut Ctrl+↑ &#8984; Cmd+↑ Déplacer la diapositive sélectionnée au-dessus de la diapositive précedente dans la liste. Déplacer la diapositive vers le bas Ctrl+↓ &#8984; Cmd+↓ Déplacer la diapositive sélectionnée dessous la diapositive suivante dans la liste. Déplacer la diapositive au début Ctrl+⇧ Maj+↑ &#8984; Cmd+⇧ Maj+↑ Déplacer la diapositive sélectionnée à la première position dans la liste. Déplacer la diapositive à la fin Ctrl+⇧ Maj+↓ &#8984; Cmd+⇧ Maj+↓ Déplacer la diapositive sélectionnée à la dernière position dans la liste. Exécuter des actions sur des objets Créer une copie Ctrl + faire glisser, Ctrl+D ^ Ctrl + faire glisser, &#8984; Cmd + faire glisser, ^ Ctrl+D, &#8984; Cmd+D Maintenez la touche Ctrl pour faire glisser l'objet sélectionné ou appuyez sur Ctrl+D (&#8984; Cmd+D pour Mac) pour créer sa copie. Grouper Ctrl+G &#8984; Cmd+G Grouper les objets sélectionnés. Dissocier Ctrl+⇧ Maj+G &#8984; Cmd+⇧ Maj+G Dissocier le groupe d'objets sélectionnés. Sélectionner l'objet suivant ↹ Tab ↹ Tab Sélectionner l'objet suivant après l'objet sélectionné. Sélectionner l'objet précédent ⇧ Maj+↹ Tab ⇧ Maj+↹ Tab Sélectionner l'objet précédent avant l'objet actuellement sélectionné. Tracer une ligne droite ou une flèche ⇧ Maj + faire glisser (lors du tracé de lignes/flèches) ⇧ Maj + faire glisser (lors du tracé de lignes/flèches) Tracer une ligne droite ou une flèche verticale/horizontale/inclinée de 45 degrés. Modification des objets Limiter le déplacement ⇧ Maj + faire glisser ⇧ Maj + faire glisser Limiter le déplacement de l'objet sélectionné horizontalement ou verticalement. Définir la rotation de 15 degrés ⇧ Maj + faire glisser (lors de la rotation) ⇧ Maj + faire glisser (lors de la rotation) Limiter l'angle de rotation à des incréments de 15 degrés. Conserver les proportions ⇧ Maj + faire glisser (lors du redimensionnement) ⇧ Maj + faire glisser (lors du redimensionnement) Conserver les proportions de l'objet sélectionné lors du redimensionnement. Mouvement pixel par pixel Ctrl+← → ↑ ↓ &#8984; Cmd+← → ↑ ↓ Maintenez la touche Ctrl (&#8984; Cmd pour Mac) enfoncée en faisant glisser et utilisez les flèches pour déplacer l'objet sélectionné d'un pixel à la fois. Utilisation des tableaux Passer à la cellule suivante d'une ligne ↹ Tab ↹ Tab Aller à la cellule suivante d'une ligne de tableau. Passer à la cellule précédente d'une ligne ⇧ Maj+↹ Tab ⇧ Maj+↹ Tab Aller à la cellule précédente d'une ligne de tableau. Passer à la ligne suivante ↓ ↓ Aller à la ligne suivante d'un tableau. Passer à la ligne précédente ↑ ↑ Aller à la ligne précédente d'un tableau. Commencer un nouveau paragraphe ↵ Entrée ↵ Retour Commencer un nouveau paragraphe dans une cellule. Ajouter une nouvelle ligne ↹ Tab dans la cellule inférieure droite du tableau. ↹ Tab dans la cellule inférieure droite du tableau. Ajouter une nouvelle ligne au bas du tableau. Prévisualisation de la présentation Démarrer l'affichage de l'aperçu dès le début Ctrl+F5 ^ Ctrl+F5 Démarrer une présentation dès le début. Naviguer vers l'avant ↵ Entrée, Pg. suiv, →, ↓, ␣ Barre d'espace ↵ Retour, Pg. suiv, →, ↓, ␣ Barre d'espace Effectuer l'animation suivante ou passer à la diapositive suivant. Navigation à rebours Pg. préc, ←, ↑ Pg. préc, ←, ↑ Effectuer l'animation précédente ou revenir à la diapositive précédente. Fermer l'aperçu Échap Échap Terminer la présentation. Annuler et Rétablir Annuler Ctrl+Z ^ Ctrl+Z, &#8984; Cmd+Z Inverser la dernière action effectuée. Rétablir Ctrl+Y ^ Ctrl+Y, &#8984; Cmd+Y Répéter la dernière action annulée. Couper, Copier et Coller Couper Ctrl+X, ⇧ Maj+Supprimer &#8984; Cmd+X Couper l'objet sélectionné et l'envoyer vers le presse-papiers. L'objet sélectionné peut inséré ensuite à un autre endroit dans la même présentation. Copier Ctrl+C, Ctrl+Inser &#8984; Cmd+C Envoyer l'objet sélectionné et vers le presse-papiers. L'objet copié peut être insérées ensuite à un autre endroit dans la même présentation. Coller Ctrl+V, ⇧ Maj+Inser &#8984; Cmd+V Insérer l'objet précédemment copié depuis le presse-papiers à la position actuelle du curseur. L'objet peut être copié à partir de la même présentation. Insérer un lien hypertexte Ctrl+K ^ Ctrl+K, &#8984; Cmd+K Insérer un lien hypertexte qui peut être utilisé pour accéder à une adresse web ou à une certaine diapositive de la présentation. Copier le style Ctrl+⇧ Maj+C ^ Ctrl+⇧ Maj+C, &#8984; Cmd+⇧ Maj+C Copier la mise en forme du fragment sélectionné du texte en cours d'édition. La mise en forme copiée peut être appliquée à un autre fragment du texte dans la même présentation. Appliquer le style Ctrl+⇧ Maj+V ^ Ctrl+⇧ Maj+V, &#8984; Cmd+⇧ Maj+V Appliquer la mise en forme précedemment copiée au texte de la partie en cours d'édition. Sélectionner avec la souris Ajouter au fragment sélectionné ⇧ Maj ⇧ Maj Démarrer la sélection, maintenez enfoncée la touche ⇧ Maj et cliquez sur l'endroit où vous souhaitez terminer la sélection. Sélectionner avec le clavier Sélectionner tout Ctrl+A ^ Ctrl+A, &#8984; Cmd+A Sélectionner toutes les diapositives (dans la liste des diapositives) ou tous les objets de la diapositive (dans la zone d'édition de la diapositive) ou tout le texte (dans le bloc de texte) - selon la position du curseur de la souris. Sélectionner le fragment du texte ⇧ Maj+→ ← ⇧ Maj+→ ← Sélectionner le texte caractère par caractère. Sélectionner le texte depuis le curseur jusqu'au début de la ligne ⇧ Maj+Début Sélectionner le fragment du texte depuis le curseur jusqu'au début de la ligne actuelle. Sélectionner le texte depuis le curseur jusqu'à la fin de la ligne ⇧ Maj+Fin Sélectionner le fragment du texte depuis le curseur jusqu'à la fin de la ligne actuelle. Sélectionner un caractère à droite ⇧ Maj+→ ⇧ Maj+→ Sélectionner un caractère à droite de la position du curseur. Sélectionner un caractère à gauche ⇧ Maj+← ⇧ Maj+← Sélectionner un caractère à gauche de la position du curseur. Sélectionner jusqu'à la fin d'un mot Ctrl+⇧ Maj+→ Sélectionner un fragment de texte depuis le curseur jusqu'à la fin d'un mot. Sélectionner au début d'un mot Ctrl+⇧ Maj+← Sélectionner un fragment de texte depuis le curseur jusqu'au début d'un mot. Sélectionner une ligne vers le haut ⇧ Maj+↑ ⇧ Maj+↑ Sélectionner une ligne vers le haut (avec le curseur au début d'une ligne). Sélectionner une ligne vers le bas ⇧ Maj+↓ ⇧ Maj+↓ Sélectionner une ligne vers le bas (avec le curseur au début d'une ligne). Style de texte Gras Ctrl+B ^ Ctrl+B, &#8984; Cmd+B Mettre la police du fragment de texte sélectionné en gras pour lui donner plus de poids. Italique Ctrl+I ^ Ctrl+I, &#8984; Cmd+I Mettre la police du fragment de texte sélectionné en italique pour lui donner une certaine inclinaison à droite. Souligné Ctrl+U ^ Ctrl+U, &#8984; Cmd+U Souligner le fragment de texte sélectionné avec la ligne qui passe sous les lettres. Barré Ctrl+5 ^ Ctrl+5, &#8984; Cmd+5 Souligner le fragment de texte sélectionné avec la ligne qui passe sous les lettres. Indice Ctrl+⇧ Maj+&gt; &#8984; Cmd+⇧ Maj+&gt; Rendre le fragment du texte sélectionné plus petit et le placer à la partie inférieure de la ligne du texte, par exemple comme dans les formules chimiques. Exposant Ctrl+⇧ Maj+&lt; &#8984; Cmd+⇧ Maj+&lt; Sélectionner le fragment du texte et le placer sur la partie supérieure de la ligne de texte, par exemple comme dans les fractions. Liste à puces Ctrl+⇧ Maj+L ^ Ctrl+⇧ Maj+L, &#8984; Cmd+⇧ Maj+L Créer une liste à puces non numérotée du fragment de texte sélectionné ou créer une nouvelle liste. Supprimer la mise en forme Ctrl+␣ Barre d'espace Supprimer la mise en forme du fragment du texte sélectionné. Agrandir la police Ctrl+] ^ Ctrl+], &#8984; Cmd+] Augmenter la taille de la police du fragment de texte sélectionné de 1 point. Réduire la police Ctrl+[ ^ Ctrl+[, &#8984; Cmd+[ Réduire la taille de la police du fragment de texte sélectionné de 1 point. Alignement centré Ctrl+E Centrer le texte entre les bords gauche et droit. Justifié Ctrl+J Justifier le texte du paragraphe en ajoutant un espace supplémentaire entre les mots pour que les bords gauche et droit du texte soient alignés avec les marges du paragraphe. Aligner à droite Ctrl+R Aligner à droite avec le texte aligné par le côté droit du bloc de texte, le côté gauche reste non aligné. Alignement à gauche Ctrl+L Aligner à gauche avec le texte aligné par le côté gauche du bloc de texte, le côté droit reste non aligné. Augmenter le retrait gauche Ctrl+M ^ Ctrl+M, &#8984; Cmd+M Augmenter l'alinéa de gauche d'une position de tabulation. Diminuer l'indentation gauche Ctrl+⇧ Maj+M ^ Ctrl+⇧ Maj+M, &#8984; Cmd+⇧ Maj+M Diminuer l'alinéa de gauche d'une position de tabulation. Supprimer un caractère à gauche ← Retour arrière ← Retour arrière Supprimer un caractère à gauche du curseur. Supprimer un caractère à droite Supprimer Fn+Supprimer Supprimer un caractère à droite du curseur. Se déplacer dans le texte Déplacer un caractère vers la gauche ← ← Déplacer le curseur d'un caractère vers la gauche. Déplacer un caractère vers la droite → → Déplacer le curseur d'un caractère vers la droite. Déplacer une ligne vers le haut ↑ ↑ Déplacer le curseur d'une ligne vers le haut. Déplacer une ligne vers le bas ↓ ↓ Déplacer le curseur d'une ligne vers le bas. Déplacer vers le début d'un mot ou un mot vers la gauche Ctrl+← &#8984; Cmd+← Déplacer le curseur au début d'un mot ou d'un mot vers la gauche. Déplacer un caractère vers la droite Ctrl+→ &#8984; Cmd+→ Déplacer le curseur d'un mot vers la droite. Passer à l'emplacement suivant Ctrl+↵ Entrée ^ Ctrl+↵ Retour, &#8984; Cmd+↵ Retour Passer au titre ou au corps du texte suivant. S'il s'agit du dernier caractère de remplacement d'une diapositive, une nouvelle diapositive sera insérée avec la même disposition que la diapositive originale. Sauter au début de la ligne Début Début Placer le curseur au début de la ligne en cours d'édition. Sauter à la fin de la ligne Fin Fin Placer le curseur à la fin de la ligne en cours d'édition. Aller au début de la zone de texte Ctrl+Début Placer le curseur au début de la zone de texte en cours d'édition. Aller à la fin de la zone de texte Ctrl+Fin Placer le curseur à la fin de la zone de texte en cours d'édition."
    },
   {
        "id": "HelpfulHints/Navigation.htm", 
        "title": "Paramètres d'affichage et outils de navigation", 
        "body": "Presentation Editor est doté de plusieurs outils qui vous aide à afficher et naviguer à travers votre présentation: le zoom, les boutons de diapositive précédente/suivante et l'indicateur du numéro de diapositive etc. Régler les paramètres d'affichage Pour régler les paramètres d'affichage par défaut et définir le mode le plus pratique pour travailler avec la présentation, passez à l'onglet Affichage . Les options disponibles sont les suivantes: Zoom - sert à définir la valeur de zoom de 50% à 200% en sélectionnant de la liste des options disponibles. Ajuster à la diapositive sert à adapter la diapositive entière à la partie visible de la zone de travail. Ajuster à la largeur sert à adapter la largeur de la diapositive à la partie visible de la zone de travail. Thème d'interface - sélectionnez l'une des thèmes d'interface disponibles dans la liste déroulante: Identique à système, Claire, Classique claire, Sombre, Contraste élevé sombre. Notes - une fois désactivé, les notes au-dessous de la diapositive sont masquées. Il est également possible de masquer/afficher cette section en la faisant glisser avec la souris. Règles une fois désactivé, masque les règles qui sont utilisées pour définir des tabulations et des retraits de paragraphe dans une zone de texte. Pour afficher les Règles masquées, cliquez sur cette option encore une fois. Repères - sélectionnez le type de repères approprié pour positionner correctement des objets sur une diapositive. Les options disponibles pour un positionnement amélioré vertical, horizontal et repères actifs. Quadrillage - sélectionnez la taille de la grille appropriée parmi les modèles disponibles ou indiquez la taille personnalisée et activez ou désactivez les options d'aligner sur la grille pour améliorer le positionnement des objets. Toujours afficher la barre d'outils - une fois désactivé, la barre d'outils supérieure comportant toutes les commandes sont masquée mais tous les onglets restent visibles. Vous pouvez également double-cliquer sur un onglet pour masquer la barre d'outils supérieure ou l'afficher à nouveau. Barre d'état - une fois désactivé, sert à masquer la barre qui se situe tout en bas avec les boutons Affichage des numéros de diapositives et Zoom. Pour afficher la Barre d'état masquée cliquez sur cette option encore une fois. Panneau gauche - une fois désactivé, le panneau gauche comportant les onglets Rechercher, Diapositives, Commentaires sera masqué. Pour afficher le panneau gauche, cochez cette case. Panneau droit - une fois désactivé, le panneau droit comportant les options de configurations des Paramètres sera masqué. Pour afficher le panneau droit, cochez cette case. La barre latérale sur la droite est réduite par défaut. Pour l'agrandir, sélectionnez un objet/diapositive et cliquez sur l'icône de l'onglet actuellement activé sur la droite. Pour réduire la barre latérale sur la droite, cliquez à nouveau sur l'icône. La largeur de la barre latérale gauche est ajustée par simple glisser-déposer: déplacez le curseur de la souris sur la bordure gauche pour qu'elle se transforme en flèche bidirectionnelle et déplacez la bordure vers la gauche pour réduire la largeur de la barre latérale ou vers la droite pour l'agrandir. Utiliser les outils de navigation Pour naviguer à travers votre présentation, utilisez les outils suivants: Les boutons Zoom sont situés en bas à droite et sont utilisés pour faire un zoom avant et arrière dans la présentation active. Pour modifier la valeur de zoom sélectionnée en pourcentage, cliquez dessus et sélectionnez l'une des options de zoom disponibles dans la liste (50% / 75% / 100% / 125% / 150% / 175% / 200% / 300% / 400% / 500%) ou utilisez les boutons Zoom avant ou Zoom arrière . Cliquez sur l'icône Ajuster à la largeur pour adapter la largeur de la diapositive à la partie visible de la zone de travail. Pour adapter la diapositive entière à la partie visible de la zone de travail, cliquez sur l'icône Ajuster à la diapositive . Les options de zoom sont également disponibles sous l'onglet Affichage. Vous pouvez définir une valeur de zoom par défaut. Basculez vers l'onglet Fichier de la barre d'outils supérieure, allez à la section Paramètres avancés..., choisissez la Valeur de zoom par défaut nécessaire dans la liste et cliquez sur le bouton Appliquer. Pour accéder à la diapositive précédente ou suivante lors de la modification de la présentation, vous pouvez utiliser les boutons et en haut et en bas de la barre de défilement verticale sur le côté droit de la diapositive. Indicateur du numéro de diapositive affiche la diapositive actuelle en tant que partie de toute la présentation actuelle (diapositive 'n' de 'nn'). Cliquez sur la légende pour ouvrir la fenêtre dans laquelle vous pouvez entrer le numéro de la diapositive et y accéder rapidement. Si la Barre d'état est masquée, cet outil reste inaccessible."
    },
   {
        "id": "HelpfulHints/Password.htm", 
        "title": "Protéger une présentation avec un mot de passe", 
        "body": "Vous pouvez protéger votre présentation avec un mot de passe afin que tous les co-auteurs puissent d'accéder en mode d'édition. On peut modifier ou supprimer le mot de passe, le cas échéant. Il n'est pas possible de réinitialiser un mot de passe perdu ou oublié. Gardez vos mots de passe dans un endroit sécurisé. Définir un mot de passe passez à l'onglet Fichier de la barre d'outils supérieure, choisissez l'option Protéger, cliquez sur le bouton Ajouter un mot de passe. saisissez le mot de passe dans le champ Mot de passe et validez-le dans le champ Confirmez le mot de passe au-dessous, ensuite cliquez sur OK. Cliquez sur pour afficher ou masquer les caractères du mot de passe lors de la saisie. Modifier le mot de passe passez à l'onglet Fichier de la barre d'outils supérieure, choisissez l'option Protéger, cliquez sur le bouton Modifier un mot de passe. saisissez le mot de passe dans le champ Mot de passe et validez-le dans le champ Confirmez le mot de passe au-dessous, ensuite cliquez sur OK. Supprimer le mot de passe passez à l'onglet Fichier de la barre d'outils supérieure, choisissez l'option Protéger, cliquez sur le bouton Supprimer le mot de passe."
    },
   {
        "id": "HelpfulHints/Search.htm", 
        "title": "Fonction de recherche", 
        "body": "et remplacement Pour rechercher des caractères, des mots ou des phrases dans l'Éditeur de Présentations, cliquez sur l'icône située sur la barre latérale gauche, sur l'icône située au coin droit en haut ou appuyez sur la combinaison de touches Ctrl+F (Command+F pour MacOS) pour ouvrir le petit panneau Recherche ou sur la combinaison de touches Ctrl+H pour ouvrir le panneau complet Recherche. Un petit panneau Recherche s'affiche au coin droit en haut de la zone de travail. Afin d'accéder aux paramètres avancés, cliquez sur l'icône . La fenêtre Rechercher et remplacer s'ouvre : Saisissez votre enquête dans le champ de saisie correspondant Rechercher. Si vous avez besoin de remplacer une ou plusieurs occurrences des caractères saisis, saisissez le texte de remplacement dans le champ de saisie correspondant Remplacer par. Vous pouvez remplacer l'occurrence actuellement sélectionnée ou remplacer toutes les occurrences en cliquant sur les boutons correspondants Remplacer et Remplacer tout. Afin de parcourir les occurrences trouvées, cliquez sur un des boutons à flèche. Le bouton affiche l'occurrence suivante, le bouton ) affiche l'occurrence précédente. Spécifiez les paramètres de recherche en cochant les options nécessaires : Sensible à la casse sert à trouver les occurrences saisies de la même casse (par exemple, si votre enquête est 'Editeur' et cette option est sélectionnée, les mots tels que 'éditeur' ou 'EDITEUR' etc. ne seront pas trouvés). Seulement les mots entiers - sert à surligner les mots entiers uniquement. La première diapositive contenant les caractères saisis dans la direction sélectionnée sera activée dans la liste des diapositives et affichée avec les caractères requis dans la zone de travail. Si ce n'est pas la diapositive que vous cherchez, cliquez sur le bouton sélectionné encore une fois pour passer à la diapositive suivante contenant les caractères saisis."
    },
   {
        "id": "HelpfulHints/SpellChecking.htm", 
        "title": "Vérification de l'orthographe", 
        "body": "Éditeur de Présentations vous permet de vérifier l'orthographe du texte saisi dans une certaine langue et corriger des fautes lors de l'édition. Sur l'édition de bureau, il est aussi possible d'ajouter les mots au dictionnaire personnalisé commun à tous les trois éditeurs. À partir de la version 6.3, les éditeurs ONLYOFFICE prennent en charge l'interface SharedWorker pour un meilleur fonctionnement en évitant une utilisation élevée de la mémoire. Si votre navigateur ne prend pas en charge SharedWorker, alors c'est seulement Worker qui sera actif. Pour en savoir plus sur SharedWorker, veuillez consulter cette page. Tout d'abord, choisissez la langue pour tout le document. Cliquez sur l'icône sur le côté droit de la barre d'état. Dans la fenêtre ouverte sélectionnez la langue nécessaire et cliquez sur OK. La langue sélectionnée sera appliquée à tout le document. Pour sélectionner une langue différente pour un fragment de texte dans la présentation, sélectionnez le fragment nécessaire avec la souris et utilisez le menu de la barre d'état. Pour activer l'option de vérification orthographique, vous pouvez : cliquer sur l'icône Vérification orthographique dans la barre d'état, ou basculer vers l'onglet Fichier de la barre d'outils supérieure, aller à la section Paramètres avancés..., cocher la case Activer la vérification orthographique et cliquer sur le bouton Appliquer. Les mots mal orthographiés seront soulignés par une ligne rouge. Cliquez droit sur le mot nécessaire pour activer le menu et : choisissez une des variantes suggérées pour remplacer le mot mal orthographié. S'il y a trop de variantes, l'option Plus de variantes... apparaît dans le menu ; utilisez l'option Ignorer pour ignorer juste ce mot et supprimer le surlignage ou Ignorer tout pour ignorer tous les mots identiques présentés dans le texte ; si le mot n'est pas dans le dictionnaire, vous pouvez l'ajouter à votre dictionnaire personnel. La fois prochaine ce mot ne sera donc plus considéré comme erroné. Cette option est disponible sur l'édition de bureau ; sélectionnez une autre langue pour ce mot. Pour désactiver l'option de vérification orthographique, vous pouvez : cliquer sur l'icône Vérification orthographique dans la barre d'état, ou Basculer vers l'onglet Fichier de la barre d'outils supérieure, aller à la section Paramètres avancés..., décocher la case Activer la vérification orthographique et cliquer sur le bouton Appliquer."
    },
   {
        "id": "HelpfulHints/SupportedFormats.htm", 
        "title": "Formats des présentations électroniques pris en charge", 
        "body": "Une présentation est l'ensemble des diapositives qui peut inclure de différents types de contenu tels que des images, des fichiers multimédias, des textes, des effets etc. Éditeur de Présentations prend en charge les formats suivants : Lors du téléchargement ou de l'ouverture d'un fichier, celui-ci sera converti au format Office Open XML (PPTX). Cette conversion permet d'accélérer le traitement des fichiers et d'améliorer l'interopérabilité des données. Le tableau ci-dessous présente les formats de fichiers pour l'affichage et/ou pour l'édition. Formats Description Affichage au format natif Affichage lors de la conversion en OOXML Édition au format natif Édition lors de la conversion en OOXML ODP Présentation OpenDocument Format de fichier utilisé pour les présentations créées par l'application Impress, qui fait partie des applications OpenOffice + + OTP Modèle de présentation OpenDocument Format de fichier OpenDocument pour les modèles de présentation. Un modèle OTP contient des paramètres de mise en forme, des styles, etc. et peut être utilisé pour créer plusieurs présentations avec la même mise en forme + + POTX Modèle de document PowerPoint Open XML Format de fichier zippé, basé sur XML, développé par Microsoft pour les modèles de présentation. Un modèle POTX contient des paramètres de mise en forme, des styles, etc. et peut être utilisé pour créer plusieurs présentations avec la même mise en forme + + PPSX Microsoft PowerPoint Slide Show Un format de présentation pour démarrer le diaporama. + + PPT Format de fichier utilisé par Microsoft PowerPoint + + PPTX Présentation Office Open XML Compressé, le format de fichier basé sur XML développé par Microsoft pour représenter des classeurs, des tableaux, des présentations et des documents de traitement de texte + + Le tableau ci-dessous présente les formats pris en charge pour le téléchargement d'une présentation dans le menu Fichier -> Télécharger comme. Format en entrée Téléchargeable comme ODP JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX OTP JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX POTX JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX PPSX JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX PPT JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX PPTX JPG, ODP, OTP, PDF, PDF/A, PNG, POTX, PPSX, PPTX Veuillez consulter la matrice de conversion sur api.onlyoffice.com pour vérifier s'il est possible de convertir vos présentations dans des formats les plus populaires."
    },
   {
        "id": "HelpfulHints/UsingChat.htm", 
        "title": "Communiquer en temps réel", 
        "body": "Éditeur de Présentations permet de gérer le flux de travail continu par l'ensemble de l'équipe : partager des fichiers et des dossiers, collaborer sur présentations en temps réel, laisser des commentaires pour des fragments de la présentation nécessitant la participation d'une tierce personne, sauvegarder des versions du classeur pour une utilisation ultérieure. Dans l'Éditeur de Présentations il est possible de communiquer avec vos co-auteurs en temps réel en utilisant l'outil intégré Chat et les modules complémentaires utiles, par ex. Telegram ou Rainbow. Pour accéder au Chat et laisser un message pour les autres utilisateurs, cliquez sur l'icône sur la barre latérale gauche, saisissez le texte dans le champ correspondant, cliquez sur le bouton Envoyer. Les messages de discussion sont stockés pendant une session seulement. Pour discuter le contenu de la présentation, il est préférable d'utiliser les commentaires, car ils sont stockés jusqu'à ce que vous décidiez de les supprimer. Tous les messages envoyés par les utilisateurs seront affichés sur le panneau à gauche. S'il y a de nouveaux messages à lire, l'icône chat sera affichée de la manière suivante - . Pour fermer le panneau avec les messages, cliquez sur l'icône encore une fois."
    },
   {
        "id": "HelpfulHints/VersionHistory.htm", 
        "title": "Historique des versions", 
        "body": "L'éditeur Éditeur de Présentations permet de gérer le flux de travail continu par l'ensemble de l'équipe : partager des fichiers et des dossiers, collaborer sur des présentations en temps réel, communiquer directement depuis l'éditeur, laisser des commentaires pour des fragments de la présentation nécessitant la participation d'une tierce personne. Dans l'Éditeur de Présentations vous pouvez afficher l'historique des versions de la présentation sur laquelle vous collaborez. Afficher l'historique des versions : Pour afficher toutes les modifications apportées à la présentation, passez à l'onglet Fichier, sélectionnez l'option Historique des versions sur la barre latérale gauche, ou passez à l'onglet Collaboration, accédez à l'historique des versions en utilisant l'icône Historique des versions de la barre d'outils supérieure. La liste des versions de la présentation s'affichera à gauche comportant le nom de l'auteur de chaque version/révision, la date et l'heure de création. Pour les versions de présentation, le numéro de la version est également indiqué (par exemple ver. 2). Afficher la version Pour savoir exactement quelles modifications ont été apportés à chaque version/révision, vous pouvez voir celle qui vous intéresse en cliquant dessus dans la barre latérale de gauche. Les modifications apportées par l'auteur de la version/révision sont marquées avec la couleur qui est affichée à côté du nom de l'auteur dans la barre latérale gauche. Pour revenir à la version actuelle de la présentation, cliquez sur Fermer l'historique en haut de le liste des versions. Restaurer une version : Si vous souhaitez restaurer l'une des versions précédentes de la présentation, cliquez sur Restaurer au-dessous de la version/révision sélectionnée. Pour en savoir plus sur la gestion des versions et des révisions intermédiaires, et sur la restauration des versions précédentes, veuillez consulter cet article."
    },
   {
        "id": "ProgramInterface/AnimationTab.htm", 
        "title": "Onglet Animation", 
        "body": "L'onglet Animation de l'Éditeur de Présentations permet de gérer des effets d'animation. Vous pouvez ajouter des effets d'animation, définir des trajectoires des effets et configurer d'autres paramètres des animations pour personnaliser votre présentation. Fenêtre de l'Éditeur de Présentations en ligne : Fenêtre de l'Éditeur de Présentations de bureau : En utilisant cet onglet, vous pouvez : sélectionner des effets d'animation, paramétrer la direction du mouvement de chaque effet, ajouter plusieurs animations, changer l'ordre des effets d'animation en utilisant les options Déplacer avant ou Déplacer après, afficher l'aperçu d'une animation, configurer des options de minutage telles que la durée, le retard et la répétition des animations, activer et désactiver le retour à l'état d'origine de l'animation."
    },
   {
        "id": "ProgramInterface/CollaborationTab.htm", 
        "title": "Onglet Collaboration", 
        "body": "L'onglet Collaboration dans l'Éditeur de Présentations permet d'organiser le travail collaboratif sur une présentation. Dans la version en ligne, vous pouvez partager le fichier, sélectionner un mode de co-édition, gérer les commentaires. En mode Commentaires, vous pouvez ajouter et supprimer les commentaires et utiliser le chat. Dans la version de bureau, vous ne pouvez que gérer les commentaires. Fenêtre de l'Éditeur de Présentations en ligne : Fenêtre de l'Éditeur de Présentations de bureau : En utilisant cet onglet, vous pouvez : spécifier les paramètres de partage (disponible uniquement dans la version en ligne), basculer entre les modes d'édition collaborative Strict et Rapide (disponible uniquement dans la version en ligne), ajouter des commentaires à la présentation et les supprimer, ouvrir le panneau de Chat (disponible uniquement dans la version en ligne). parcourir l'historique des versions (disponible uniquement sous version en ligne)."
    },
   {
        "id": "ProgramInterface/FileTab.htm", 
        "title": "Onglet Fichier", 
        "body": "L'onglet Fichier dans l'Éditeur de Présentations permet d'effectuer certaines opérations de base sur le fichier en cours. Fenêtre de l'Éditeur de Présentations en ligne : Fenêtre de l'Éditeur de Présentations de bureau : En utilisant cet onglet, vous pouvez : dans la version en ligne, enregistrer le fichier actuel (si l'option Enregistrement automatique est désactivée), télécharger comme (enregistrer le document dans le format sélectionné sur le disque dur de l'ordinateur), enregistrer la copie sous (enregistrer une copie du document dans le format sélectionné dans les documents du portail), imprimer ou renommer le fichier actuel, dans la version bureau, enregistrer le fichier actuel en conservant le format et l'emplacement actuels à l'aide de l'option Enregistrer ou enregistrer le fichier actuel avec un nom, un emplacement ou un format différent à l'aide de l'option Enregistrer sous, imprimer le fichier. proteger le fichier en utilisant un mot de passe, changer ou supprimer le mot de passe ; protéger le fichier à l'aide d'un mot de passe, modifier ou supprimer le mot de passe (disponible dans la version de bureau uniquement) ; créer une nouvelle présentation ou ouvrir une présentation récemment éditée (disponible dans la version en ligne uniquement), voir des informations générales sur la présentation ou configurer certains paramètres ; gérer les droits d'accès (disponible uniquement dans la version en ligne), accéder aux Paramètres avancés de l'éditeur, dans la version de bureau, ouvrez le dossier où le fichier est stocké dans la fenêtre Explorateur de fichiers. Dans la version en ligne, ouvrez le dossier du module Documents où le fichier est stocké dans un nouvel onglet du navigateur."
    },
   {
        "id": "ProgramInterface/HomeTab.htm", 
        "title": "Onglet Accueil", 
        "body": "L'onglet Accueil dans l'Éditeur de Présentations s'ouvre par défaut lorsque vous ouvrez une présentation. Il permet de définir les paramètres généraux de la diapositive, mettre en forme le texte, insérer des objets, les aligner et les organiser. Fenêtre de l'Éditeur de Présentations en ligne : Fenêtre de l'Éditeur de Présentations de bureau : En utilisant cet onglet, vous pouvez : gérer les diapositives et démarrer le diaporama, mettre en forme le texte dans la zone de texte insérer des zones de texte, des images, des formes, aligner et organiser des objets dans une diapositive, copier/effacer la mise en forme du texte, changer le thème, le jeu de couleurs ou la taille de diapositive."
    },
   {
        "id": "ProgramInterface/InsertTab.htm", 
        "title": "Onglet Insertion", 
        "body": "L'onglet Insertion dans l'Éditeur de Présentations permet d'ajouter des objets visuels et des commentaires dans votre présentation. Fenêtre de l'Éditeur de Présentations en ligne : Fenêtre de l'Éditeur de Présentations de bureau : Sous cet onglet vous pouvez insérer des tableaux, insérer des zones de texte et des objets Text Art, des images, des formes, des graphiques, insérer des commentaires et des liens hypertexte, insérer des pieds de page, de la date et l'heure, du numéro de diapositive.. insérer des équations, symboles, ajouter des enregistrements audio et vidéo stockés sur votre ordinateur (disponible en version de bureau seulement, n'est pas disponible sur Mac OS), Remarque : Il est nécessaire d'installer un pack de codec permettant de lire les fichiers vidéos, par exemple, K-Lite."
    },
   {
        "id": "ProgramInterface/PluginsTab.htm", 
        "title": "Onglet Modules complémentaires", 
        "body": "L'onglet Modules complémentaires dans l'Éditeur de Présentations permet d'accéder à des fonctions d'édition avancées à l'aide de composants tiers disponibles. Ici vous pouvez également utiliser des macros pour simplifier les opérations de routine. Fenêtre de l'Éditeur de Présentations en ligne : Fenêtre de l'Éditeur de Présentations de bureau : Le bouton Paramètres permet d'ouvrir la fenêtre où vous pouvez visualiser et gérer toutes les extensions installées et ajouter vos propres modules. Le bouton Macros permet d'ouvrir la fenêtre où vous pouvez créer vos propres macros et les exécuter. Pour en savoir plus sur les macros, veuillez vous référer à la documentation de notre API. Actuellement, les modules suivants sont disponibles : Envoyer permet d'envoyer la présentation par e-mail à l'aide d'un client de messagerie installé sur votre ordinateur (disponible en version de bureau seulement), Code en surbrillance sert à surligner la syntaxe du code en sélectionnant la langue, le style, la couleur de fond approprié etc., Éditeur de photos sert à modifier les images : rogner, retourner, pivoter, dessiner les lignes et le formes, ajouter des icônes et du texte, charger l'image de masque et appliquer des filtres comme Niveaux de gris, Inverser, Sépia, Flou, Embosser, Affûter etc., Thésaurus sert à trouver les synonymes et les antonymes et les utiliser à remplacer le mot sélectionné, Traducteur sert à traduire le texte sélectionné dans des langues disponibles, Remarque : ce module complémentaire ne fonctionne pas sur Internet Explorer. You Tube permet d'ajouter les videos YouTube dans votre présentation. Pour en savoir plus sur les modules complémentaires, veuillez vous référer à la documentation de notre API. Tous les exemples de modules open source actuels sont disponibles sur GitHub."
    },
   {
        "id": "ProgramInterface/ProgramInterface.htm", 
        "title": "Présentation de l'interface utilisateur de l'Éditeur de Présentations", 
        "body": "Éditeur de Présentations utilise une interface à onglets dans laquelle les commandes d'édition sont regroupées en onglets par fonctionnalité. La fenêtre principale de l'Éditeur de Présentations en ligne : La fenêtre principale de l'Éditeur de Présentations de bureau : L'interface de l'éditeur est composée des éléments principaux suivants : L'en-tête de l'éditeur affiche le logo, les onglets de présentations ouvertes et leurs titres et les onglets du menu. Dans la partie gauche de l'en-tête de l'éditeur se trouvent les boutons Enregistrer, Imprimer le fichier, Annuler et Rétablir. Dans la partie droite de l'en-tête de l'éditeur, le nom de l'utilisateur est affiché ainsi que les icônes suivantes : Ouvrir l'emplacement du fichier - dans la version de bureau, elle permet d'ouvrir le dossier où le fichier est stocké dans la fenêtre Explorateur de fichiers. Dans la version en ligne, elle permet d'ouvrir le dossier du module Documents où le fichier est stocké dans un nouvel onglet du navigateur. Gérer les droits d'accès au document - (disponible uniquement dans la version en ligne) permet de définir les droits d'accès aux documents stockés dans le cloud. Marquer en tant que favori - cliquez sur l'étoile pour ajouter le fichier aux favoris et pour le retrouver rapidement. Ce n'est qu'un fichier de raccourcis car le fichier lui-même est dans l'emplacement de stockage d'origine. Le fichier réel n'est pas supprimé quand vous le supprimez de Favoris. Recherche - permet de rechercher dans la présentation un mot ou un symbole particulier, etc. La barre d'outils supérieure affiche un ensemble de commandes d'édition en fonction de l'onglet de menu sélectionné. Actuellement, les onglets suivants sont disponibles : Fichier, Accueil, Insertion, Collaboration, Protection, Modules complémentaires. Des options Copier et Coller sont toujours disponibles dans la partie gauche de la Barre d'outils supérieure, quel que soit l'onglet sélectionné. La Barre d'état en bas de la fenêtre de l'éditeur contient l'icône Démarrer le diaporama et certains outils de navigation : l'indicateur de numéro de diapositive et les boutons de zoom. La Barre d'état affiche également certaines notifications (telles que Toutes les modifications enregistrées ou Connection est perdue quand l'éditeur ne pavient pas à se connecter etc.) et permet de définir la langue du texte et d'activer la vérification orthographique. La barre latérale gauche contient les icônes suivantes : - permet d'utiliser l'outil Rechercher et remplacer , - permet d'ouvrir le panneau Commentaires , - (disponible dans la version en ligne seulement) permet d'ouvrir le panneau de Chat , - (disponible dans la version en ligne seulement) permet de contacter notre équipe d'assistance technique, - (disponible dans la version en ligne seulement) permet de visualiser les informations sur le programme. La barre latérale droite permet d'ajuster les paramètres supplémentaires de différents objets. Lorsque vous sélectionnez un objet particulier sur une diapositive, l'icône correspondante est activée dans la barre latérale droite. Cliquez sur cette icône pour développer la barre latérale droite. Les Règles horizontales et verticales vous aident à placer des objets sur une diapositive et permettent de définir des tabulations et des retraits de paragraphe dans les zones de texte. La Zone de travail permet d'afficher le contenu de la présentation, d'entrer et de modifier les données. La Barre de défilement sur la droite permet de faire défiler la présentation de haut en bas. Pour plus de commodité, vous pouvez masquer certains composants et les afficher à nouveau lorsque cela est nécessaire. Pour en savoir plus sur l'ajustement des paramètres d'affichage, reportez-vous à cette page."
    },
   {
        "id": "ProgramInterface/TransitionsTab.htm", 
        "title": "Onglet Transitions", 
        "body": "L'onglet Transitions de l'Éditeur de Présentations permet de gérer les transitions entre les diapositives. Vous pouvez ajouter des effets de transition, définir la durée de la transition et configurer d'autres paramètres des transitions entre les diapositives. Fenêtre de l'Éditeur de Présentations en ligne : Fenêtre de l'Éditeur de Présentations de bureau : En utilisant cet onglet, vous pouvez : sélectionner l'effet de transition, configurer les paramètres pour chaque effet de transition, définir la durée de transition, afficher l'aperçu de transition une fois paramétrée, contrôler la durée d'affichage de chaque diapositive à l'aide des options Démarrer en cliquant et Retard, appliquer la même transition à toutes les diapositives de la présentation en cliquant sur le bouton Appliquer à toutes les diapositives."
    },
   {
        "id": "ProgramInterface/ViewTab.htm", 
        "title": "Onglet Affichage", 
        "body": "L'onglet Affichage dans Presentation Editor permet de gérer l'apparence de la présentation pendant que vous travaillez sur celui-ci. L'onglet dans l'éditeur en ligne Presentation Editor: L'onglet dans l'éditeur de bureau Presentation Editor: Les options d'affichage disponibles sous cet onglet: Zoom permet de zoomer et dézoomer sur une page du document. Ajuster à la diapositive permet de redimensionner la diapositive pour afficher une diapositive entière sur l'écran. Ajuster à la largeur permet de redimensionner la diapositive pour l'adapter à la largeur de l'écran. Thème d'interface permet de modifier le thème d'interface en choisissant le thème Identique à système, Clair, Classique clair, Sombre ou Contraste élevé sombre. Les options suivantes permettent de choisir les éléments à afficher ou à cacher. Cachez les cases appropriées aux éléments que vous souhaitez rendre visible: Notes pour rendre visible le panneau de notes. Règles pour rendre visible les règles. Guides et Quadrillage pour positionner correctement des objets sur la diapositive. Toujours afficher la barre d'outils pour rendre visible la barre d'outils supérieure. Barre d'état pour rendre visible la barre d'état. Panneau gauche pour rendre visible le panneau gauche. Panneau droit pour rendre visible le panneau droit."
    },
   {
        "id": "UsageInstructions/AddHyperlinks.htm", 
        "title": "Ajouter des liens hypertextes", 
        "body": "Pour ajouter un lien hypertexte dans l'Éditeur de Présentations, placez le curseur dans le bloc de texte à une position où vous voulez ajouter un lien hypertexte, passez à l'onglet Insertion de la barre d'outils supérieure, cliquez sur l'icône Lien hypertexte de la barre d'outils supérieure, ensuite la fenêtre Paramètre du lien hypertexte s'affiche où vous pouvez configurer les paramètres du lien hypertexte : Sélectionnez un type de lien à insérer : Utilisez l'option Lien externe et entrez une URL au format http://www.example.com dans le champ Lien vers si vous avez besoin d'ajouter un lien hypertexte menant vers un site externe. Si vous avez besoin d'ajouter un lien hypertexte vers un fichier local, entrez une URL au format file://path/Presentation.pptx (pour Windows) ou file:///path/Presentation.pptx (pour MacOS et Linux) dans le champ Lien vers. Le lien hypertexte file://path/Presentation.pptx ou file:///path/Presentation.pptx ne peut être ouvert que dans la version de bureau de l'éditeur. Dans l'éditeur web il est seulement possible d'ajouter un lien hypertexte sans pouvoir l'ouvrir. Utilisez l'option Emplacement dans cette présentation et sélectionnez l'une des options si vous avez besoin d'ajouter un lien hypertexte menant à une certaine diapositive dans la même présentation. Les options disponibles sont les suivantes : Diapositive suivante, Diapositive précédente, Première diapositive, Dernière diapositive, Diapositive dont les numéro est indiqué. Afficher - entrez un texte qui sera cliquable et mène vers l'adresse web / à la diapositive spécifiée dans le champ supérieur. Texte de l'infobulle - entrez un texte qui sera visible dans une petite fenêtre contextuelle offrant une courte note ou étiquette lorsque vous placez le curseur sur un lien hypertexte. Cliquez sur le bouton OK. Pour ajouter un lien hypertexte, vous pouvez également cliquer avec le bouton droit de la souris et sélectionner l'option Lien hypertexte ou utiliser la combinaison des touches Ctrl+K. Remarque : il est également possible de sélectionner un caractère, mot, une combinaison de mots avec la souris ou à l'aide du clavier et ouvrez la fenêtre Paramètres du lien hypertexte comme mentionné ci-dessus. Dans ce cas, le champ Afficher se remplit avec le texte que vous avez sélectionné. Si vous placez le curseur sur le lien hypertexte ajouté, vous verrez l'info-bulle contenant le texte que vous avez spécifié. Pour suivre le lien, appuyez sur la touche CTRL et cliquez sur le lien dans votre présentation. Pour modifier ou supprimer le lien hypertexte ajouté, cliquez sur le lien avec le bouton droit de la souris, sélectionnez l'option Lien hypertexte dans le menu contextuel et ensuite l'action à effectuer - Modifier le lien hypertexte ou Supprimer le lien hypertexte."
    },
   {
        "id": "UsageInstructions/AddingAnimations.htm", 
        "title": "Ajouter des animations", 
        "body": "Animation est un effet visuel permettant d'animer du texte, des objets et des graphiques pour rendre votre présentation plus dynamique et pour accentuer une information importante. Vous pouvez contrôler le trajectoire, la couleur et la taille du texte, des objets et des graphiques. Appliquer un effet d'animation passez à l'onglet Animation de la barre d'outils supérieure. sélectionnez le texte, l'objet ou l'élément graphique à animer. sélectionnez l'Animation dans la galerie des effets d'animation. paramétrez la direction du mouvement de l'animation en cliquant sur Paramètres à côté de la galerie des effets d'animation. Le choix des paramètres dans la liste déroulante dépend de l'effet choisi. Vous pouvez afficher l'aperçu des effets d'animation sur la diapositive actuelle. Par défaut, des effets d'animation démarrent automatiquement lorsque vous les ajoutez à la diapositive mais vous pouvez désactiver cette option. Cliquez sur la liste déroulante Aperçu dans l'onglet Animation et sélectionnez le mode d'aperçu : Aperçu pour afficher l'aperçu lorsque vous cliquez sur le bouton Aperçu, Aperçu partiel pour afficher l'aperçu automatiquement lorsque vous ajoutez une animation à la diapositive ou remplacez l'animation existante. Types d'animations La liste de toutes animations disponibles se trouve dans la galerie des effets d'animation. Cliquez sur la flèche déroulante pour ouvrir la galerie. Chaque effet d'animation est représenté sous forme d'une étoile. Les effets d'animation sont regroupées selon le moment auquel ils se produisent : Les effets d'Entrée permettent de faire apparaître des objets sur une diapositive et sont colorés en vert dans la galerie. Les effets d'Accentuation permettent de modifier la taille et la couleur de l'objet pour le mettre en évidence et pour attirer l'attention de l'auditoire et sont colorés en jaune ou sont bicolores dans la galerie. Les effets de Sortie permettent de faire disparaître des objets de la diapositive et sont colorés en rouge dans la galerie. Les Trajectoires du mouvement permettent de déterminer le déplacement de l'objet et son trajectoire. Les icônes dans la galerie affichent le chemin proposé. L'option Chemin personnalisé est également disponible. Pour déterminer un chemin personnalisé, marquez les points du chemin avec le bouton gauche de la souris. Double-cliquez sur le bouton gauche de la souris, quand tout est prêt. Faites défiler la page vers le bas pour naviguer tous les effets disponibles dans la galerie. Si vous ne trouvez pas l'animation requise dans la galerie, cliquez sur Afficher plus d'effets en bas de la galerie. Ici, vous allez trouver la liste complète des effets d'animation. En outre, les effets sont regroupés par leur impact visuel sur l'audience. Les effets d'Entrée, d'Accentuation et de Sortie sont regroupés par Simple, Discret, Modérer et Captivant. Les Trajectoires du mouvement sont regroupés par Simple, Discret et Modérer. Par défaut, l'option Effet d'aperçu est activée, désactivez-la si vous n'en avez pas besoin. Appliquer plusieurs animations Vous pouvez appliquer plusieurs effets d'animation au même objet. Pour ce faire, Cliquez sur le bouton Ajouter une animation sous l'onglet Animation. La liste des effets d'animation s'affichera. Répétez les opérations 3 et 4 ci-dessus pour appliquer encore un effet d'animation. Si vous utilisez la galerie des effets d'animation au lieu du bouton Ajouter une animation, le premier effet d'animation sera remplacé par un nouvel. Les nombres dans des petits carrés gris indiquent l'ordre des effets appliqués. Lorsque vous appliquez plusieurs effets à un objet, l'icône Multiple apparaîtra dans la galerie des effets d'animation. Changer l'ordre des effets d'animation sur une diapositive Cliquez sur le symbole carré indiquant l'effet d'animation. Cliquez sur l'une des flèches ou sous l'onglet Animation pour modifier l'ordre d'apparition sur la diapositive. Déterminer le minutage d'une animation Utilisez les options de minutage sous l'onglet Animation pour déterminer le début, la durée, le retard, la répétition et le retour à l'état d'origine de l'animation. Options de démarrage d'une animation. Au clic - l'animation va démarrer lorsque vous cliquez sur la diapositive. C'est l'option par défaut. Avec la précédente - l'animation va démarrer au même temps que l'animation précédente. Après la précédente - l'animation va démarrer immédiatement après l'animation précédente. Remarque :</b> Les effets d'animation sont numérotés automatiquement sur une diapositive. Toutes les animations définies à démarrer Avec la précédente ou Après la précédente prennent le numéro de l'animation avec laquelle ils sont connectées. Options de déclenchement d'une animation : Cliquez sur le bouton Déclencheur et sélectionnez l'une des options appropriée : Séquence de clics pour démarrer l'animation suivante chaque fois que vous cliquez n'importe où sur une diapositive. C'est l'option par défaut. Au clic sur pour démarrer l'animation lorsque vous cliquez sur l'objet que vous avez sélectionné dans la liste déroulante. Autres options de minutage Durée - utilisez cette option pour contrôler la durée d'affichage de chaque animation. Sélectionnez l'une des options disponibles dans le menu ou saisissez votre valeur. Retard - utilisez cette option si vous voulez préciser le délai dans lequel une animation sera affichée ou si vous avez besoin d'une pause dans la lecture des effets. Sélectionnez une valeur appropriée en utilisant les flèches pu ou saisissez votre valeur mesurée en secondes. Répéter - utilisez cette option si vous souhaitez afficher une animation plusieurs fois. Activez l'option Répéter et sélectionnez l'une des options disponibles ou saisissez votre valeur. Rembobiner - activez cette option si vous souhaitez revenir à l'état d'origine d'un effet après son affichage."
    },
   {
        "id": "UsageInstructions/AlignArrangeObjects.htm", 
        "title": "Aligner et organiser des objets dans une diapositive", 
        "body": "Les formes automatiques, images, graphiques et blocs de texte ajoutés peuvent être alignés, regroupés, triés, répartis horizontalement et verticalement dans une diapositive dans l'Éditeur de Présentations. Pour effectuer une de ces actions, premièrement sélectionnez un objet ou plusieurs objets dans la zone de travail. Pour sélectionner plusieurs objets, maintenez la touche Ctrl enfoncée et cliquez avec le bouton gauche sur les objets nécessaires. Pour sélectionner un bloc de texte, cliquez sur son bord, pas sur le texte à l'intérieur. Après quoi vous pouvez utiliser soit les icônes de la barre d'outils supérieure décrites ci-après soit les options similaires du menu contextuel. Aligner des objets Pour aligner deux ou plusieurs objets sélectionnés, Cliquez sur l'icône Aligner une forme située dans l'onglet Accueil de la barre d'outils supérieure et sélectionnez une des options disponibles : Aligner sur la diapositive pour aligner les objets par rapport aux bords de la diapositive, Aligner les objets sélectionnés (cette option est sélectionnée par défaut) pour aligner les objets les uns par rapport aux autres, Cliquez à nouveau sur l'icône Aligner la forme et sélectionnez le type d'alignement nécessaire dans la liste : Aligner à gauche - pour aligner les objets horizontalement sur le côté gauche de l'objet le plus à gauche/le bord gauche de la diapositive, Aligner au centre - pour aligner les objets horizontalement au centre/centre de la diapositive, Aligner à droite - pour aligner les objets horizontalement sur le côté droit de l'objet le plus à droite/le bord droit de la diapositive, Aligner en haut - pour aligner les objets verticalement sur le bord supérieur de l'objet le plus haut/le bord supérieur de la diapositive, Aligner au milieu - pour aligner les objets verticalement par leur milieu/ milieu de la diapositive, Aligner en bas - pour aligner verticalement les objets par le bord inférieur de l'objet le plus bas/bord inférieur de la diapositive. Vous pouvez également cliquer avec le bouton droit sur les objets sélectionnés, choisir l'option Aligner dans le menu contextuel, puis utiliser une des options d’alignement disponibles. Si vous voulez aligner un seul objet, il peut être aligné par rapport aux bords de la diapositive. L'option Aligner sur la diapositive est sélectionnée par défaut dans ce cas. Distribuer des objets Pour distribuer horizontalement ou verticalement trois objets sélectionnés ou plus de façon à ce que la même distance apparaisse entre eux, Cliquez sur l'icône Aligner située dans l'onglet Accueil de la barre d'outils supérieure et sélectionnez une des options disponibles : Aligner sur la diapositive pour répartir les objets entre les bords de la diapositive, Aligner les objets sélectionnés (cette option est sélectionnée par défaut) pour distribuer les objets entre les deux objets les plus externes sélectionnés, Cliquez à nouveau sur l'icône Aligner la forme et sélectionnez le type de distribution nécessaire dans la liste : Distribuer horizontalement - pour répartir uniformément les objets entre les bords gauche et droit des objets sélectionnés et les bords gauche et droit de la diapositive. Distribuer verticalement - pour répartir uniformément les objets entre les bords supérieurs et inférieurs des objets sélectionnés et les bords supérieurs et inférieurs de la diapositive.. Vous pouvez également cliquer avec le bouton droit sur les objets sélectionnés, choisir l'option Aligner dans le menu contextuel, puis utiliser une des options de distribution disponibles. Remarque : les options de distribution sont désactivées si vous sélectionnez moins de trois objets. Grouper plusieurs objets Pour grouper deux ou plusieurs objets sélectionnés ou les dissocier, cliquez sur l'icône Organiser une forme dans l'onglet Accueil de la barre d'outils supérieure et sélectionnez l'option nécessaire depuis la liste : Grouper - pour combiner plusieurs objets de sorte que vous puissiez les faire pivoter, déplacer, redimensionner, aligner, organiser, copier, coller, mettre en forme simultanément comme un objet unique. Dissocier - pour dissocier le groupe sélectionné d'objets préalablement combinés. Vous pouvez également cliquer avec le bouton droit sur les objets sélectionnés, choisir l'option Ordonner dans le menu contextuel, puis utiliser l'option Grouper ou Dissocier. Remarque : l'option Grouper est désactivée si vous sélectionnez moins de deux objets. L'option Dégrouper n'est disponible que lorsqu'un groupe des objets précédemment joints est sélectionné. Organiser plusieurs objets Pour organiser les objets sélectionnés (c'est à dire pour modifier leur ordre lorsque plusieurs objets se chevauchent), cliquez sur l'icône Organiser une forme dans l'onglet Accueil de la barre d'outils supérieure et sélectionnez le type nécessaire depuis la liste. Mettre au premier plan - pour déplacer les objets devant tous les autres objets, Avancer d'un plan - pour déplacer les objets d'un niveau en avant par rapport à d'autres objets. Mettre en arrière-plan - pour déplacer les objets derrière tous les autres objets, Reculer d'un plan - pour déplacer les objets d'un niveau en arrière par rapport à d'autres objets. Vous pouvez également cliquer avec le bouton droit sur les objets sélectionnés, choisir l'option Organiser dans le menu contextuel, puis utiliser une des options de rangement disponibles."
    },
   {
        "id": "UsageInstructions/ApplyTransitions.htm", 
        "title": "Appliquer des transitions", 
        "body": "Une transition est un effet d'animation qui apparaît entre deux diapositives quand une diapositive avance vers la suivante pendant la démonstration. Dans l'Éditeur de Présentations, vous pouvez appliquer une même transition à toutes les diapositives ou à de différentes transitions à chaque diapositive séparée et régler leurs propriétés. Pour appliquer une transition à une seule diapositive ou à plusieurs diapositives sélectionnées : Passez à l'onglet Transitions de la barre d'outils supérieure. Sélectionnez une diapositive nécessaire (ou plusieurs diapositives de la liste) à laquelle vous voulez appliquer une transition. Sélectionnez une transition appropriée parmi les transitions disponibles sous l'onglet Transitions: Aucune, Fondu, Expulsion, Effacement, Diviser, Découvrir, Couvrir, Horloge, Zoom. Cliquez sur le bouton Paramètres pour sélectionner l'un des effets disponibles et définir le mode d'apparition de l'effet. Par exemple, si vous appliquez l'effet Zoom, vous pouvez sélectionner une des options suivantes: Zoom avant, Zoom arrière ou Zoom et rotation. Spécifiez la durée de la transition dans la boîte Durée, saisissez ou sélectionnez la valeur appropriée mesurée en secondes. Cliquez sur le bouton Aperçu pour visualiser la diapositive avec la transition appliquée dans la zone d'édition. Précisez combien de temps la diapositive doit être affichée avant d'avancer vers une autre: Démarrer en cliquant - cochez cette case si vous ne voulez pas limiter le temps de l'affichage de la diapositive sélectionnée. La diapositive n'avance vers une autre qu'après un clic de la souris. Retard utilisez cette option si vous voulez préciser le temps de l'affichage d'une diapositive avant son avancement vers une autre. Cochez cette case et saisissez la valeur appropriée, mesurée en secondes. Remarque: si vous ne cochez que la case Retard, les diapositives avancent automatiquement avec un intervalle de temps indiqué. Si vous cochez les deux cases Démarrer en cliquant et Retard et précisez la valeur de temps nécessaire, l'avancement des diapositives se fait aussi automatiquement, mais vous aurez la possibilité de cliquer sur la diapositive pour vous avancer vers une autre. Pour appliquer une transition à toutes les diapositives de la présentation cliquez sur le bouton Appliquer à toutes les diapositives sous l'onglet Transitions. Pour supprimer une transition: sélectionnez une diapositive nécessaire et choisissez l'option Aucun parmi les effets disponibles sous l'onglet Transitions. Pour supprimer toutes les transitions: sélectionnez une diapositive, choisissez l'option Aucun parmi les effets disponibles et appuyez sur Appliquer à toutes les diapositives sous l'onglet Transitions."
    },
   {
        "id": "UsageInstructions/CommunicationPlugins.htm", 
        "title": "Communiquer lors de l'édition", 
        "body": "Dans l'Éditeur de Présentations ONLYOFFICE vous pouvez toujours rester en contact avec vos collègues et utiliser des messageries en ligne populaires, par exemple Telegram et Rainbow. Les plug-ins Telegram et Rainbow ne sont pas installés par défaut. Pour en savoir plus sur leur installation, veuillez consulter l'article approprié : Ajouter des modules complémentaires à ONLYOFFICE Desktop Editors Adding plugins to ONLYOFFICE Cloud, ou Ajouter de nouveaux modules complémentaires aux éditeurs de serveur . Telegram Pour commencer à chatter dans le plug-in Telegram, Passez à l'onglet Modules complémentaires et cliquez sur Telegram, saisissez votre numéro de téléphone dans le champ correspondant, cochez la case Rester connecté lorsque vous souhaitez enregistrer vos données pour la session en cours, ensuite cliquez sur le bouton Suivant, saisissez le code reçu dans votre application Telegram, ou connectez-vous en utilisant le Code QR, ouvrez l'application Telegram sur votre téléphone, go to Settings > Devices > Numériser QR, numérisez l'image pour vous connecter. Vous pouvez maintenant utiliser Telegram au sein de l'interface des éditeurs ONLYOFFICE. Rainbow Pour commencer à chatter dans le plug-in Rainbow, Passez à l'onglet Modules complémentaires et cliquez sur Rainbow, enregistrez un nouveau compte en cliquant sur le bouton Inscription ou connectez-vous à un compte déjà créé. Pour le faire, saisissez votre email dans le champ correspondant et cliquez sur Continuer, puis saisissez le mot de passe de votre compte, cochez la case Maintenir ma session lorsque vous souhaitez enregistrer vos données pour la session en cours, ensuite cliquez sur le bouton Connecter. Vous êtes maintenant prêt à chatter dans Rainbow et travailler au sein de l'interface des éditeurs ONLYOFFICE en même temps."
    },
   {
        "id": "UsageInstructions/CopyClearFormatting.htm", 
        "title": "Copier/effacer la mise en forme", 
        "body": "Pour copier une certaine mise en forme du texte dans l'Éditeur de Présentations, sélectionnez le fragment du texte contenant la mise en forme à copier en utilisant la souris ou le clavier, cliquez sur l'icône Copier le style dans l'onglet Accueil de la barre d'outils supérieure ( le pointeur de la souris aura la forme suivante ), sélectionnez le fragment de texte à mettre en forme. Pour appliquer la mise en forme copiée aux plusieurs fragments du texte, sélectionnez le fragment du texte contenant la mise en forme à copier en utilisant la souris ou le clavier, double-cliquez sur l'icône Copier le style dans l'onglet Accueil de la barre d'outils supérieure (le pointeur de la souris ressemblera à ceci et l'icône Copier le style restera sélectionnée : ), sélectionnez les fragments du texte nécessaires un par un pour appliquer la même mise en forme pour chacun d'eux, pour quitter ce mode, cliquez à nouveau sur l'icône Copier le style ou appuyez sur la touche Échap du clavier. Pour effacer la mise en forme appliquée de votre texte, sélectionnez le fragment de texte dont vous souhaitez supprimer la mise en forme, cliquez sur l'icône Effacer le style dans l'onglet Accueil de la barre d'outils supérieure."
    },
   {
        "id": "UsageInstructions/CopyPasteUndoRedo.htm", 
        "title": "Copier/coller les données, annuler/rétablir vos actions", 
        "body": "Utiliser les opérations de base du presse-papiers Pour couper, copier et coller les objets sélectionnés (diapositives, passages de texte, formes automatiques) dans Éditeur de Présentations ou annuler/rétablir vos actions, utilisez les icônes correspondantes sur la barre d'outils supérieure : Couper – sélectionnez un objet et utilisez l'option Couper du menu contextuel pour effacer la sélection et l'envoyer dans le presse-papiers de l'ordinateur. Les données coupées peuvent être insérées ensuite à un autre endroit de la même présentation Copier – sélectionnez un objet et utilisez l'option Copier dans le menu contextuel, ou l'icône Copier de la barre d'outils supérieure pour copier la sélection dans le presse-papiers de l'ordinateur. L'objet copié peut être inséré ensuite à un autre endroit dans la même présentation. Coller – trouvez l'endroit dans votre présentation où vous voulez coller l'objet précédemment copié et utilisez l'option Coller du menu contextuel ou l'icône Coller de la barre d'outils supérieure. L'objet sera inséré à la position actuelle du curseur. L'objet peut être copié depuis la même présentation. Dans la version en ligne, les combinaisons de touches suivantes ne sont utilisées que pour copier ou coller des données de/vers une autre présentation ou un autre programme, dans la version de bureau, les boutons/options de menu et les combinaisons de touches correspondantes peuvent être utilisées pour toute opération copier/coller : Ctrl+C pour copier ; Ctrl+V pour coller ; Ctrl+X pour couper. Utiliser la fonctionnalité Collage spécial Note : Pendant le travail collaboratif, la fonctionnalité Collage spécial n'est disponible que pour le mode de collaboration Strict. Une fois le texte copié collé, le bouton Collage spécial apparaît à côté du passage de texte inséré. Cliquez sur ce bouton pour sélectionner l'option de collage requise ou utilisez la touche Ctrl en combinaison avec la touche indiquée entre parenthèses à côté de l'option requise. Lors du collage de passages de texte, les options suivantes sont disponibles : Utiliser le thème de destination (Ctrl+H) - permet d'appliquer la mise en forme spécifiée par le thème de la présentation en cours. Cette option est utilisée par défaut. Garder la mise en forme de la source (Ctrl+K) - permet de conserver la mise en forme de la source des données copiées. Image (Ctrl+U) - permet de coller le texte en tant qu'image afin qu'il ne puisse pas être modifié. Conserver le texte uniquement (Ctrl+T) - permet de coller le texte sans sa mise en forme d'origine. Lorsque vous collez des objets (formes automatiques, graphiques, tableaux), les options suivantes sont disponibles : Utiliser le thème de destination (Ctrl+H) - permet d'appliquer la mise en forme spécifiée par le thème de la présentation en cours. Cette option est utilisée par défaut. Image (Ctrl+U) - permet de coller l'objet en tant qu'image afin qu'il ne puisse pas être modifié. Pour activer / désactiver l'affichage du bouton Collage spécial lorsque vous collez le texte, passez à l'onglet Fichier > Paramètres avancés... et cochez / décochez la case Afficher le bouton \"Options de collage\" lorsque le contenu est collé. Utiliser les opérations Annuler/Rétablir Pour effectuer les opérations annuler/rétablir, utilisez les icônes correspondantes dans la partie gauche de l'en-tête de l'éditeur ou les raccourcis clavier : Annuler – utilisez l'icône Annuler pour annuler la dernière action effectuée. Rétablir – utilisez l'icône Rétablir pour rétablir la dernière action annulée. Vous pouvez aussi utiliser la combinaison de touches Ctrl+Z pour annuler ou pour rétablir Ctrl+Y. Remarque : lorsque vous co-éditez une présentation en mode Rapide, la possibilité de Rétablir la dernière opération annulée n'est pas disponible."
    },
   {
        "id": "UsageInstructions/CreateLists.htm", 
        "title": "Créer des listes", 
        "body": "Pour créer une liste dans l'Éditeur de Présentations, placez le curseur dans le bloc de texte à la position où vous voulez commencer la liste (cela peut être une nouvelle ligne ou le texte déjà saisi), passez à l'onglet Accueil de la barre d'outils supérieure, sélectionnez le type de liste à créer : Liste non ordonnée avec des marqueurs est créée à l'aide de l'icône Puces de la barre d'outils supérieure Liste ordonnée avec numérotage spécial est créée à l'aide de l'icône Numérotation de la barre d'outils supérieure. Remarque : cliquez sur la flèche vers le bas à côté de l'icône Puces ou Numérotation pour sélectionner le format de puces ou de numérotation souhaité. appuyez sur la touche Entrée à la fin de la ligne pour ajouter un nouvel élément à la liste. Pour terminer la liste, appuyez sur la touche Retour arrière et continuez le travail. Il est aussi possible de modifier le retrait et l'imbrication des listes en utilisant des icônes Diminuer le retrait et Augmenter le retrait sous l'onglet Acceuil de la barre barre d'outils supérieure. Remarque : on peut configurer les paramètres du retrait et de l'espacement supplémentaire sur la barre latérale droite dans la fenêtre Paramètres avancés. Pour en savoir plus, consultez Insertion en mise en forme du texte. Configurer les paramètres de la liste Pour configurer les paramètres de la liste comme la puce, la taille et la couleur : cliquez sur l'élément de la liste actuelle ou sélectionnez le texte à partir duquel vous souhaitez créer une liste, cliquez sur l'icône Puces ou Numérotation sous l'onglet Accueil dans la barre d'outils en haut, sélectionnez l'option Paramètres de la liste, la fenêtre Paramètres de la liste s'affiche. La fenêtre Paramètres de la liste à puces se présente sous cet aspect : Type - permet de sélectionner le caractère nécessaire utilisé pour la liste. Lorsque vous cliquez sur l'option Nouvelle puce, la fenêtre Symbole s'ouvre et vous pouvez choisir l'un des caractères disponibles. Vous pouvez également ajouter un nouveau symbole. Pour en savoir plus sur le travail avec les symboles, veuillez consulter cet article. Lorsque vous cliquez sur l'option Nouvelle image, un nouveau champ Importer va apparaître dans lequel vous pouvez choisir de nouvelles images pour les puces Depuis un fichier, D'une URL, ou À partir de l'espace de stockage. Taille - permet de sélectionner la taille des puces nécessaire en fonction de la taille actuelle du texte. La taille peut être configuré de 25% à 400%. Couleur - permet de choisir la couleur des puces nécessaire. Vous pouvez choisir l'une des couleurs de thème, ou des couleurs standard de la palette ou définir la couleur personnalisée. La fenêtre des paramètres de la liste numérotée se présente sous cet aspect : Type - permet de sélectionner le format des numéros nécessaire utilisé pour la liste. Taille - permet de sélectionner la taille des numéros nécessaire en fonction de la taille actuelle du texte. La taille peut être configuré de 25% à 400%. Commencer par - permet de sélectionner le numéro de séquence nécessaire à partir duquel une liste numérotée commence. Couleur - permet de choisir la couleur des numéros nécessaire. Vous pouvez choisir l'une des couleurs de thème, ou des couleurs standard de la palette ou définir la couleur personnalisée. Cliquez sur OK pour appliquer toutes les modifications et fermer la fenêtre des paramètres."
    },
   {
        "id": "UsageInstructions/FillObjectsSelectColor.htm", 
        "title": "Remplir des objets et sélectionner des couleurs", 
        "body": "Dans l'Éditeur de Présentations, vous pouvez appliquer de différents remplissages pour l'arrière-plan de diapositives ainsi que pour les formes automatiques et l'arrière-plan de Text Art. Sélectionnez un objet. Pour modifier le remplissage de l'arrière-plan de la diapositive, sélectionnez les diapositives voulues dans la liste des diapositives. L'onglet Paramètres de la diapositive sera activé sur la barre latérale droite. Pour modifier le remplissage de la forme automatique, cliquez avec le bouton gauche de la souris la forme automatique concernée. L'onglet Paramètres de la forme sera activé sur la barre latérale droite. Pour modifier le remplissage de la police Text Art, cliquez avec le bouton gauche sur l'objet texte concerné. L'onglet Paramètres Text Art sera activé sur la barre latérale droite. Définissez le type de remplissage nécessaire. Réglez les paramètres du remplissage sélectionné (voir la description détaillée de chaque type de remplissage ci-après) Remarque : Quel que soit le type de remplissage sélectionné, vous pouvez toujours régler le niveau d'Opacité des formes automatiques en faisant glisser le curseur ou en saisissant la valeur de pourcentage à la main. La valeur par défaut est 100%. Elle correspond à l'opacité complète. La valeur 0% correspond à la transparence totale. Les types de remplissage disponibles sont les suivants Couleur de remplissage - sélectionnez cette option pour spécifier la couleur unie à utiliser pour remplir l'espace intérieur de la forme / diapositive sélectionnée. Cliquez sur la case de couleur et sélectionnez la couleur nécessaire à partir de l'ensemble de couleurs disponibles ou spécifiez n'importe quelle couleur que vous aimez : Couleurs de thème - les couleurs qui correspondent à la palette de couleurs sélectionnée de la présentation. Une fois que vous avez appliqué un thème ou un jeu de couleurs différent, le jeu de Couleurs du thème change. Couleurs standard - le jeu de couleurs par défaut. Couleur personnalisée - choisissez cette option si il n'y a pas de couleur nécessaire dans les palettes disponibles. Sélectionnez la gamme de couleurs nécessaire en déplaçant le curseur vertical et définissez la couleur spécifique en faisant glisser le sélecteur de couleur dans le grand champ de couleur carré. Une fois que vous sélectionnez une couleur avec le sélecteur de couleur, les valeurs de couleur appropriées RGB et sRGB seront affichées dans les champs à droite. Vous pouvez également spécifier une couleur sur la base du modèle de couleur RGB en entrant les valeurs numériques nécessaires dans les champs R, G, B (rouge, vert, bleu) ou saisir le code hexadécimal dans le champ sRGB marqué par le signe #. La couleur sélectionnée apparaît dans la case de prévisualisation Nouveau. Si l'objet a déjà été rempli d'une couleur personnalisée, cette couleur sera affichée dans la case bafin que vous puissiez comparer les couleurs originales et modifiées. Lorsque la couleur est spécifiée, cliquez sur le bouton Ajouter : La couleur personnalisée sera appliquée à votre objet et ajoutée dans la palette Couleur personnalisée du menu. Remarque : vous pouvez utiliser les mêmes types de couleurs lors de la sélection de la couleur du trait de la forme automatique, ou lors du changement de la couleur de police ou de l'arrière-plan de tableau ou la couleur de bordure. Dégradé - sélectionnez cette option pour spécifier deux couleurs pour créer une transition douce entre elles et remplir la forme. Cliquez sur l'icône Paramètres de la forme pour ouvrir le menu de Remplissage : Style - choisissez une des options disponibles : Linéaire (la transition se fait selon un axe horizontal/vertical ou en diagonale, sous l'angle de 45 degrés) ou Radial (la transition se fait autour d'un point, les couleurs se fondent progressivement du centre aux bords en formant un cercle). Direction affiche la couleur de dégradé sélectionnée, cliquez sur la flèche pour définir la direction du dégradé. Si vous avez sélectionné le style Linéaire, vous pouvez choisir une des directions suivantes : du haut à gauche vers le bas à droite, du haut en bas, du haut à droite vers le bas à gauche, de droite à gauche, du bas à droite vers le haut à gauche, du bas en haut, du bas à gauche vers le haut à droite, de gauche à droite. Si vous avez choisi le style Radial, il n'est disponible qu'un seul modèle. Angle - spécifiez l'angle selon lequel les couleurs se fondent. Point de dégradé est le point d'arrêt où une couleur se fond dans une autre. Utilisez le bouton Ajouter un point de dégradé ou le curseur de dégradé pour ajouter un point de dégradé et le bouton Supprimer le point de dégradé</b> pour le supprimer. Vous pouvez ajouter 10 points de dégradé. Le nouveau arrêt de couleur n'affecte pas l'aspect actuel du dégradé. Faites glisser le curseur de déragé pour changer l'emplacement des points de dégradé ou spécifiez la Position en pourcentage pour l'emplacement plus précis. Pour choisir la couleur au dégradé, cliquez sur l'arrêt concerné sur le curseur de dégradé, ensuite cliquez sur Couleur pour sélectionner la couleur appropriée. Image ou Texture - sélectionnez cette option pour utiliser une image ou une texture prédéfinie en tant que l'arrière-plan de la forme / diapositive. Si vous souhaitez utiliser une image en tant que l'arrière-plan de la forme / diapositive, cliquez sur le bouton Sélectionner l'image et ajoutez une image D'un fichier en la sélectionnant sur le disque dur de votre ordinateur ou D'une URL en insérant l'adresse URL appropriée dans la fenêtre ouverte, ou À partir de l'espace de stockage en la sélectionnant sur votre portail. Si vous souhaitez utiliser une texture en tant que l'arrière-plan de la forme / diapositive, utilisez le menu déroulant D'une texture et sélectionnez le préréglage de la texture nécessaire. Actuellement, les textures suivantes sont disponibles : Toile, Carton, Tissu foncé, Grain, Granit, Papier gris, Tricot, Cuir, Papier brun, Papyrus, Bois. Si l'Image sélectionnée est plus grande ou plus petite que la forme automatique ou diapositive, vous pouvez profiter d'une des options Étirement ou Mosaïque depuis la liste déroulante. L'option Étirement permet de régler la taille de l'image pour l'adapter à la taille de la diapositive ou de la forme automatique afin qu'elle puisse remplir tout l'espace uniformément. L'option Mosaïque permet d'afficher seulement une partie de l'image plus grande en gardant ses dimensions d'origine, ou de répéter l'image plus petite en conservant ses dimensions initiales sur la surface de la forme automatique ou de la diapositive afin qu'elle puisse remplir tout l'espace uniformément. Remarque : tout préréglage Texture sélectionné remplit l'espace de façon uniforme, mais vous pouvez toujours appliquer l'effet Étirement, si nécessaire. Modèle - sélectionnez cette option pour sélectionner le modèle à deux couleurs composé des éléments répétés. Modèle - sélectionnez un des modèles prédéfinis du menu. Couleur de premier plan - cliquez sur cette palette de couleurs pour changer la couleur des éléments du modèle. Couleur d'arrière-plan - cliquez sur cette palette de couleurs pour changer de l'arrière-plan du modèle. Pas de remplissage - sélectionnez cette option si vous ne voulez pas utiliser un remplissage."
    },
   {
        "id": "UsageInstructions/HighlightedCode.htm", 
        "title": "Insérer le code en surbrillance", 
        "body": "Dans l'Éditeur de Présentations, vous pouvez intégrer votre code mis en surbrillance auquel le style est déjà appliqué à correspondre avec le langage de programmation et le style de coloration dans le programme choisi. Accédez à votre présentation et placez le curseur à l'endroit où le code doit être inséré. Passez à l'onglet Modules complémentaires et choisissez Code en surbrillance. Spécifiez la Langue de programmation. Choisissez le Style du code pour qu'il apparaisse de manière à sembler celui dans le programme. Spécifiez si on va remplacer les tabulations par des espaces. Choisissez la Couleur de fond. Pour le faire manuellement, déplacez le curseur sur la palette de couleurs ou passez la valeur de type RBG/HSL/HEX. Cliquez sur OK pour insérer le code."
    },
   {
        "id": "UsageInstructions/InsertAutoshapes.htm", 
        "title": "Insérer et mettre en forme des formes automatiques", 
        "body": "Insérer une forme automatique Pour ajouter une forme automatique à une diapositive dans l'Éditeur de Présentations, sélectionnez la diapositive à laquelle vous voulez ajouter une forme automatique dans la liste des diapositives à gauche. cliquez sur l'icône Forme dans l'onglet Accueil ou sur la flèche déroulante de la Galerie de formes dans l'onglet Insertion de la barre d'outils supérieure, sélectionnez l'un des groupes des formes automatiques disponibles dans la Galerie des formes : Récemment utilisé, Formes de base, Flèches figurées, Maths, Graphiques, Étoiles et rubans, Légendes, Boutons, Rectangles, Lignes, cliquez sur la forme automatique voulue du groupe sélectionné, dans la zone d'édition de la diapositive, placez le curseur de la souris là où vous voulez insérer la forme, Remarque : vous pouvez cliquer et faire glisser pour étirer la forme. après avoir ajouté la forme automatique vous pouvez modifier sa taille, sa position et ses propriétés. Remarque : pour ajouter une légende à la forme, assurez-vous que la forme est sélectionnée et commencez à taper le texte. Le texte que vous ajoutez fait partie de la forme (ainsi si vous déplacez ou faites pivoter la forme, le texte change de position lui aussi). Il est possible d'ajouter une forme automatique à la disposition d'une diapositive. Pour en sqvoir plus, veuillez consulter cet article. Modifier les paramètres de la forme automatique Certains paramètres de la forme automatique peuvent être modifiés en utilisant l'onglet Paramètres de la forme de la barre latérale droite. Pour l'activer, sélectionnez la forme ajoutée avec la souris et sélectionnez l'icône Paramètres de la forme à droite. Vous pouvez y modifier les paramètres suivants : Remplissage - utilisez cette section pour sélectionner le remplissage de la forme automatique. Les options disponibles sont les suivantes : Couleur de remplissage - sélectionnez cette option pour spécifier la couleur unie à appliquer aux diapositives sélectionnées. Remplissage en dégradé - sélectionnez cette option pour spécifier deux couleurs et remplir la forme avec une transition douce entre elles. Image ou texture - sélectionnez cette option pour utiliser une image ou une texture prédéfinie en tant que arrière-plan de la forme. Modèle - sélectionnez cette option pour choisir un modèle à deux couleurs composé d'éléments répétés. Pas de remplissage - sélectionnez cette option si vous ne voulez pas utiliser un remplissage. Pour en savoir plus consultez le chapitre Remplir les objets et sélectionner les couleurs. Ligne - utilisez cette section pour changer la largeur et la couleur du ligne de la forme automatique. Pour modifier la largeur du contour, sélectionnez une des options disponibles depuis la liste déroulante Taille. Les options disponibles sont les suivantes : 0,5 pt, 1 pt, 1,5 pt, 2,25 pt, 3 pt, 4,5 pt, 6 pt ou Pas de ligne si vous ne voulez pas de contour. Pour changer la couleur du contour, cliquez sur la case colorée et sélectionnez la couleur voulue. Vous pouvez utiliser la couleur de thème sélectionnée, une couleur standard ou choisir une couleur personnalisée. Pour modifier le type de contour, sélectionnez l'option voulue dans la liste déroulante correspondante (une ligne continue est appliquée par défaut, vous pouvez la remplacer par l'une des lignes pointillées disponibles). Rotation permet de faire pivoter la forme de 90 degrés dans le sens des aiguilles d'une montre ou dans le sens inverse des aiguilles d'une montre, ainsi que de retourner la forme horizontalement ou verticalement. Cliquez sur l'un des boutons : pour faire pivoter la forme de 90 degrés dans le sens inverse des aiguilles d'une montre pour faire pivoter la forme de 90 degrés dans le sens des aiguilles d'une montre pour retourner la forme horizontalement (de gauche à droite) pour retourner la forme verticalement (à l'envers) Modifier la forme - utilisez cette section pour remplacer la forme automatique insérée par une autre sélectionnée de la liste déroulante. Ajouter une ombre - cochez cette case pour affichage de la forme ombré. Pour changer les paramètres avancés de la forme automatique, cliquez sur la forme avec le bouton droit et sélectionnez l'option Paramètres avancés dans le menu contextuel ou cliquez avec le bouton gauche et utilisez le lien Afficher paramètres avancés sur la barre latérale droite. La fenêtre Propriétés de la forme s'ouvre : L'onglet Emplacement vous permet de modifier la Largeur et/ou Hauteur de la forme automatique. Si le bouton Proportions constantes est activé (auquel cas il ressemble à ceci ), la largeur et la hauteur seront changées en même temps et le ratio d'aspect de la forme automatique originale sera préservé. Vous pouvez également saisir la position exacte en utilisant les champs Horizontalement et Verticalement ainsi que le champ De dans lesquels vous pouvez accéder aux paramètres tels que Coin supérieur gauche et Au centre. L'onglet Rotation comporte les paramètres suivants : Angle - utilisez cette option pour faire pivoter la forme d'un angle exactement spécifié. Entrez la valeur souhaitée mesurée en degrés dans le champ ou réglez-la à l'aide des flèches situées à droite. Retourné - cochez la case Horizontalement pour retourner la forme horizontalement (de gauche à droite) ou la case Verticalement pour retourner la forme verticalement (à l'envers). L'onglet Poids et flèches contient les paramètres suivants : Style de ligne - ce groupe d'options vous permet de spécifier les paramètres suivants : Type de litterine - cette option permet de définir le style de la fin de la ligne, ainsi elle peut être appliquée seulement aux formes avec un contour ouvert telles que des lignes, des polylignes etc. : Plat - les points finaux seront plats. Arrondi - les points finaux seront arrondis. Carré - les points finaux seront carrés. Type de jointure - cette option permet de définir le style de l'intersection de deux lignes, par exemple, une polyligne, les coins du triangle ou le contour du rectangle : Arrondi - le coin sera arrondi. Plaque - le coin sera coupé d'une manière angulaire. Onglet - l'angle sera aiguisé. Bien adapté pour les formes à angles vifs. Remarque : l'effet sera plus visible si vous utilisez un contour plus épais. Flèches - ce groupe d'options est disponible pour les formes du groupe Lignes. Il permet de définir le Style de début et Style de fin aussi bien que la Taille des flèches en sélectionnant l'option appropriée des listes déroulantes. L'onglet Zone de texte contient les paramètres suivants : Ajuster automatiquement - pour modifier la façon dont le texte est affiché à l'intérieur de la forme : Ne pas ajuster automatiquement, Rétrécir le texte dans la zone de débordement uniquement, Redimensionner la forme pour ajuster le texte. L'onglet Marges intérieures - afin de changer les marges internes En haut, En bas, A gauche et A droite de la forme automatique (c'est-à-dire la distance entre le texte à l'intérieur de la forme et les bordures de la forme automatique). Remarque : cet onglet n'est disponible que si tu texte est ajouté dans la forme automatique, sinon l'onglet est désactivé. L'onglet Colonnes permet d'ajouter des colonnes de texte dans la forme automatique en spécifiant le Nombre de colonnes nécessaires (jusqu'à 16) et l'Espacement entre les colonnes. Une fois que vous avez cliqué sur OK, le texte qui existe déjà ou tout autre texte que vous entrez dans la forme automatique apparaîtra dans les colonnes et circulera d'une colonne à l'autre. L'onglet Texte de remplacement permet de spécifier un Titre et une Description qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information du tableau. Pour remplacer la forme automatique, cliquez dessus avec le bouton gauche de la souris et utilisez la liste déroulante Modifier la forme automatique dans l'onglet Paramètres de forme de la barre latérale droite. Pour supprimer la forme automatique ajoutée, cliquez avec le bouton gauche de la souris et appuyez sur la touche Supprimer. Pour apprendre à aligner une forme automatique sur la diapositive ou à organiser plusieurs formes, reportez-vous à la section Aligner et organiser les objets dans une diapositive. Joindre des formes automatiques à l'aide de connecteurs Vous pouvez connecter des formes automatiques à l'aide de lignes munies de points de connexion pour démontrer les dépendances entre les objets (par exemple, si vous souhaitez créer un diagramme). Pour le faire, cliquez sur l'icône Forme dans l'onglet Accueil ou Insertion de la barre d'outils supérieure, sélectionnez le groupe Lignes dans le menu, cliquez sur la forme souhaitée dans le groupe sélectionné (à l'exception des trois dernières formes qui ne sont pas des connecteurs, à savoir les formes Courbe, Dessin à main levée et Forme libre), passez le curseur de la souris sur la première forme automatique et cliquez sur l'un des points de connexions apparaissant sur le contour, faites glisser le curseur de la souris vers la deuxième forme automatique et cliquez sur le point de connexion voulu sur son contour. Si vous déplacez les formes automatiques jointes, le connecteur reste attaché aux formes et se déplace avec elles. Vous pouvez également détacher le connecteur des formes, puis l'attacher à d'autres points de connexion."
    },
   {
        "id": "UsageInstructions/InsertCharts.htm", 
        "title": "Insérer et modifier des graphiques", 
        "body": "Insérer un graphique Pour insérer un graphique dans Presentation Editor, Placez le curseur à l'endroit où vous voulez insérer un graphique, Passez à l'onglet Insertion de la barre d'outils supérieure. Cliquez sur l'icône Graphique de la barre d'outils supérieure. Sélectionnez le type de graphique approprié: Graphique à colonnes Histogramme groupé Histogramme empilé Histogramme empilé 100% Histogramme groupé en 3D Histogramme empilé en 3D Histogramme empilé 100 % en 3D Histogrammes en 3D Graphiques en ligne Ligne Lignes empilées Lignes empilées 100% Lignes avec marques de données Lignes empilées avec marques de données Lignes empilées 100 % avec des marques de données Lignes 3D Graphiques en secteurs Secteurs Donut Camembert 3D Graphiques à barres Barres groupées Barres empilées Barres empilées 100 % Barres groupées en 3D Barres empilées en 3D Barres empilées 100 % en 3D Graphiques en aires Aires Aires empilées Aires empilées 100% Graphiques boursiers Nuage de points (XY) Disperser Barres empilées Disperser avec lignes lissées et marqueurs Disperser avec lignes lissées Disperser avec des lignes droites et marqueurs Disperser avec des lignes droites Graphiques Combo Histogramme groupé - lignes Histogramme groupé - ligne sur un axe secondaire Aires empilées - histogramme groupé Combinaison personnalisée Remarque: ONLYOFFICE Spreadsheet Editor prend en charge la modification des graphiques créés dans d'autres applications tels que: Graphiques en pyramides, à barres (pyramides), horizontal/vertical à cylindre, horizontal/vertical à cônes. Vous pouvez ouvrir le fichier comportant tels graphiques et les modifier en utilisant les outils disponibles. Lorsque la fenêtre Éditeur du graphique s'affiche, vous pouvez saisir les données en utilisant des boutons suivants: et pour copier et coller des données et pour annuler et rétablir une action pour insérer une fonction et pour réduire et ajouter une décimale modifier le format de nombre, c'est à dire l'apparence d'un nombre saisi pour modifier le type de graphique. Cliquez sur le bouton Sélection de données dans la fenêtre Éditeur du graphique. La fenêtre Données du graphique s'affiche. Utiliser la boîte de dialogue Données du graphique pour gérer la Plage de données du graphique, la Série de la légende, le Nom de l'axe horizontal, et Changer de ligne ou de colonne. Plage de données du graphique - sélectionnez les données pour votre graphique. Cliquez sur l'icône à droite de la boîte Plage de données du graphique pour sélectionner la plage de données. Série de la légende - ajouter, modifier ou supprimer les entrées de légende. Tapez ou sélectionnez le nom de série des entrées de légende. Dans la Série de la légende, cliquez sur le bouton Ajouter. Dans Modifier la série, saisissez une nouvelle entrée de légende ou cliquez sur à droite de la zone Sélectionner un nom. Nom de l'axe horizontal - modifier le texte de l'étiquette de l'axe Dans la fenêtre Nom de l'axe horizontal cliquez sur Modifier. Dans Plage de données de l'étiquette de l'axe, saisissez les étiquettes à ajouter ou cliquez sur l'icône à droite de la zone Plage de données de l'étiquette de l'axe pour sélectionner la plage de données. Changer de ligne ou de colonne - modifier le façon de traçage des données dans la feuille de calcul. Changer de ligne ou de colonne pour afficher des données sur un autre axe. Cliquez sur OK pour appliquer toutes les modifications et fermer la fenêtre. Cliquez sur le bouton Modifier le type de graphique dans la fenêtre Éditeur du graphique pour choisir le type et le style du graphique. Sélectionnez le graphique approprié dans des sections disponibles: Colonne, Graphique en ligne, Graphique à secteurs, En barres, En aires, Nuages de points (XY), Boursier. Lorsque vous choisissez Graphiques Combo, la fenêtre Type de graphique représente les séries du graphiques et permet de choisir les types de graphiques à combiner et de sélectionner la série de données à placer sur l'axe secondaire. Paramétrer le graphique en cliquant sur Modifier le graphique dans la fenêtre Éditeur du graphique. La fenêtre Graphique - Paramètres avancés s'affiche. L'onglet Disposition vous permet de modifier la disposition des éléments de graphique. Spécifiez la position du Titre du graphique sur votre graphique en sélectionnant l'option voulue dans la liste déroulante: Rien pour ne pas afficher le titre du graphique, Superposition pour superposer et centrer le titre sur la zone de tracé, Sans superposition pour afficher le titre au-dessus de la zone de tracé. Spécifiez la position de la Légende sur votre graphique en sélectionnant l'option voulue dans la liste déroulante: Rien pour ne pas afficher de légende, En bas pour afficher la légende et l'aligner au bas de la zone de tracé, En haut pour afficher la légende et l'aligner en haut de la zone de tracé, À droite pour afficher la légende et l'aligner à droite de la zone de tracé, À gauche pour afficher la légende et l'aligner à gauche de la zone de tracé, Superposition à gauche pour superposer et centrer la légende à gauche de la zone de tracé, Superposition à droite pour superposer et centrer la légende à droite de la zone de tracé. Spécifiez les paramètres des Étiquettes de données (c'est-à-dire les étiquettes de texte représentant les valeurs exactes des points de données): Définissez la position des Étiquettes de données par rapport aux points de données en sélectionnant l'option nécessaire dans la liste déroulante. Les options disponibles varient en fonction du type de graphique sélectionné. Pour les graphiques en Colonnes/Barres, vous pouvez choisir les options suivantes: Rien, Au centre, En haut à l'intérieur, En haut à l'intérieur, En haut à l'extérieur. Pour les graphiques en Ligne/ Nuage de points (XY)/Boursier, vous pouvez choisir les options suivantes: Aucun, Centre, À gauche, À droite, En haut. Pour les graphiques Secteur, vous pouvez choisir les options suivantes: Rien, Au centre</b>, Ajuster à la largeur, En haut à l'intérieur, En haut à l'extérieur. Pour les graphiques en Aire ainsi que pour les graphiques 3D en Colonnes, Ligne, Barres et Combo vous pouvez choisir les options suivantes: Rien, Au centre. Sélectionnez les données que vous souhaitez inclure dans vos étiquettes en cochant les cases correspondantes: Nom de la série, Nom de la catégorie, Valeur, Saisissez un caractère (virgule, point-virgule, etc.) que vous souhaitez utiliser pour séparer plusieurs étiquettes dans le champ de saisie Séparateur des étiquettes de données. Lignes - permet de choisir un style de ligne pour les graphiques en Ligne/Nuage de points (XY). Vous pouvez choisir parmi les options suivantes: Droit pour utiliser des lignes droites entre les points de données, Lisse pour utiliser des courbes lisses entre les points de données, ou Rien pour ne pas afficher les lignes. Marqueurs - est utilisé pour spécifier si les marqueurs doivent être affichés (si la case est cochée) ou non (si la case n'est pas cochée) pour les graphiques Ligne/Nuage de points (XY). Remarque: les options Lignes et Marqueurs sont disponibles uniquement pour les graphiques en Ligne et Ligne/Nuage de points (XY). L'onglet Axe vertical vous permet de modifier les paramètres de l'axe vertical, également appelés axe des valeurs ou axe y, qui affiche des valeurs numériques. Notez que l'axe vertical sera l'axe des catégories qui affiche des étiquettes de texte pour les Graphiques à barres. Dans ce cas, les options de l'onglet Axe vertical correspondront à celles décrites dans la section suivante. Pour les Graphiques Nuage de points (XY), les deux axes sont des axes de valeur. Remarque: les sections Paramètres des axes et Quadrillage seront désactivées pour les Graphiques à secteurs, car les graphiques de ce type n'ont ni axes ni lignes de quadrillage. Sélectionnez Masquer pour masquer l'axe vertical du graphique, laissez cette option décochée pour afficher l'axe. Définissez l'orientation du Titre en choisissant l'option appropriée de la liste déroulante: Rien pour ne pas afficher le titre de l'axe vertical Incliné pour afficher le titre de bas en haut à gauche de l'axe vertical, Horizontal pour afficher le titre horizontalement à gauche de l'axe vertical. Valeur minimale</b> sert à définir la valeur la plus basse à afficher au début de l'axe vertical. L'option Auto est sélectionnée par défaut, dans ce cas la valeur minimale est calculée automatiquement en fonction de la plage de données sélectionnée. Vous pouvez sélectionner l'option Fixé dans la liste déroulante et spécifier une valeur différente dans le champ de saisie sur la droite. Valeur maximale sert à définir la valeur la plus élevée à afficher à la fin de l'axe vertical. L'option Auto est sélectionnée par défaut, dans ce cas la valeur maximale est calculée automatiquement en fonction de la plage de données sélectionnée. Vous pouvez sélectionner l'option Fixé dans la liste déroulante et spécifier une valeur différente dans le champ de saisie sur la droite. Croisement de l'axe - est utilisé pour spécifier un point sur l'axe vertical où l'axe horizontal doit le traverser. L'option Auto est sélectionnée par défaut, dans ce cas la valeur du point d'intersection est calculée automatiquement en fonction de la plage de données sélectionnée. Vous pouvez sélectionner l'option Valeur dans la liste déroulante et spécifier une valeur différente dans le champ de saisie à droite, ou définir le point d'intersection des axes à la Valeur minimum/maximum sur l'axe vertical. Unités d'affichage - est utilisé pour déterminer la représentation des valeurs numériques le long de l'axe vertical. Cette option peut être utile si vous travaillez avec de grands nombres et souhaitez que les valeurs sur l'axe soient affichées de manière plus compacte et plus lisible (par exemple, vous pouvez représenter 50 000 comme 50 en utilisant les unités d'affichage de Milliers). Sélectionnez les unités souhaitées dans la liste déroulante: Centaines, Milliers, 10 000, 100 000, Millions, 10 000 000, 100 000 000, Milliards, Billions, ou choisissez l'option Rien pour retourner aux unités par défaut. Valeurs dans l'ordre inverse - est utilisé pour afficher les valeurs dans la direction opposée. Lorsque la case n'est pas cochée, la valeur la plus basse est en bas et la valeur la plus haute est en haut de l'axe. Lorsque la case est cochée, les valeurs sont triées de haut en bas. La section Options de graduations permet d'ajuster l'apparence des graduations sur l'échelle verticale. Les graduations du type principal sont les divisions à plus grande échelle qui peuvent avoir des étiquettes affichant des valeurs numériques. Les graduations du type secondaire sont les subdivisions d'échelle qui sont placées entre les graduations principales et n'ont pas d'étiquettes. Les graduations définissent également l'endroit où le quadrillage peut être affiché, si l'option correspondante est définie dans l'onglet Disposition. Les listes déroulantes Type principal/secondaire contiennent les options de placement suivantes: Rien pour ne pas afficher les graduations principales/secondaires, Sur l'axe pour afficher les graduations principales/secondaires des deux côtés de l'axe, Dans pour afficher les graduations principales/secondaires dans l'axe, A l'extérieur pour afficher les graduations principales/secondaires à l'extérieur de l'axe. La section Options d'étiquettes permet d'ajuster l'apparence des étiquettes de graduations du type principal qui affichent des valeurs. Pour spécifier la Position de l'étiquette par rapport à l'axe vertical, sélectionnez l'option voulue dans la liste déroulante: Rien pour ne pas afficher les étiquettes de graduations, En bas pour afficher les étiquettes de graduations à gauche de la zone de tracé, En haut pour afficher les étiquettes de graduations à droite de la zone de tracé, À côté de l'axe pour afficher les étiquettes de graduations à côté de l'axe. Pour définir le Format d'étiquette cliquez sur le bouton format d'étiquette et choisissez la catégorie appropriée. Les catégories du format d'étiquette disponibles: Général Nombre Scientifique Comptabilité Monétaire Date Heure Pourcentage Fraction Texte Personnalisé Les options du format d'étiquette varient en fonction de la catégorie sélectionné. Pour en savoir plus sur la modification du format de nombre, veuillez consulter cette page. Activez Lié à la source pour conserver la représentation de nombre de la source de données du graphique. Remarque: Les axes secondaires sont disponibles sur les graphiques Combo uniquement. Axes secondaires sont utiles pour des graphiques Combo lorsque les nombres varient considérablement, ou lorsque des types de données mixtes sont utilisés pour créer un graphique. Avec des axes secondaires on peut lire et comprendre un graphique combiné plus facilement. L'onglet Second axe vertical/horizontal s'affiche lorsque vous choisissez une série de données appropriée pour votre graphique combiné. Les options et les paramètres disponibles sous l'onglet Second axe vertical/horizontal sont les mêmes que ceux sous l'onglet Axe vertical/horizontal. Pour une description détaillée des options disponibles sous l'onglet Axe vertical/horizontal, veuillez consulter les sections appropriées ci-dessus/ci-dessous. L'onglet Axe horizontal vous permet de modifier les paramètres de l'axe horizontal, également appelés axe des catégories ou axe x, qui affiche des étiquettes textuels. Notez que l'axe horizontal sera l'axe des valeurs qui affiche des valeurs numériques pour les Graphiques à barres. Dans ce cas, les options de l'onglet Axe horizontal correspondent à celles décrites dans la section précédente. Pour les Graphiques Nuage de points (XY), les deux axes sont des axes de valeur. Sélectionnez Masquer pour masquer l'axe horizontal du graphique, laissez cette option décochée pour afficher l'axe. Définissez l'orientation du Titre en choisissant l'option appropriée de la liste déroulante: Rien pour ne pas afficher le titre de l'axe horizontal. Sans superposition pour afficher le titre en-dessous de l'axe horizontal. Quadrillage permet de spécifier les lignes du Quadrillage horizontal que vous souhaitez afficher en sélectionnant l'option voulue dans la liste déroulante: Rien, Principaux, Secondaires ou Principaux et secondaires. Croisement de l'axe - est utilisé pour spécifier un point sur l'axe vertical où l'axe horizontal doit le traverser. L'option Auto est sélectionnée par défaut, dans ce cas la valeur du point d'intersection est calculée automatiquement en fonction de la plage de données sélectionnée. Vous pouvez sélectionner l'option Valeur dans la liste déroulante et spécifier une valeur différente dans le champ de saisie à droite, ou définir le point d'intersection des axes à la Valeur minimum/maximum (correspondant à la première et la dernière catégorie) sur l'axe vertical. Position de l'étiquette - est utilisé pour spécifier où les étiquettes de l'axe doivent être placés: Graduation ou Entre graduations. Valeurs dans l'ordre inverse - est utilisé pour afficher les catégories dans la direction opposée. Lorsque la case est désactivée, les valeurs sont affichées de gauche à droite. Lorsque la case est activée, les valeurs sont affichées de droite à gauche. La section Options de graduations permet d'ajuster l'apparence des graduations sur l'échelle horizontale. Les graduations principales sont les divisions à plus grande échelle qui peuvent avoir des étiquettes affichant des valeurs de catégorie. Les graduations secondaires sont les divisions à moins grande d'échelle qui sont placées entre les graduations principales et n'ont pas d'étiquettes. Les graduations définissent également l'endroit où le quadrillage peut être affiché, si l'option correspondante est définie dans l'onglet Disposition. Vous pouvez ajuster les paramètres de graduation suivants: Type principal/secondaire est utilisé pour spécifier les options de placement suivantes: Rien pour ne pas afficher les graduations principales/secondaires, Sur l'axe pour afficher les graduations principales/secondaires des deux côtés de l'axe, Dans pour afficher les graduations principales/secondaires dans l'axe, A l'extérieur pour afficher les graduations principales/secondaires à l'extérieur de l'axe. Intervalle entre les marques - est utilisé pour spécifier le nombre de catégories à afficher entre deux marques de graduation adjacentes. La section Options d'étiquettes permet d'ajuster l'apparence des étiquettes qui affichent des catégories. Position de l'étiquette est utilisé pour spécifier où les étiquettes de l'axe doivent être placés par rapport à l'axe horizontal: Sélectionnez l'option souhaitée dans la liste déroulante: Rien pour ne pas afficher les étiquettes de catégorie, En bas pour afficher les étiquettes de catégorie au bas de la zone de tracé, En haut pour afficher les étiquettes de catégorie en haut de la zone de tracé, À côté de l'axe pour afficher les étiquettes de catégorie à côté de l'axe. Distance de l'étiquette de l'axe - est utilisé pour spécifier la distance entre les étiquettes et l'axe. Spécifiez la valeur nécessaire dans le champ situé à droite. Plus la valeur que vous définissez est élevée, plus la distance entre l'axe et les étiquettes est grande. Intervalle entre les étiquettes - est utilisé pour spécifier la fréquence à laquelle les étiquettes doivent être affichés. L'option Auto est sélectionnée par défaut, dans ce cas les étiquettes sont affichés pour chaque catégorie. Vous pouvez sélectionner l'option Manuel dans la liste déroulante et spécifier la valeur voulue dans le champ de saisie sur la droite. Par exemple, entrez 2 pour afficher les étiquettes pour une catégorie sur deux. Pour définir le Format d'étiquette cliquez sur le bouton format d'étiquette et choisissez la catégorie appropriée. Les catégories du format d'étiquette disponibles: Général Nombre Scientifique Comptabilité Monétaire Date Heure Pourcentage Fraction Texte Personnalisé Les options du format d'étiquette varient en fonction de la catégorie sélectionné. Pour en savoir plus sur la modification du format de nombre, veuillez consulter cette page. Activez Lié à la source pour conserver la représentation de nombre de la source de données du graphique. L'onglet Alignement dans une cellule comprend les options suivantes: Déplacer et dimensionner avec des cellules - cette option permet de placer le graphique derrière la cellule. Quand une cellule se déplace (par exemple: insertion ou suppression des lignes/colonnes), le graphique se déplace aussi. Quand vous ajustez la largeur ou la hauteur de la cellule, la dimension du graphique s'ajuste aussi. Déplacer sans dimensionner avec les cellules - cette option permet de placer le graphique derrière la cellule mais d'empêcher son redimensionnement. Quand une cellule se déplace, le graphique se déplace aussi, mais si vous redimensionnez la cellule, le graphique demeure inchangé. Ne pas déplacer et dimensionner avec les cellules - cette option empêche le déplacement ou redimensionnement du graphique si la position ou la dimension de la cellule restent inchangées. L'onglet Texte de remplacement permet de spécifier un Titre et une Description qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information du graphique. Une fois le graphique ajouté, on peut également modifier sa taille et sa position. Vous pouvez définir la position du graphique sur la diapositive en le faisant glisser à l'horizontale/verticale. On peut ajouter un graphique dans l'espace réservé en cliquant sur l'icône Graphique à l'intérieur de l'espace et en sélectionnant le type du graphique approprié: On peut aussi ajouter un graphique à la disposition de diapositive. Pour en savoir plus, veuillez consulter cet article. Modifier les éléments de graphique Pour modifier le Titre du graphique, sélectionnez le texte par défaut à l'aide de la souris et saisissez le vôtre à la place. Pour modifier la mise en forme de la police dans les éléments de texte, tels que le titre du graphique, les titres des axes, les entrées de légende, les étiquettes de données, etc., sélectionnez l'élément de texte nécessaire en cliquant dessus. Utilisez ensuite les icônes de l'onglet Accueil de la barre d'outils supérieure pour modifier le type de police, la taille, la couleur. Une fois le graphique sélectionné, l'icône Paramètres de la forme est aussi disponible à la droite car une forme est utilisé en arrière plan du graphique. Vous pouvez appuyer sur cette icône pour accéder l'onglet Paramètres de la forme dans la barre latérale droite et configurer le Remplissage, le Trait et le Style d'habillage de la forme. Veuillez noter qu'on ne peut pas modifier le type de la forme. Sous l'onglet Paramètres de la forme dans le panneau droit, vous pouvez configurer la zone du graphique là-même aussi que les éléments du graphique tels que la zone de tracé, la série de données, le titre du graphique, la légende et les autres et ajouter les différents types de remplissage. Sélectionnez l'élément du graphique nécessaire en cliquant sur le bouton gauche de la souris et choisissez le type de remplissage approprié: couleur de remplissage, remplissage en dégradé, image ou texture, modèle. Configurez les paramètres du remplissage et spécifier le niveau d'opacité si nécessaire. Lorsque vous sélectionnez l'axe vertical ou horizontal ou le quadrillage, vous pouvez configurer le paramètres du trait seulement sous l'onglet Paramètres de la forme: couleur, taille et type. Pour plus de détails sur utilisation des couleurs de la forme, du remplissage et du trait veuillez accéder à cet page. Remarque: l'option Ajouter un ombre est aussi disponible sous l'onglet Paramètres de la forme, mais elle est désactivée pour les éléments du graphique. Si vous voulez redimensionner les éléments du graphique, sélectionnez l'élément nécessaire en cliquant sur le bouton gauche de la souris et faites glisser un des huit carreaux blancs le long du périmètre de l'élément. Pour modifier la position d'un élément, cliquez sur cet élément avec le bouton gauche de souris, vérifiez si le curseur est devenu , maintenez le bouton gauche de la souris enfoncé et faites-le glisser vers la position souhaité. Pour supprimer un élément de graphique, sélectionnez-le en cliquant sur le bouton gauche et appuyez sur la touche Suppr. Vous pouvez également faire pivoter les graphiques 3D à l'aide de la souris. Faites un clic gauche dans la zone de tracé et maintenez le bouton de la souris enfoncé. Faites glisser le curseur sans relâcher le bouton de la souris pour modifier l'orientation du graphique 3D. Ajuster les paramètres du graphique Modifiez la taille, le type et le style du graphique aussi que les données utilisés pour créer un graphique sur la barre latérale droite. Pour l'activer, cliquez sur le graphique et sélectionne l'icône Paramètres du graphique à droite. La section Taille vous permet de modifier la largeur et/ou la hauteur du graphique. Lorsque le bouton Proportions constantes est activé (dans ce cas, il ressemble à ceci ), la largeur et la hauteur seront changées en même temps, le ratio d'aspect du graphique original sera préservé. La section Modifier le type de graphique vous permet de modifier le type et/ou le style de graphique sélectionné à l'aide du menu déroulant correspondant. Pour sélectionner le Style de graphique nécessaire, utilisez le deuxième menu déroulant de la section Modifier le type de graphique. Le bouton Modifier les données vous permet d'ouvrir la fenêtre Éditeur de graphique et d'éditer les données comme décrit ci-dessus. Remarque: pour ouvrir rapidement la fenêtre Éditeur de graphiques, vous pouvez également double-cliquer sur le graphique dans le document. En outre, la configuration de Rotation 3D est disponible pour des graphiques 3D: Rotation X - définissez la valeur de rotation de l'axe X en utilisant le clavier ou les flèches Gauche et Droite. Rotation Y - définissez la valeur de rotation de l'axe Y en utilisant le clavier ou les flèches Haut et Bas. Perspective - définissez la valeur appropriée de profondeur en utilisant le clavier ou les flèches Rétrécir le champ ou Élargir le champ. Axes à angle droit sert à définir l'angle de l'axe à droite. Mise à l'échelle automatique - activez cette option pour mettre automatiquement à l'échelle la profondeur et la hauteur du graphique ou désactivez cette option pour définir manuellement la profondeur et la hauteur. Profondeur (% de la base) - définissez la profondeur en utilisant le clavier ou le flèches. Hauteur (% de la base) - définissez la hauteur en utilisant le clavier ou le flèches. Rotation par défaut - restaurer les paramètres 3D par défaut. Veuillez noter que c'est pas possible de modifier chaque élément du graphique, tous paramètres seront appliqués au graphique dans son ensemble. L'option Afficher les paramètres avancés dans la barre latérale droite permet d'ouvrir la fenêtre Graphique - Paramètres avancés dans laquelle vous pouvez configurer le paramètres suivants: L'onglet Emplacement permet de modifier les paramètres suivants: Taille utilisez cet option pour modifier la largeur et/ou la hauteur du graphique. Lorsque le bouton Proportions constantes est activé (dans ce cas, il ressemble à ceci ), la largeur et la hauteur seront changées en même temps, le ratio d'aspect du graphique original sera préservé. Position définissez la position exacte à l'aide des champs Horizontal et Vertical, aussi que le champ De pour accéder aux tels paramètres comme Coin supérieur gauche et Centre. L'onglet Texte de remplacement permet de spécifier un Titre et une Description qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information du graphique. Pour supprimer un graphique inséré, sélectionnez-le avec la souris et appuyez sur la touche Suppr. Pour apprendre à aligner un graphique sur la diapositive ou à organiser plusieurs objets, reportez-vous à la section Aligner et organiser les objets dans une diapositive ."
    },
   {
        "id": "UsageInstructions/InsertEquation.htm", 
        "title": "Insérer des équations", 
        "body": "Presentation Editor vous permet de créer des équations à l'aide des modèles intégrés, de les modifier, d'insérer des caractères spéciaux (à savoir des opérateurs mathématiques, des lettres grecques, des accents, etc.). Ajouter une nouvelle équation Pour insérer une équation depuis la galerie, placez le curseur à l'endroit où vous voulez insérer une équation, passez à l'onglet Insertion de la barre d'outils supérieure, cliquez sur la flèche à côté de l'icône Équation de la barre d'outils supérieure, sélectionnez la catégorie de l'équation sur la barre d'outils supérieure au-dessus de l'équation ajoutée, ou sélectionnez la catégorie d'équation nécessaire dans la liste déroulante. Les catégories suivantes sont actuellement disponibles: Symboles, Fractions, Scripts, Radicaux, Intégrales, Grands opérateurs, Crochets, Fonctions, Accentuations, Limites et logarithmes, Opérateurs, Matrices, Cliquez sur Paramètres d'équations dans la barre d'outils au-dessus de l'équation ajoutée pour accéder à d'autres paramètres, tels que Unicode ou LaTeX, Professionnel ou Linéaire et passer à mode en ligne, cliquez sur le symbole/l'équation voulu(e) dans l'ensemble de modèles correspondant. La zone de symbole/équation sélectionnée sera inséré au centre de la diapositive actuelle. Si la bordure de la zone d'équation est invisible, cliquez n'importe où dans l'équation - la bordure sera affichée en pointillé. Vous pouvez déplacer, redimensionner ou pivoter librement la zone d'équation sur une diapositive. Pour ce faire, cliquez sur la bordure de la zone d'équation (elle sera affichée en trait plein) et utilisez les poignées correspondantes. Chaque modèle d'équation comporte un ensemble d'emplacements. Un emplacement est une position pour chaque élément qui compose l'équation. Un emplacement vide (également appelé un espace réservé) a un contour en pointillé . Vous devez remplir tous les espaces réservés en spécifiant les valeurs nécessaires. Entrer des valeurs Le point d'insertion spécifie où le prochain caractère que vous entrez apparaîtra. Pour positionner le point d'insertion avec précision, cliquez sur l'espace réservé et utilisez les flèches du clavier pour déplacer le point d'insertion d'un caractère vers la gauche/la droite. Une fois le point d'insertion positionné, vous pouvez remplir l'espace réservé: entrez la valeur numérique/littérale souhaitée à l'aide du clavier, insérez un caractère spécial de la palette des symboles du menu Équation sous l'onglet Insertion de la barre d'outils supérieure ou saisissez avec le clavier (consultez le guide AutoMaths), ajoutez un autre modèle d'équation à partir de la palette pour créer une équation imbriquée complexe. La taille de l'équation primaire sera automatiquement ajustée pour s'adapter à son contenu. La taille des éléments de l'équation imbriquée dépend de la taille de l'espace réservé de l'équation primaire, mais elle ne peut pas être inférieure à la taille de sous-indice. Pour ajouter de nouveaux éléments d'équation, vous pouvez également utiliser les options du menu contextuel: Pour ajouter un nouvel argument avant ou après celui existant dans les Crochets, vous pouvez cliquer avec le bouton droit sur l'argument existant et sélectionner l'option Insérer un argument avant/après dans le menu. Pour ajouter une nouvelle équation dans les Cas avec plusieurs conditions du groupe Crochets, vous pouvez cliquer avec le bouton droit de la souris sur un espace réservé vide ou une équation entrée et sélectionner l'option Insérer une équation avant/après dans le menu. Pour ajouter une nouvelle ligne ou une colonne dans une Matrice, vous pouvez cliquer avec le bouton droit de la souris sur un espace réservé, sélectionner l'option Insérer dans le menu, puis sélectionner Ligne au-dessus/en dessous ou Colonne à gauche/à droite. Remarque: actuellement, les équations ne peuvent pas être entrées en utilisant le format linéaire, c'est-à-dire \\sqrt(4&x^3). Lorsque vous entrez les valeurs des expressions mathématiques, vous n'avez pas besoin d'utiliser la Barre d'espace car les espaces entre les caractères et les signes des opérations sont définis automatiquement. Si l'équation est trop longue et ne tient pas en une seule ligne dans la zone d'équation, le saut de ligne automatique se produit pendant que vous tapez. Vous pouvez également insérer un saut de ligne à une position spécifique en cliquant avec le bouton droit sur un opérateur mathématique et en sélectionnant l'option Insérer un saut manueldans le menu. L'opérateur sélectionné va commencer une nouvelle ligne. Pour supprimer le saut de ligne manuel ajouté, cliquez avec le bouton droit sur l'opérateur mathématique qui commence une nouvelle ligne et sélectionnez l'option Supprimer un saut manuel. Mise en forme des équations Par défaut, l'équation dans la zone d'équation est centrée horizontalement et alignée verticalement sur le haut de la zone d'équation. Pour modifier son alignement horizontal/vertical, placez le curseur dans la zone d'équation (les bordures de la zone d'équation seront affichées en pointillés) et utilisez les icônes correspondantes sous l'onglet Accueil de la barre d'outils supérieure. Pour augmenter ou diminuer la taille de la police d'une équation, cliquez n'importe où dans la zone d'équation et sélectionnez la taille de police nécessaire dans la liste sous l'onglet Accueil de la barre d'outils supérieure. Tous les éléments d'équation changeront en conséquence. Les lettres de l'équation sont en italique par défaut. Si nécessaire, vous pouvez changer le style de police (gras, italique, barré) ou la couleur pour une équation entière ou une portion. Le style souligné peut être appliqué uniquement à l'équation entière et non aux caractères individuels. Sélectionnez la partie de l'équation voulue en cliquant et en faisant glisser. La partie sélectionnée sera surlignée en bleu. Utilisez ensuite les boutons nécessaires dans l'onglet Accueil de la barre d'outils supérieure pour formater la sélection. Par exemple, vous pouvez supprimer le format italique pour les mots ordinaires qui ne sont pas des variables ou des constantes. Pour modifier certains éléments d'équation, vous pouvez également utiliser les options du menu contextuel: Pour modifier le format des Fractions, vous pouvez cliquer sur une fraction avec le bouton droit de la souris et sélectionner l'option Changer en fraction en biais/linéaire/empilée dans le menu (les options disponibles varient en fonction du type de fraction sélectionné). Pour modifier la position des Scripts par rapport au texte, vous pouvez faire un clic droit sur l'équation contenant des scripts et sélectionner l'option Scripts avant/après le texte dans le menu. Pour modifier la taille des arguments pour Scripts, Radicaux, Intégrales, Grands opérateurs, Limites et Logarithmes, Opérateurs ainsi que pour les accolades supérieures/inférieures et les Modèles avec des caractères de regroupement du groupe Accentuations, vous pouvez cliquer avec le bouton droit sur l'argument que vous souhaitez modifier et sélectionner l'option Augmenter/Diminuer la taille de l'argument dans le menu. Pour spécifier si un espace libre vide doit être affiché ou non pour un Radical, vous pouvez cliquer avec le bouton droit de la souris sur le radical et sélectionner l'option Masquer/Afficher le degré dans le menu. Pour spécifier si un espace réservé de limite vide doit être affiché ou non pour une Intégrale ou un Grand opérateur, vous pouvez cliquer sur l'équation avec le bouton droit de la souris et sélectionner l'option Masquer/Afficher la limite supérieure/inférieure dans le menu. Pour modifier la position des limites relative au signe d'intégrale ou d'opérateur pour les Intégrales ou les Grands opérateurs, vous pouvez cliquer avec le bouton droit sur l'équation et sélectionner l'option Modifier l'emplacement des limites dans le menu. Les limites peuvent être affichées à droite du signe de l'opérateur (sous forme d'indices et d'exposants) ou directement au-dessus et au-dessous du signe de l'opérateur. Pour modifier la position des limites par rapport au texte des Limites et des Logarithmes et des modèles avec des caractères de regroupement du groupe Accentuations, vous pouvez cliquer avec le bouton droit sur l'équation et sélectionner l'option Limites sur/sous le texte dans le menu. Pour choisir lequel des Crochets doit être affiché, vous pouvez cliquer avec le bouton droit de la souris sur l'expression qui s'y trouve et sélectionner l'option Masquer/Afficher les parenthèses ouvrantes/fermantes dans le menu. Pour contrôler la taille des Crochets, vous pouvez cliquer avec le bouton droit sur l'expression qui s'y trouve. L'option Étirer les parenthèses est sélectionnée par défaut afin que les parenthèses puissent croître en fonction de l'expression qu'elles contiennent, mais vous pouvez désélectionner cette option pour empêcher l'étirement des parenthèses. Lorsque cette option est activée, vous pouvez également utiliser l'option Faire correspondre les crochets à la hauteur de l'argument. Pour modifier la position du caractère par rapport au texte des accolades ou des barres supérieures/inférieures du groupe Accentuations, vous pouvez cliquer avec le bouton droit sur le modèle et sélectionner l'option Caractère/Barre sur/sous le texte dans le menu. Pour choisir les bordures à afficher pour une Formule encadrée du groupe Accentuations, vous pouvez cliquer sur l'équation avec le bouton droit de la souris et sélectionner l'option Propriétés de bordure dans le menu, puis sélectionner Masquer/Afficher bordure supérieure/inférieure/gauche/droite ou Ajouter/Masquer ligne horizontale/verticale/diagonale. Pour spécifier si un espace réservé vide doit être affiché ou non pour une Matrice, vous pouvez cliquer avec le bouton droit de la souris sur le radical et sélectionner l'option Masquer/Afficher l'espace réservé dans le menu. Pour aligner certains éléments d'équation, vous pouvez utiliser les options du menu contextuel: Pour aligner des équations dans les Cas avec plusieurs conditions du groupe Crochets, vous pouvez cliquer avec le bouton droit de la souris sur une équation, sélectionner l'option Alignement dans le menu, puis sélectionnez le type d'alignement: Haut, Centre ou Bas Pour aligner une Matrice verticalement, vous pouvez cliquer avec le bouton droit sur la matrice, sélectionner l'option Alignement de Matrice dans le menu, puis sélectionner le type d'alignement: Haut, Centre ou Bas Pour aligner les éléments d'une colonne Matrice horizontalement, vous pouvez cliquer avec le bouton droit sur la colonne, sélectionner l'option Alignement de Colonne dans le menu, puis sélectionner le type d'alignement: Gauche, Centre ou Droite. Supprimer les éléments d'une équation Pour supprimer une partie de l'équation, sélectionnez la partie que vous souhaitez supprimer en faisant glisser la souris ou en maintenant la touche Maj enfoncée et en utilisant les boutons fléchés, puis appuyez sur la touche Suppr. Un emplacement ne peut être supprimé qu'avec le modèle auquel il appartient. Pour supprimer toute équation, cliquez sur la bordure de la zone d'équation (elle sera affichée en trait plein) et appuyez sur la touche Suppr. Pour supprimer certains éléments d'équation, vous pouvez également utiliser les options du menu contextuel: Pour supprimer un Radical, vous pouvez faire un clic droit dessus et sélectionner l'option Supprimer radical dans le menu. Pour supprimer un Indice et/ou un Exposant, vous pouvez cliquer avec le bouton droit sur l'expression qui les contient et sélectionner l'option Supprimer indice/exposant dans le menu. Si l'expression contient des scripts qui viennent avant le texte, l'option Supprimer les scripts est disponible. Pour supprimer des Crochets, vous pouvez cliquer avec le bouton droit de la souris sur l'expression qu'ils contiennent et sélectionner l'option Supprimer les caractères englobants ou Supprimer les caractères et séparateurs englobants dans le menu. Si l'expression contenue dans les Crochets comprend plus d'un argument, vous pouvez cliquer avec le bouton droit de la souris sur l'argument que vous voulez supprimer et sélectionner l'option Supprimer l'argument dans le menu. Si les Crochets contiennent plus d'une équation (c'est-à-dire des Cas avec plusieurs conditions), vous pouvez cliquer avec le bouton droit sur l'équation que vous souhaitez supprimer et sélectionner l'option Supprimer l'équation dans le menu. Pour supprimer une Limite, vous pouvez faire un clic droit dessus et sélectionner l'option Supprimer limite dans le menu. Pour supprimer une Accentuation, vous pouvez cliquer avec le bouton droit de la souris et sélectionner l'option Supprimer le caractère d'accentuation, Supprimer le caractère ou Supprimer la barre dans le menu (les options disponibles varient en fonction de l'accent sélectionné). Pour supprimer une ligne ou une colonne d'une Matrice, vous pouvez cliquer avec le bouton droit de la souris sur l'espace réservé dans la ligne/colonne à supprimer, sélectionner l'option Supprimer dans le menu, puis sélectionner Supprimer la ligne/Colonne. Convertir des équations Si votre document comporte des équations qu'on a créé avec des versions antérieures de l'éditeur d'équations (par ex. des versions de MS Office lancées avant 2007), il vous faut les convertir au format Office Math ML pour avoir la possibilité de les modifier. Pour convertir une équation, faites un clic double dessus. Un message d'avertissement va apparaître. Pour convertir uniquement la équation sélectionnée, cliquez sur Oui dans le message d'avertissement. Pour convertir toutes équations du document, activez l'option Appliquer à toutes les équations et cliquez sur Oui. Une fois l'équation convertie, vous pouvez la modifier."
    },
   {
        "id": "UsageInstructions/InsertHeadersFooters.htm", 
        "title": "Insérer les pieds de page", 
        "body": "Les pieds de page permettent d'ajouter des informations supplémentaires sur une diapositive, telles que la date et l'heure, le numéro de la diapositive ou un texte. Pour insérer un pied de page dans l'Éditeur de Présentations : Passez à l'onglet Insertion Cliquez sur le bouton Modifier le pied de page dans la barre d'outils supérieure, La fenêtre Paramètres des pieds de page s'ouvre. Cochez le type de données que vous souhaitez ajouter dans le pied de page. Les modifications sont affichées dans la fenêtre d'aperçu à droite. Cochez la case Date et heure pour insérer une date ou une heure dans un format sélectionné. La date sélectionnée sera ajoutée au champ gauche du pied de page de la diapositive. Spécifiez le format de données : Mettre à jour automatiquement - sélectionnez cette option si vous souhaitez mettre à jour automatiquement la date et l'heure en fonction de la date et de l'heure actuelles. Sélectionnez ensuite le Format de date et d'heure et la Langue dans les listes déroulantes. Corrigé - sélectionnez cette option si vous ne souhaitez pas mettre à jour automatiquement la date et l'heure. Cochez la case Numéro de diapositive pour insérer le numéro de diapositive en cours. Le numéro de la diapositive sera ajouté dans le champ droit du pied de page de la diapositive. Cochez la case Texte en pied de page pour insérer le texte. Saisissez le texte nécessaire dans le champ de saisie ci-dessous. Le texte sera ajouté dans le champ central du pied de page de la diapositive. Cochez l'option Ne pas afficher sur la diapositive titre, si nécessaire, Cliquez sur le bouton Appliquer à tous pour appliquer les modifications à toutes les diapositives ou utilisez le bouton Appliquer pour appliquer les modifications à la diapositive actuelle uniquement. Pour insérer rapidement une date ou un numéro de diapositive dans le pied de page de la diapositive sélectionnée, vous pouvez utiliser les options Afficher le numéro de diapositive et Afficher la date et l'heure dans l'onglet Paramètres de la diapositive de la barre latérale droite. Dans ce cas, les paramètres sélectionnés seront appliqués à la diapositive actuelle uniquement. La date et l'heure ou le numéro de diapositive ajoutés de cette manière peuvent être ajustés ultérieurement à l'aide de la fenêtre Paramètres de pied de page Pour modifier le pied de page ajouté, cliquez sur le bouton Modifier le pied de page dans la barre d'outils supérieure, apportez les modifications nécessaires dans la fenêtre Paramètres de pied de page, puis cliquez sur le bouton Appliquer ou Appliquer à tous pour enregistrer les modifications. Insérer la date et l'heure et le numéro de diapositive dans la zone de texte Il est également possible d'insérer la date et l'heure ou le numéro de diapositive dans la zone de texte sélectionnée à l'aide des boutons correspondants de l'onglet Insertion de la barre d'outils supérieure. Insérer la date et l'heure Placez le curseur de la souris dans la zone de texte où vous souhaitez insérer la date et l'heure, Cliquez sur le bouton Date &amp; Heure dans l'onglet Insertion de la barre d'outils supérieure, Sélectionnez la Langue nécessaire dans la liste et choisissez le Format de date et d'heure nécessaire dans la fenêtre Date &amp; Heure, Si nécessaire, cochez la case Mettre à jour automatiquement ou sélectionnez l'option Définir par défaut pour définir le format de date et d'heure sélectionné par défaut pour la langue spécifiée, Cliquez sur le bouton OK pour appliquer les modifications. La date et l'heure seront insérées à la position actuelle du curseur. Pour modifier la date et l'heure insérées, sélectionnez la date et l'heure insérées dans la zone de texte, cliquez sur le bouton Date &amp; Heure dans l'onglet Insertion de la barre d'outils supérieure, Choisissez le format nécessaire dans la fenêtre Date &amp; Heure, Cliquez sur OK. Insérer un numéro de diapositive Placez le curseur de la souris dans la zone de texte ou vous souhaitez insérer le numéro de diapositive, Cliquez sur le bouton Numéro de diapositive dans l'onglet Insertion de la barre d'outils supérieure, Cochez la case Numéro de diapositive dans la fenêtre Paramètres de pied de page, Cliquez sur le bouton OK pour appliquer les modifications. Le numéro de diapositive sera inséré à la position actuelle du curseur."
    },
   {
        "id": "UsageInstructions/InsertImages.htm", 
        "title": "Insérer et modifier des images", 
        "body": "Insérer une image Éditeur de Présentations vous permet d'insérer des images aux formats populaires dans votre présentation. Les formats d'image pris en charge sont les suivants : BMP, GIF, JPEG, JPG, PNG. Pour ajouter une image à une diapositive, sélectionnez la diapositive à laquelle vous voulez ajouter une image dans la liste des diapositives à gauche, cliquez sur l'icône Image dans l'onglet Accueil ou Insertion de la barre d'outils supérieure, sélectionnez l'une des options suivantes pour charger l'image : l'option Image à partir d'un fichier ouvre la fenêtre de dialogue standard pour sélectionner le fichier. Sélectionnez le fichier de votre choix sur le disque dur de votre ordinateur et cliquez sur le bouton Ouvrir Dans l’éditeur en ligne, vous pouvez sélectionner plusieurs images à la fois. l'option Image à partir d'une URL ouvre la fenêtre où vous pouvez saisir l'adresse Web de l'image et cliquer sur le bouton OK l'option Image de stockage ouvrira la fenêtre Sélectionner la source de données. Sélectionnez une image stockée sur votre portail et cliquez sur le bouton OK après avoir ajouté l'image, vous pouvez modifier sa taille, ses paramètres et sa position. On peut ajouter une image dans l'espace réservé en cliquant sur l'icône Image à partir d'un fichier à l'intérieur de l'espace et sélectionner l'image stockée sur votre ordinateur, ou utiliser le bouton Image à partir d'une URL et saisissez l'adresse URL de l'image : On peut aussi ajouter une image à la disposition de diapositive. Pour en savoir plus, veuillez consulter cet article. Ajuster les paramètres de l'image La barre latérale droite est activée lorsque vous cliquez avec le bouton gauche sur une image et sélectionnez l'icône Paramètres de l'image à droite. Elle comporte les sections suivantes : Taille - permet d'afficher la Largeur et la Hauteur de l'image actuelle ou de restaurer la Taille par défaut de l'image si nécessaire. Le bouton Rogner sert à recadrer l'image. Cliquez sur le bouton Rogner pour activer les poignées de recadrage qui appairaient par chaque coin et sur les côtés. Faites glisser manuellement les pognées pour définir la zone de recadrage. Vous pouvez positionner le curseur sur la bordure de la zone de recadrage lorsque il se transforme en et faites la glisser. Pour rogner un seul côté, faites glisser la poignée située au milieu de ce côté. Pour rogner simultanément deux côtés adjacents, faites glisser l'une des poignées d'angle. Pour rogner également deux côtés opposés de l'image, maintenez la touche Ctrl enfoncée lorsque vous faites glisser la poignée au milieu de l'un de ces côtés. Pour rogner également tous les côtés de l'image, maintenez la touche Ctrl enfoncée lorsque vous faites glisser l'une des poignées d'angle. Lorsque la zone de recadrage est définie, cliquez à nouveau sur le bouton Rogner, ou appuyez sur la touche Echap, ou cliquez n'importe où à l'extérieur de la zone de recadrage pour appliquer les modifications. Une fois la zone de recadrage sélectionnée, il est également possible d'utiliser les options Rogner à la forme, Remplir ou Ajuster disponibles dans le menu déroulant Rogner. Cliquez de nouveau sur le bouton Rogner et sélectionnez l'option de votre choix : Si vous sélectionnez l'option Rogner à la forme, l'image va s'ajuster à une certaine forme. Vous pouvez sélectionner la forme appropriée dans la galerie qui s'affiche lorsque vous placez le poiunteur de la soiris sur l'option Rogner à la forme. Vous pouvez toujours utiliser les options Remplir et Ajuster pour choisir le façon d'ajuster votre image à la forme. Si vous sélectionnez l'option Remplir, la partie centrale de l'image originale sera conservée et utilisée pour remplir la zone de cadrage sélectionnée, tandis que les autres parties de l'image seront supprimées. Si vous sélectionnez l'option Ajuster, l'image sera redimensionnée pour correspondre à la hauteur ou à la largeur de la zone de recadrage. Aucune partie de l'image originale ne sera supprimée, mais des espaces vides peuvent Remplacer l'image - est utilisé pour charger une autre image à la place de celle en cours en sélectionnant la source désirée. Vous pouvez choisir parmi les options suivantes : A partir du fichier, Image de stockage ou A partir de l'URL. L'option Remplacer l'image est également disponible dans le menu contextuel. Rotation permet de faire pivoter l'image de 90 degrés dans le sens des aiguilles d'une montre ou dans le sens inverse des aiguilles d'une montre, ainsi que de retourner l'image horizontalement ou verticalement. Cliquez sur l'un des boutons : pour faire pivoter l'image de 90 degrés dans le sens inverse des aiguilles d'une montre pour faire pivoter l'image de 90 degrés dans le sens des aiguilles d'une montre pour retourner l'image horizontalement (de gauche à droite) pour retourner l'image verticalement (à l'envers) Lorsque l'image est sélectionnée, l'icône Paramètres de la forme est également disponible sur la droite. Vous pouvez cliquer sur cette icône pour ouvrir l'onglet Paramètres de la forme dans la barre latérale droite et ajuster le type du Ligne la taille et la couleur de la forme ainsi que le type de forme en sélectionnant une autre forme dans le menu Modifier la forme automatique. La forme de l'image changera en conséquence. Sous l'onglet Paramètres de la forme, vous pouvez utiliser l'option Ajouter une ombre pour créer une zone ombrée autour de l'image. Pour modifier les paramètres avancés, cliquez sur l'image avec le bouton droit de la souris et sélectionnez Paramètres avancés de l'image du menu contextuel ou cliquez sur le lien de la barre latérale droite Afficher les paramètres avancés. La fenêtre paramètres de l'image s'ouvre : L'onglet Emplacement vous permet de régler les paramètres suivants : Taille - utilisez cette options pour modifier la largeur/hauteur de l'image. Lorsque le bouton Proportions constantes </b> est activé (Ajouter un ombre) ), le rapport largeur/hauteur d'origine s'ajuste proportionnellement. Pour rétablir la taille par défaut de l'image ajoutée, cliquez sur le bouton Taille actuelle. Position - saisissez la position exacte en utilisant les champs Horizontalement et Verticalement, ainsi que le champ De dans lesquels vous pouvez accéder aux paramètres tels que Coin supérieur gauche et Au centre. L'onglet Rotation comporte les paramètres suivants : Angle - utilisez cette option pour faire pivoter l'image d'un angle exactement spécifié. Entrez la valeur souhaitée mesurée en degrés dans le champ ou réglez-la à l'aide des flèches situées à droite. Retourné - cochez la case Horizontalement pour retourner l'image horizontalement (de gauche à droite) ou la case Verticalement pour retourner l'image verticalement (à l'envers). L'onglet Texte de remplacement permet de spécifier un Titre et une Description qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information de l'image. Pour supprimer une image insérée, sélectionnez-le avec la souris et appuyez sur la touche Supprimer Pour apprendre à aligner une image sur la diapositive ou à organiser plusieurs images, reportez-vous à la section Aligner et organiser les objets dans une diapositive ."
    },
   {
        "id": "UsageInstructions/InsertSmartArt.htm", 
        "title": "Insérer des graphiques SmartArt dans ONLYOFFICE Presentation Editor", 
        "body": "Insérer des graphiques SmartArt Un graphique SmartArt sert à créer une représentation visuelle de la structure hiérarchique en choisissant le type du graphique qui convient le mieux. Insérez des graphiques SmartArt ou modifiez des graphiques SmartArt qui ont été créés dans des logiciels d'éditeurs tiers. Pour ajouter un graphiques SmartArt, passez à l'onglet Insertion, cliquez sur le bouton SmartArt, placez le pointeur sur l'un des styles de mise en page disponibles, par ex. Liste ou Processus. sélectionnez le type de mise en page de la liste qui apparaît à droite de l'option du menu mis en surbrillance. Vous pouvez personnaliser la configuration SmartArt sur le panneau à droite: Remplissage - utilisez cette section pour sélectionner le remplissage du graphique SmartArt. Les options disponibles sont les suivantes: Couleur de remplissage - sélectionnez cette option pour spécifier la couleur unie à utiliser pour remplir l'espace intérieur du graphique SmartArt. Remplissage en dégradé - sélectionnez cette option pour spécifier deux couleurs pour créer une transition douce entre elles et remplir la forme. Personnaliser votre dégradé sans aucune contrainte. Cliquez sur l'icône Paramètres de la forme. pour ouvrir le menu Remplissage de la barre latérale sur la droite: Les options disponibles du menu: Style - choisissez Linéaire or Radial: Linéaire sert à remplir par un dégradé de gauche à droite, de bas en haut ou sous l'angle partant en direction définie. La fenêtre d'aperçu Direction affiche la couleur de dégradé sélectionnée, cliquez sur la flèche pour définir la direction du dégradé. Utilisez les paramètres Angle pour définir un angle précis du dégradé. Radial sert à remplir par un dégradé de forme circulaire entre le point de départ et le point d'arrivée. Point de dégradé est le point d'arrêt de d'une couleur et de la transition entre les couleurs. Utilisez le bouton Ajouter un point de dégradé ou le curseur de dégradé pour ajouter un point de dégradé. Vous pouvez ajouter 10 points de dégradé. Le nouveau arrêt de couleur n'affecte pas l'aspect actuel du dégradé. Utilisez le bouton Supprimer un point de dégradé pour supprimer un certain point de dégradé. Faites glisser le curseur de déragé pour changer l'emplacement des points de dégradé ou spécifiez la Position en pourcentage pour l'emplacement plus précis. Pour choisir la couleur au dégradé, cliquez sur l'arrêt concerné sur le curseur de dégradé, ensuite cliquez sur Couleur pour sélectionner la couleur appropriée. Image ou Texture - sélectionnez cette option pour utiliser une image ou une texture prédéfinie en tant que l'arrière-plan du graphique SmartArt. Si vous souhaitez utiliser une image en tant que l'arrière-plan du graphique SmartArt, vous pouvez ajouter une image D'un fichier en la sélectionnant sur le disque dur de votre ordinateur ou D'une URL en insérant l'adresse URL appropriée dans la fenêtre ouverte, ou À partir de l'espace de stockage en sélectionnant l'image nécessaire sur votre portail. Si vous souhaitez utiliser une texture en tant que arrière-plan du graphique SmartArt, utilisez le menu déroulant D'une texture et sélectionnez le préréglage de la texture nécessaire. Actuellement, les textures suivantes sont disponibles: Toile, Carton, Tissu foncé, Grain, Granit, Papier gris, Tricot, Cuir, Papier brun, Papyrus, Bois. Si l'image sélectionnée est plus grande ou plus petite que le graphique SmartArt, vous pouvez utiliser l'une des options Prolonger ouTuile depuis la liste déroulante. L'option Prolonger permet de régler la taille de l'image pour l'adapter à la taille de la forme automatique afin qu'elle puisse remplir tout l'espace uniformément. L'option Tuile permet d'afficher seulement une partie de l'image plus grande en gardant ses dimensions d'origine, ou de répéter l'image plus petite en conservant ses dimensions initiales sur la surface du graphique SmartArt afin qu'elle puisse remplir tout l'espace uniformément. Remarque: tout préréglage Texture sélectionné remplit l'espace de façon uniforme, mais vous pouvez toujours appliquer l'effet Prolonger, si nécessaire. Modèle - sélectionnez cette option pour sélectionner le modèle à deux couleurs composé des éléments répétés pour remplir l'espace intérieur du graphique SmartArt. Modèle - sélectionnez un des modèles prédéfinis du menu. Couleur de premier plan - cliquez sur cette palette de couleurs pour changer la couleur des éléments du modèle. Couleur d'arrière-plan - cliquez sur cette palette de couleurs pour changer de l'arrière-plan du modèle. Pas de remplissage - sélectionnez cette option si vous ne voulez pas utiliser un remplissage. Trait - sert à régler la taille, la couleur et le type du contour du graphique SmartArt. Pour modifier la largeur du trait, sélectionnez une des options disponibles depuis la liste déroulante Taille. Les options disponibles sont les suivantes: 0,5 pt, 1 pt, 1,5 pt, 2,25 pt, 3 pt, 4,5 pt, 6 pt ou Pas de ligne si vous ne voulez pas utiliser de trait. Pour changer la couleur du contour, cliquez sur la case colorée et sélectionnez la couleur voulue. Pour modifier le type de contour, sélectionnez l'option voulue dans la liste déroulante correspondante (une ligne continue est appliquée par défaut, vous pouvez la remplacer par l'une des lignes pointillées disponibles). Ajouter une ombre activez cette option pour ajouter une ombre portée à un graphique SmartArt. Cliquez sur Afficher les paramètres avancés pour accéder aux paramètres avancés."
    },
   {
        "id": "UsageInstructions/InsertSymbols.htm", 
        "title": "Insérer des symboles et des caractères", 
        "body": "Pendant le processus de travail dans l'Éditeur de Présentations, vous devrez peut-être insérer un symbole qui n'est pas sur votre clavier. Pour insérer de tels symboles dans votre présentation, utilisez l'option Insérer un symbole et suivez ces étapes simples : placez le curseur là où vous souhaitez ajouter un symbole spécifique, passez à l'onglet Insertion de la barre d'outils supérieure, cliquez sur Symbole, De la fenêtre Symbole qui apparaît sélectionnez le symbole approprié, utilisez la section Plage pour trouvez rapidement le symbole nécessaire. Tous les symboles sont divisés en groupes spécifiques, par exemple, sélectionnez des «symboles monétaires» spécifiques si vous souhaitez insérer un caractère monétaire. Si ce caractère n'est pas dans le jeu, sélectionnez une police différente. Plusieurs d'entre elles ont également des caractères différents que ceux du jeu standard. Ou, entrez la valeur hexadécimale Unicode du symbole souhaité dans le champ Valeur hexadécimale Unicode. Ce code se trouve dans la Carte des caractères. Vous pouvez aussi utiliser l'onglet Symboles spéciaux pour choisir un symbole spéciale proposé dans la liste. Les symboles précédemment utilisés sont également affichés dans le champ des Caractères spéciaux récemment utilisés, cliquez sur Insérer. Le caractère sélectionné sera ajouté au document. Insérer des symboles ASCII La table ASCII est utilisée pour ajouter des caractères. Pour le faire, maintenez la touche ALT enfoncée et utilisez le pavé numérique pour saisir le code de caractère. Remarque : utilisez le pavé numérique, pas les chiffres du clavier principal. Pour activer le pavé numérique, appuyez sur la touche verrouillage numérique. Par exemple, pour ajouter un caractère de paragraphe (§), maintenez la touche ALT tout en tapant 789, puis relâchez la touche ALT. Insérer des symboles à l'aide de la table des caractères Unicode Des caractères et symboles supplémentaires peuvent également être trouvés dans la table des symboles Windows. Pour ouvrir cette table, effectuez l'une des opérations suivantes : Dans le champ Rechercher, tapez 'Table de caractères' et ouvrez-la, Appuyez simultanément sur Win+R, puis dans la fenêtre ouverte tapez charmap.exe et cliquez sur OK. Dans la Table des caractères ouverte, sélectionnez l'un des Jeux de caractères, Groupes et Polices. Ensuite, cliquez sur les caractères nécessaires, copiez-les dans le presse-papiers et insérez-les au bon endroit dans la présentation."
    },
   {
        "id": "UsageInstructions/InsertTables.htm", 
        "title": "Insérer et mettre en forme des tableaux", 
        "body": "Insérer un tableau Pour insérer un tableau sur une diapositive dans l'Éditeur de Présentations, sélectionnez la diapositive où le tableau sera ajouté, passez à l'onglet Insertion de la barre d'outils supérieure, cliquez sur l'icône Tableau sur la la barre d'outils supérieure, sélectionnez l'une des options suivantes pour créer le tableau : soit un tableau avec le nombre prédéfini de cellules (10 par 8 cellules maximum) Si vous voulez ajouter rapidement un tableau, il vous suffit de sélectionner le nombre de lignes (8 au maximum) et de colonnes (10 au maximum). soit un tableau personnalisé Si vous avez besoin d'un tableau de plus de 10 par 8 cellules, sélectionnez l'option Insérer un tableau personnalisé pour ouvrir la fenêtre et spécifiez le nombre nécessaire de lignes et de colonnes, ensuite cliquez sur le bouton OK. lorsque vous voulez insérer un tableau comme un objet OLE : Sélectionnez l'option Insérer la feuille de calcul au menu Tableau dans l'onglet Insérer. La fenêtre correspondante s'ouvre dans laquelle vous pouvez saisir les données requises et les modifier en utilisant les outils de formatage du Tableur, par exemple sélectionner la police, le type et le style, saisir le format de nombre, insérer des functions, mettre en forme les tableaux etc. L'en-tête contient le bouton Zone visible dans le coin supérieur droit de la fenêtre. Choisissez l'option Modifier la zone visible afin de sélectionner la zone qui sera affichée quand l'objet est inseré au sein du présentation ; les autres données ne sont pas perdues mais seulement masquées. Cliquez sur Terminé lorsque c'est prêt. Cliquez sur le bouton Afficher la zone visible afin d'afficher la zone sélectionnée qui aura une bordure bleue. Quand tout est prêt, cliquez sur le bouton Enregistrer et quitter. après avoir ajouté le tableau, vous pouvez modifier ses paramètres et son position. On peut aussi ajouter un tableau dans l'espace réservé en cliquant sur l'icône Tableau à l'intérieur de l'espace et spécifier le nombre de cellules ou en utilisant l'option Insérer un tableau personnalisé : Pour déplacer une tableau, faites glisser les poignées situés sur ses bords jusqu'à ce que la taille du tableau soit atteinte. Vous pouvez également modifier manuellement la largeur d'une certaine colonne ou la hauteur d'une ligne. Déplacez le curseur de la souris sur la bordure droite de la colonne de sorte que le curseur se transforme en flèche bidirectionnelle et faites glisser la bordure vers la gauche ou la droite pour définir la largeur nécessaire. Pour modifier manuellement la hauteur d'une seule ligne, déplacez le curseur de la souris sur la bordure inférieure de la ligne afin que le curseur devienne la flèche bidirectionnelle et déplacez la bordure vers le haut ou le bas. Vous pouvez définir la position du tableau sur la diapositive en le faisant glisser horizontalement ou verticalement. Remarque : pour vous déplacer dans un tableau, vous pouvez utiliser des raccourcis clavier. On peut aussi ajouter un tableau à la disposition de diapositive. Pour en savoir plus, veuillez consulter cet article. Ajuster les paramètres du tableau Plusieurs paramètres du tableau ainsi que sa structure peuvent être modifiés à l'aide de la barre latérale droite. Pour l'activer, cliquez sur le tableau et sélectionnez l'icône Paramètres du graphique à droite. Les sections Lignes et Colonnes en haut vous permettent de mettre en évidence certaines lignes/colonnes en leur appliquant une mise en forme spécifique ou de mettre en évidence différentes lignes/colonnes avec les différentes couleurs d'arrière-plan pour les distinguer clairement. Les options suivantes sont disponibles : En-tête - accentue la ligne la plus haute du tableau avec un formatage spécial. Total -accentue la ligne la plus basse du tableau avec un formatage spécial. Bordé - permet l'alternance des couleurs d'arrière-plan pour les lignes paires et impaires. Première - accentue la colonne la plus à gauche du tableau avec un formatage spécial. Dernière - accentue la colonne la plus à droite du tableau avec un formatage spécial. Bordé - permet l'alternance des couleurs d'arrière-plan pour les colonnes paires et impaires. La section Sélectionner à partir d'un modèle vous permet de choisir l'un des styles de tableaux prédéfinis. Chaque modèle combine certains paramètres de formatage, tels qu'une couleur d'arrière-plan, un style de bordure, des lignes/colonnes en bandes, etc. Selon les options cochées dans les sections Lignes et/ou Colonnes ci-dessus, l'ensemble de modèles sera affiché différemment. Par exemple, si vous avez coché l'option En-tête dans la section Lignes et l'option Bordé dans la section Colonnes, la liste des modèles affichés inclura uniquement les modèles avec la ligne d'en-tête et les colonnes en bandes activées : La section Style de bordure vous permet de modifier la mise en forme appliquée qui correspond au modèle sélectionné. Vous pouvez sélectionner toute la table ou une certaine plage de cellules dont vous souhaitez modifier la mise en forme et définir tous les paramètres manuellement. Paramètres de Bordure - définissez la largeur de la bordure en utilisant la liste (ou choisissez l'option Aucune bordure), sélectionnez sa Couleur dans les palettes disponibles et déterminez la façon dont elle sera affichée dans les cellules en cliquant sur les icônes : Couleur d'arrière-plan - sélectionnez la couleur de l'arrière-plan dans les cellules sélectionnées. La section Lignes et colonnes vous permet d'effectuer les opérations suivantes : Sélectionner une ligne, une colonne, une cellule (en fonction de la position du curseur) ou la totalité du tableau. Insérer une nouvelle ligne au-dessus ou en dessous de celle sélectionnée ainsi qu'une nouvelle colonne à gauche ou à droite de celle sélectionnée. Supprimer une ligne, une colonne (en fonction de la position du curseur ou de la sélection) ou la totalité du tableau. Fusionner les cellules - pour fusionner les cellules précédemment sélectionnées en une seule. Fractionner la cellule... - scinder une cellule précédemment sélectionnée en un certain nombre de lignes et de colonnes. Cette option ouvre la fenêtre suivante : Entrez le Nombre de colonnes et le Nombre de lignes en lesquelles la cellule sélectionnée doit être divisée et appuyez sur OK. Remarque : les options de la section Lignes et colonnes sont également accessibles depuis le menu contextuel. Taille de la cellule est utilisée pour ajuster la largeur et la hauteur de la cellule actuellement sélectionnée. Dans cette section, vous pouvez également Distribuer les lignes afin que toutes les cellules sélectionnées aient la même hauteur ou Distribuer les colonnes de sorte que toutes les cellules sélectionnées aient la même largeur. Les options Distribuer les lignes/les colonnes sont également accessibles depuis le menu contextuel. Configurer les paramètres avancés du tableau Pour modifier les paramètres du tableau avancés, cliquez sur le tableau avec le clic droit de la souris et sélectionnez l'option Paramètres avancés du tableau du menu contextuel ou utilisez le lien Afficher les paramètres avancés sur la barre latérale droite. La fenêtre paramètres de l'image s'ouvre : L'onglet Emplacement permet de saisir les paramètres du tableau suivants : Taille - utilisez cette option pour modifier la largeur et/ou la hauteur du tableau. Si le bouton Proportions constantes est activé (dans ce cas, il ressemble à ceci ), la largeur et la hauteur seront changées en même temps, le ratio d'aspect de l'image originale sera préservé. Position - saisissez la position exacte en utilisant les champs Horizontalement et Verticalement, ainsi que le champ De dans lesquels vous pouvez accéder aux paramètres tels que Coin supérieur gauche et Au centre. La section Marges permet d'ajuster l'espace entre le texte dans les cellules et la bordure de la cellule : entrez manuellement les valeurs de Marges de cellule, ou cochez la case Utiliser les marges par défaut pour appliquer les valeurs prédéfinies (si nécessaire, elles peuvent également être ajustées). L'onglet Texte de remplacement permet de spécifier un Titre et une Description qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information du tableau. Pour mettre en forme le texte saisi dans les cellules du tableau, vous pouvez utiliser les icônes dans l'onglet Accueil de la barre d'outils supérieure. Le menu contextuel qui s'affiche lorsque vous cliquez sur la table avec le bouton droit de la souris inclut deux options supplémentaires : Alignement vertical des cellules - vous permet de définir le type préféré d'alignement vertical du texte dans les cellules sélectionnées : Aligner en haut, Aligner au centre ou Aligner en bas. Lien hypertexte - vous permet d'insérer un lien hypertexte dans la cellule sélectionnée."
    },
   {
        "id": "UsageInstructions/InsertText.htm", 
        "title": "Insérer et mettre en forme du texte", 
        "body": "Insérer une zone de texte dans votre présentation Dans Presentation Editor, vous pouvez ajouter un nouveau texte de trois manières différentes: Ajoutez un passage de texte dans l'espace réservé de texte correspondant inclus dans la présentation de diapositive. Pour ce faire, placez simplement le curseur dans l'espace réservé et tapez votre texte ou collez-le en utilisant la combinaison de touches Ctrl+V à la place du texte par défaut correspondant. Ajoutez un passage de texte n'importe où sur une diapositive. Vous pouvez insérer une zone de texte (un cadre rectangulaire qui permet de saisir du texte) ou un objet Text Art (une zone de texte avec un style de police et une couleur prédéfinis permettant d'appliquer certains effets de texte). Selon le type d'objet textuel voulu, vous pouvez effectuer les opérations suivantes: pour ajouter une zone de texte, cliquez sur l'icône Zone de texte sous l'onglet Accueil ou Insertion de la barre d'outils supérieure, sélectionnez l'une des options suivantes: Insérer une zone de texte horizontale ou Insérer une zone de texte verticale, ensuite cliquez sur l'emplacement où vous souhaitez insérer la zone de texte, maintenez le bouton de la souris enfoncé et faites glisser la bordure pour définir sa taille. Lorsque vous relâchez le bouton de la souris, le point d'insertion apparaîtra dans la zone de texte ajoutée, vous permettant d'entrer votre texte. Il est également possible d'insérer une zone de texte en cliquant sur l'icône Forme dans la barre d'outils supérieure et en sélectionnant la forme du groupe Formes de base. pour ajouter un objet Text Art, cliquez sur l'icône Text Art sous l'onglet Insertion dans la barre d'outils supérieure, ensuite cliquez sur le modèle de style souhaité - l'objet Text Art sera ajouté au centre de la diapositive. Sélectionnez le texte par défaut dans la zone de texte avec la souris et remplacez-le par votre propre texte. Ajouter un passage de texte dans une forme automatique. Sélectionnez une forme et commencez à taper votre texte. Cliquez en dehors de l'objet texte pour appliquer les modifications et revenir à la diapositive. Le texte dans l'objet textuel fait partie de celui ci (ainsi si vous déplacez ou faites pivoter l'objet textuel, le texte change de position lui aussi). Comme un objet texte inséré représente un cadre rectangulaire (avec des bordures de zone de texte invisibles par défaut) avec du texte à l'intérieur et que ce cadre est une forme automatique commune, vous pouvez modifier aussi bien les propriétés de forme que de texte. Pour supprimer l'objet textuel ajouté, cliquez sur la bordure de la zone de texte et appuyez sur la touche Suppr du clavier. Le texte dans la zone de texte sera également supprimé. Mettre en forme une zone de texte Sélectionnez la zone de texte en cliquant sur sa bordure pour pouvoir modifier ses propriétés. Lorsque la zone de texte est sélectionnée, ses bordures sont affichées en tant que lignes pleines (non pointillées). Pour redimensionner, déplacer, faire pivoter la zone de texte, utilisez les poignées spéciales sur les bords de la forme. pour modifier le remplissage, le contour ou remplacer la boîte rectangulaire par une forme différente, cliquez sur l'icône Paramètres avancés de forme, cliquez sur l'icône Paramètres de forme dans la barre latérale sur la droite et utilisez les options correspondantes. pour aligner une zone de texte sur la diapositive, la faire pivoter ou la retourner, organiser des zones de texte par rapport à d'autres objets, cliquez avec le bouton droit sur la bordure de la zone de texte et utilisez les options de menu contextuel. pour créer des colonnes de texte dans la zone de texte, cliquez sur l'icône appropriée de la barre de mise en forme du texte et choisissez l'option appropriée, ou cliquez avec le bouton droit sur la bordure de la zone de texte, cliquez sur Paramètres avancés de forme et passez à l'onglet Colonnes dans la fenêtre Forme - Paramètres avancés. Mettre en forme le texte dans la zone de texte Cliquez sur le texte dans la zone de texte pour pouvoir modifier ses propriétés. Lorsque le texte est sélectionné, les bordures de la zone de texte sont affichées en lignes pointillées. Remarque: il est également possible de modifier le formatage du texte lorsque la zone de texte (et non le texte lui-même) est sélectionnée. Dans ce cas, toutes les modifications seront appliquées à tout le texte dans la zone de texte. Certaines options de mise en forme de police (type de police, taille, couleur et styles de décoration) peuvent être appliquées séparément à une partie du texte précédemment sélectionnée. Aligner le texte dans la zone de texte Le texte peut être aligné horizontalement de quatre façons : aligné à gauche, centré, aligné à droite et justifié. Pour ce faire: placez le curseur à la position où vous voulez appliquer l'alignement (une nouvelle ligne ou le texte déjà saisi), faites dérouler la liste Alignement horizontal sous l'onglet Accueil de la barre d'outils supérieure, sélectionnez le type d'alignement que vous allez appliquer: l'option Aligner le texte à gauche permet d'aligner votre texte sur le côté gauche de la zone de texte (le côté droit reste non aligné). l'option Aligner le texte au centre permet d'aligner votre texte au centre de la zone de texte (les côtés droit et gauche ne sont pas alignés). l'option Aligner le texte à droite permet d'aligner votre texte sur le côté droit de la zone de texte (le côté gauche reste non aligné). l'option Justifier permet d'aligner votre texte par les côtés gauche et droit de la zone de texte (l'espacement supplémentaire est ajouté si nécessaire pour garder l'alignement). Remarque: on peut configurer les mêmes paramètres dans la fenêtre Paragraphe - Paramètres avancés . Le texte peut être aligné verticalement de trois façons: haut, milieu ou bas. Pour ce faire: placez le curseur à la position où vous voulez appliquer l'alignement (une nouvelle ligne ou le texte déjà saisi), faites dérouler la liste Alignement vertical sous l'onglet Accueil de la barre d'outils supérieure, sélectionnez le type d'alignement que vous allez appliquer: l'option Aligner le texte en haut permet d'aligner votre texte en haut de la zone de texte. l'option Aligner le texte au milieu permet d'aligner votre texte au centre de la zone de texte. l'option Aligner le texte en bas permet d'aligner votre texte en bas de la zone de texte. Changer la direction du texte Pour Faire pivoter le texte dans la zone de texte, cliquez avec le bouton droit sur le texte, sélectionnez l'option Direction du texte, puis choisissez l'une des options disponibles: Horizontal (sélectionné par défaut), Rotation du texte vers le bas (définit une direction verticale, de haut en bas) ou Rotation du texte vers le haut (définit une direction verticale, de bas en haut). Ajuster le type de police, la taille, la couleur et appliquer les styles de décoration Vous pouvez sélectionner le type, la taille et la couleur de police et appliquer l'un des styles de décoration en utilisant les icônes correspondantes situées dans l'onglet Accueil de la barre d'outils supérieure. Remarque: si vous voulez appliquer la mise en forme au texte déjà saisi, sélectionnez-le avec la souris ou en utilisant le clavieret appliquez la mise en forme. Vous pouvez aussi positionner le curseur de la souris sur le mot à mettre en forme. Police Sert à sélectionner l'une des polices disponibles dans la liste. Si une police requise n'est pas disponible dans la liste, vous pouvez la télécharger et l'installer sur votre système d'exploitation, après quoi la police sera disponible pour utilisation dans la version de bureau. Taille de la police Sert à sélectionner la taille de la police parmi les valeurs disponibles dans la liste déroulante, les valeurs par défaut sont: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 et 96). Il est également possible d'entrer manuellement une valeur personnalisée dans le champ de taille de police jusqu'à 300 pt. Appuyer sur la touche Entrée pour confirmer Augmenter la taille de la police Sert à modifier la taille de la police en la rendant plus grande à un point chaque fois que vous appuyez sur le bouton. Diminuer la taille de la police Sert à modifier la taille de la police en la rendant plus petite à un point chaque fois que vous appuyez sur le bouton. Modifier la casse Sert à modifier la casse du texte. Majuscule en début de phrase - la casse à correspondre la casse de la proposition ordinaire. minuscule - mettre en minuscule toutes les lettres MAJUSCULES - mettre en majuscule toutes les lettres Mettre en majuscule chaque mot - mettre en majuscule la première lettre de chaque mot Inverser la casse - basculer entre d'affichages de la casse du texte. Couleur de surlignage Est utilisé pour marquer des phrases, des fragments, des mots ou même des caractères séparés en ajoutant une bande de couleur qui imite l'effet du surligneur sur le texte. Vous pouvez sélectionner la partie voulue du texte, puis cliquer sur la flèche vers le bas à côté de l'icône pour sélectionner une couleur dans la palette (cet ensemble de couleurs ne dépend pas du Jeux de couleurs sélectionné et comprend 16 couleurs). La couleur sera appliquée à la sélection. Alternativement, vous pouvez tout d'abord choisir une couleur de surbrillance et ensuite commencer à sélectionner le texte avec la souris - le pointeur de la souris ressemblera à ceci et vous serez en mesure de surligner plusieurs parties différentes de votre texte de manière séquentielle. Pour enlever la mise en surbrillance, cliquez à nouveau sur l'icône. Pour effacer la couleur de surbrillance, choisissez l'option Pas de remplissage. Couleur de police Sert à changer la couleur des lettres /caractères dans le texte. Cliquez sur la flèche vers le bas à côté de l'icône pour sélectionner la couleur. Gras Sert à mettre la police en gras pour lui donner plus de poids. Italique Sert à mettre la police en italique pour lui donner une certaine inclinaison à droite. Souligné Sert à souligner le texte avec la ligne qui passe sous les lettres. Barré Sert à barrer le texte par la ligne passant par les lettres. Exposant Sert à rendre le texte plus petit et le déplacer vers la partie supérieure de la ligne du texte, par exemple comme dans les fractions. Indice Sert à rendre le texte plus petit et le déplacer vers la partie inférieure de la ligne du texte, par exemple comme dans les formules chimiques. Définir l'interligne et modifier les retraits de paragraphe Vous pouvez définir l'interligne pour les lignes de texte dans le paragraphe ainsi que les marges entre le paragraphe courant et le précédent ou le suivant. Pour ce faire, placez le curseur dans le paragraphe de votre choix ou sélectionnez plusieurs paragraphes avec la souris, utilisez les champs correspondants de l'onglet Paramètres de texte dans la barre latérale sur la droite pour obtenir les résultats nécessaires: Interligne - réglez la hauteur de la ligne pour les lignes de texte dans le paragraphe. Vous pouvez choisir parmi deux options: multiple (sert à régler l'interligne exprimée en nombre supérieur à 1), exactement (sert à définir l'interligne fixe). Spécifiez la valeur nécessaire dans le champ situé à droite. Espacement de paragraphe - définissez l'espace entre les paragraphes. Avant - réglez la taille de l'espace avant le paragraphe. Après - réglez la taille de l'espace après le paragraphe. Remarque: on peut configurer les mêmes paramètres dans la fenêtre Paragraphe - Paramètres avancés . Pour modifier rapidement l'interligne du paragraphe actuel, vous pouvez aussi cliquer sur l'icône Interligne sous l'onglet Accueil de la barre d'outils supérieure et sélectionnez la valeur nécessaire dans la liste: 1.0, 1.15, 1.5, 2.0, 2.5, ou 3.0 lignes. Pour modifier le décalage de paragraphe du côté gauche de la zone de texte, placez le curseur dans le paragraphe de votre choix ou sélectionnez plusieurs paragraphes à l'aide de la souris et utilisez les icônes correspondantes dans l'onglet Accueil de la barre d'outils supérieure: Réduire le retrait et Augmenter le retrait . Configurer les paramètres avancés du paragraphe Pour ouvrir la fenêtre Paragraphe - Paramètres avancés, faites un clic droit sur le texte et sélectionnez l'option Paramètres avancés du texte dans le menu. Il est également possible de placer le curseur dans le paragraphe de votre choix - l'onglet Paramètres du paragraphe devient actif sur la barre latérale droite. Appuyez sur le lien Afficher les paramètres avancés. La fenêtre paramètres du paragraphe s'ouvre: L'onglet Retrait et emplacement permet de: modifier le type d'alignement du paragraphe, modifier les retraits du paragraphe par rapport aux marges internes de la zone de texte, A gauche - spécifiez le décalage du paragraphe de la marge interne gauche de la zone de texte et saisissez la valeur numérique appropriée, A droite - spécifiez le décalage du paragraphe de la marge interne droite de la zone de texte et saisissez la valeur numérique appropriée, Spécial - spécifier le retrait de la première ligne du paragraphe: sélectionnez l'élément approprié du menu ((aucun), Première ligne, Suspendu) et modifiez la valeur numérique par défaut pour les options Première ligne ou Suspendu, modifiez l'interligne du paragraphe. Vous pouvez également utilisez la règle horizontale pour changer les retraits. Sélectionnez le(s) paragraphe(s) et faites glisser les marqueurs tout au long de la règle Le marqueur Retrait de première ligne sert à définir le décalage de la marge interne gauche de la zone de texte pour la première ligne du paragraphe. Le marqueur Retrait suspendu sert à définir le décalage de la marge interne gauche de la zone de texte pour la deuxième ligne et toutes les lignes suivantes du paragraphe. Le marqueur Retrait de gauche sert à définir le décalage du paragraphe de la marge interne gauche de la zone de texte. Le marqueur Retrait de droite sert à définir le décalage du paragraphe de la marge interne gauche de la zone de texte. Remarque: si vous ne voyez pas les règles, passez à l'onglet Accueil de la barre d'outils supérieure, cliquez sur l'icône Paramètres d'affichage dans le coin supérieur droit et décochez l'option Masquer les règles pour les afficher. L'onglet Police comporte les paramètres suivants: Barré sert à barrer le texte par la ligne passant par les lettres. Barré double sert à barrer le texte par la ligne double passant par les lettres. Exposant sert à rendre le texte plus petit et le déplacer vers la partie supérieure de la ligne du texte, par exemple comme dans les fractions. Indice sert à rendre le texte plus petit et le déplacer vers la partie inférieure de la ligne du texte, par exemple comme dans les formules chimiques. Petites majuscules sert à mettre toutes les lettres en petite majuscule. Majuscules sert à mettre toutes les lettres en majuscule. Espacement des caractères sert à définir l'espace entre les caractères. Augmentez la valeur par défaut pour appliquer l'espacement Étendu, ou diminuez la valeur par défaut pour appliquer l'espacement Condensé. Utilisez les touches fléchées ou entrez la valeur voulue dans la case. Tous les changements seront affichés dans le champ de prévisualisation ci-dessous. L'onglet Tabulation vous permet de changer des taquets de tabulation c'est-à-dire l'emplacement où le curseur s'arrête quand vous appuyez sur la touche Tab du clavier. La Tabulation par défaut est 2.54 cm. Vous pouvez augmenter ou diminuer cette valeur en utilisant les boutons à flèche ou en saisissant la valeur nécessaire dans la zone. Position sert à personnaliser les taquets de tabulation. Saisissez la valeur nécessaire dans ce champ, réglez-la en utilisant les boutons à flèche et cliquez sur le bouton Spécifier. La position du taquet de tabulation personnalisée sera ajoutée à la liste dans le champ au-dessous. Alignement sert à définir le type d'alignement pour chaque taquet de tabulation de la liste. Sélectionnez le taquet nécessaire dans la liste, choisissez l'option De gauche, De centre ou De droite dans la liste déroulante Alignement et cliquez sur le bouton Spécifier. De gauche sert à aligner le texte sur le côté gauche du taquet de tabulation; le texte se déplace à droite du taquet de tabulation quand vous saisissez le texte. Le taquet de tabulation sera indiqué sur la règle horizontale par le marqueur. Du centre - sert à centrer le texte à l'emplacement du taquet de tabulation. Le taquet de tabulation sera indiqué sur la règle horizontale par le marqueur. De droite - sert à aligner le texte sur le côté droit du taquet de tabulation; le texte se déplace à gauche du taquet de tabulation quand vous saisissez le texte. Le taquet de tabulation sera indiqué sur la règle horizontale par le marqueur. Pour supprimer un taquet de tabulation de la liste sélectionnez-le et cliquez sur le bouton Supprimer ou utilisez le bouton Supprimer tout pour vider la liste. Pour définir les taquets de tabulation vous pouvez utiliser la règle horizontale: Cliquez sur le bouton de sélection de tabulation dans le coin supérieur gauche de la zone de travail pour choisir le type d'arrêt de tabulation requis: À gauche, Au centre, À droite . Cliquez sur le bord inférieur de la règle là où vous voulez positionner le taquet de tabulation. Faites-le glisser tout au long de la règle pour changer son emplacement. Pour supprimer le taquet de tabulation ajouté faites-le glisser en dehors de la règle. Remarque: si vous ne voyez pas les règles, passez à l'onglet Accueil de la barre d'outils supérieure, cliquez sur l'icône Paramètres d'affichage dans le coin supérieur droit et décochez l'option Masquer les règles pour les afficher. Modifier un style Text Art Sélectionnez un objet texte et cliquez sur l'icône des Paramètres de Text Art dans la barre latérale sur la droite. Modifiez le style de texte appliqué en sélectionnant un nouveau Modèle dans la galerie. Vous pouvez également modifier le style de base en sélectionnant un type de police différent, une autre taille, etc. Changez le remplissage et le contourde police. Les options disponibles sont les mêmes que pour les formes automatiques. Appliquez un effet de texte en sélectionnant le type de transformation de texte voulu dans la galerie Transformation. Vous pouvez ajuster le degré de distorsion du texte en faisant glisser la poignée en forme de diamant rose."
    },
   {
        "id": "UsageInstructions/ManageSlides.htm", 
        "title": "Gérer des diapositives", 
        "body": "Par défaut, une nouvelle présentation inclut une diapositive de titre vide. Dans Presentation Editor, vous pouvez créer de nouvelles diapositives, copier une diapositive et la coller dans l'endroit nécessaire dans la liste des diapositives, dupliquer des diapositives, déplacer les diapositives pour modifier l'ordre d'apparition, supprimer des diapositives inutiles et masquer certaines diapositives. Pour créer une nouvelle diapositive de titre ou une diapositive pour votre table des matières: cliquez sur l'icône Ajouter diapositive sous l'onglet Accueil ou Insertion de la barre d'outils supérieure, ou faites un clic droit sur une diapositive dans la liste et sélectionnez l'option Nouvelle diapositive dans le menu contextuel, ou appuyer sur Ctrl+M. Pour créer une nouvelle diapositive avec une autre de mise en page: cliquez sur la flèche à côté de l'icône Ajouter diapositive sous l'onglet Accueil ou Insertion de la barre d'outils supérieure, ou sélectionnez une diapositive avec la mise en page nécessaire dans le menu. Remarque: vous pouvez modifier la mise en page de la diapositive ajoutée à tout moment. Pour en savoir plus, consultez la section Définir les paramètres de la diapositive. Une nouvelle diapositive sera insérée après la diapositive sélectionnée dans la liste des diapositives existantes située à gauche. Pour dupliquer une diapositive: sélectionnez une ou plusieurs diapositives dans la liste des diapositives existantes située à gauche, cliquez avec le bouton droit de la souris et sélectionnez l'option Dupliquer la diapositive dans le menu contextuel ou passez à l'onglet Accueil ou Insertion, cliquez sur le bouton Ajouter une diapositive, ensuite sélectionnez l'option de menu Dupliquer la diapositive. La diapositive dupliquée sera insérée après la diapositive sélectionnée dans la liste des diapositives. Pour copier une diapositive: sélectionnez une ou plusieurs diapositives à copier dans la liste des diapositives existantes située à gauche, appuyez sur Ctrl+C, dans la liste des diapositives, sélectionnez la diapositive après laquelle la diapositive copiée doit être collée. appuyer sur Ctrl+V. Une fois la diapositive collée, le bouton Collage spécial apparaîtra à côté de la diapositive insérée. Cliquez sur ce bouton pour sélectionner l'option de collage requise ou utiliser un raccourci en appuyant sur la touche Ctrl et la lettre correspondante qui apparaît à côté des commandes. Les options de Collage spécial disponibles: Utiliser le thème de destination (Ctrl+H) - permet d'appliquer la mise en forme en fonction du thème de la présentation en cours. Cette option est utilisée par défaut. Reproduire la mise en forme de la source (Ctrl+K) - permet de garder la mise en forme da la diapositive copiée. Image (Ctrl+U) - permet de coller la diapositive en tant qu'image qu'on ne pourra pas modifier. Pour déplacer une diapositive existante: faites un clic gauche sur la diapositive nécessaire ou sur plusieurs diapositives dans la liste des diapositives existantes située à gauche, maintenez votre doigt dessus et faites-la glisser à l'endroit nécessaire dans la liste (une ligne horizontale indique un nouvel emplacement). Pour supprimer une diapositive inutile: faites un clic droit sur la diapositive ou plusieurs diapositives à supprimer dans la liste des diapositives existantes située à gauche, sélectionnez l'option Supprimer la diapositive depuis le menu contextuel. Pour masquer les diapositives: faites un clic droit sur la diapositive ou plusieurs diapositives à masquer dans la liste des diapositives existantes située à gauche, sélectionnez l'option Masquer la diapositive depuis le menu contextuel. Le numéro correspondant à la diapositive masquée dans la liste des diapositives située à gauche sera barré. Pour afficher la diapositive masquée comme une diapositive ordinaire dans la liste des diapositives, cliquez sur l'option Masquer la diapositive encore une fois. Remarque: utilisez cette option lorsque vous ne souhaitez pas afficher certaines diapositives aux autres utilisateurs, mais vous voulez pouvoir y accéder au besoin. Si vous lancez le diaporama en mode de Présentateur, vous pouvez voir toutes les diapositives existantes dans la liste située à gauche, tandis que les numéros des diapositives masquées sont barrés. Si vous souhaitez afficher une diapositive masquée à d'autres utilisateurs, cliquez sur cette diapositive dans la liste située à gauche - la diapositive s'affichera. Pour sélectionner toutes les diapositives existantes à la fois: faites un clic droit sur une diapositive dans la liste des diapositives existantes située à gauche, sélectionnez l'option Sélectionner tout depuis le menu contextuel. Pour sélectionner certaines diapositives: maintenez la touche Ctrl, sélectionnez les diapositives nécessaires en faisant un clic gauche dessus dans la liste des diapositives existantes située à gauche. Remarque: toutes les séquences de touches permettant de gérer les diapositives sont énumérées sur la page Raccourcis clavier."
    },
   {
        "id": "UsageInstructions/ManipulateObjects.htm", 
        "title": "Manipuler des objets", 
        "body": "Dans l'Éditeur de Présentations, vous pouvez redimensionner, déplacer, faire pivoter différents objets manuellement sur une diapositive à l'aide des poignées spéciales. Vous pouvez également spécifier les dimensions et la position de certains objets à l'aide de la barre latérale droite ou de la fenêtre Paramètres avancés. Remarque : la liste des raccourcis clavier qui peuvent être utilisés lorsque vous travaillez avec des objets est disponible ici. Redimensionner des objets Pour changer la taille d'une forme automatique/image/graphique/zone de texte, faites glisser les petits carreaux situés sur les bords de l'objet. Pour garder les proportions de l'objet sélectionné lors du redimensionnement, maintenez la touche Maj enfoncée et faites glisser l'une des icônes de coin. Pour spécifier la largeur et la hauteur précise d'un graphique, sélectionnez-le avec la souris et utilisez la section Taille de la barre latérale droite activée. Pour spécifier les dimensions précises d'une image ou d'une forme automatique, cliquez avec le bouton droit de la souris sur l'objet nécessaire et sélectionnez l'option Paramètres avancés de l'image/forme automatique du menu contextuel. Réglez les valeurs nécessaires dans l'onglet Taille de la fenêtre Paramètres avancés et cliquez sur le bouton OK. Modifier des formes automatiques Lors de la modification des formes, par exemple des Flèches figurées ou les Légendes, l'icône jaune en forme de diamant est aussi disponible. Elle vous permet d'ajuster certains aspects de la forme, par exemple, la longueur de la pointe d'une flèche. Pour modifier une forme, vous pouvez également utiliser l'option Modifier les points dans le menu contextuel. Modifier les points sert à personnaliser ou modifier le contour d'une forme. Pour activer les points d'ancrage modifiables, faites un clic droit sur la forme et sélectionnez Modifier les points dans le menu. Les carrés noirs qui apparaissent sont les points de rencontre entre deux lignes et la ligne rouge trace le contour de la forme. Cliquez sur l'un de ces points et faites-le glisser pour repositionner et modifier le contour de la forme. Lorsque vous cliquez sur le point d'ancrage, deux lignes bleus avec des carrés blanches apparaissent. Ce sont les points de contrôle Bézier permettant de créer une courbe et de modifier la finesse de la courbe. Autant que les points d'ancrage sont actifs, vous pouvez les modifier et supprimer : Pour ajouter un point de contrôle à une forme, maintenez la touche Ctrl enfoncée et cliquez sur l'emplacement du point de contrôle souhaité. Pour supprimer un point, maintenez la touche Ctrl enfoncée et cliquez sur le point superflu. Déplacer des objets Pour modifier la position d'une forme automatique/image/tableau/graphique/bloc de texte, utilisez l'icône qui apparaît si vous placez le curseur de votre souris sur l'objet. Faites glisser l'objet vers la position nécessaire sans relâcher le bouton de la souris. Pour déplacer l'objet par incrément équivaut à un pixel, maintenez la touche Ctrl enfoncée et utilisez les flèches du clavier. Pour déplacer l'objet strictement à l'horizontale/verticale et l'empêcher de se déplacer dans une direction perpendiculaire, maintenez la touche Shift enfoncée lors du glissement. Pour spécifier les dimensions précises d'une image, cliquez avec le bouton droit de la souris sur l'objet nécessaire et sélectionnez l'option Paramètres avancés de l'image du menu contextuel. Réglez les valeurs nécessaires dans l'onglet Taille de la fenêtre Paramètres avancés et cliquez sur le bouton OK. Faire pivoter des objets Pour faire pivoter manuellement une forme automatique/image/bloc de texte, placez le curseur de la souris sur la poignée de rotation et faites-la glisser vers la droite ou vers la gauche. Pour limiter la rotation de l'angle à des incréments de 15 degrés, maintenez la touche Maj enfoncée. Pour faire pivoter par incréments de 15 degrés, maintenez enfoncée la touche Maj tout en faisant pivoter. Pour faire pivoter l'objet de 90 degrés dans le sens inverse des aiguilles d'une montre/dans le sens des aiguilles d'une montre ou le retourner horizontalement/verticalement, vous pouvez utiliser la section Rotation de la barre latérale droite qui sera activée lorsque vous aurez sélectionné l'objet nécessaire. Pour l'ouvrir, cliquez sur l'icône Paramètres de la forme ou Paramètres de l'image à droite. Cliquez sur l'un des boutons : pour faire pivoter l'objet de 90 degrés dans le sens inverse des aiguilles d'une montre pour faire pivoter l'objet de 90 degrés dans le sens des aiguilles d'une montre pour retourner l'objet horizontalement (de gauche à droite) pour retourner l'objet verticalement (à l'envers) Il est également possible de cliquer avec le bouton droit de la souris sur l'objet, de choisir l'option Faire pivoter dans le menu contextuel, puis d'utiliser une des options de rotation disponibles. Pour faire pivoter l'objet selon un angle exactement spécifié, cliquez sur le lien Afficher les paramètres avancés dans la barre latérale droite et utilisez l'onglet Rotation de la fenêtre Paramètres avancés. Spécifiez la valeur nécessaire mesurée en degrés dans le champ Angle et cliquez sur OK."
    },
   {
        "id": "UsageInstructions/MathAutoCorrect.htm", 
        "title": "Fonctionnalités de correction automatique", 
        "body": "Les fonctionnalités de correction automatique ONLYOFFICE dans l'Éditeur de Présentations fournissent des options pour définir les éléments à mettre en forme automatiquement ou insérer des symboles mathématiques à remplacer les caractères reconnus. Toutes les options sont disponibles dans la boîte de dialogue appropriée. Pour y accéder, passez à l'onglet Fichier -> Paramètres avancés -> Vérification -> Options de correction automatique. La boîte de dialogue Correction automatique comprend quatre onglets : AutoMaths, Fonctions reconnues, Mise en forme automatique au cours de la frappe et Correction automatique de texte. AutoMaths Lorsque vous travaillez dans l'éditeur d'équations, vous pouvez insérer plusieurs symboles, accents et opérateurs mathématiques en les tapant sur clavier plutôt que de les rechercher dans la bibliothèque. Dans l'éditeur d'équations, placez le point d'insertion dans l'espace réservé et tapez le code de correction mathématique, puis touchez la Barre d'espace. Le code que vous avez saisi, serait converti en symbole approprié mais l'espace est supprimé. Remarque : Les codes sont sensibles à la casse. Vous pouvez ajouter, modifier, rétablir et supprimer les éléments de la liste de corrections automatiques. Passez à l'onglet Fichier -> Paramètres avancés -> Vérification -> Options de correction automatique --> AutoMaths. Ajoutez un élément à la liste de corrections automatiques. Saisissez le code de correction automatique dans la zone Remplacer. Saisissez le symbole que vous souhaitez attribuer au code approprié dans la zone Par. Cliquez sur Ajouter. Modifier un élément de la liste de corrections automatiques. Sélectionnez l'élément à modifier. Vous pouvez modifier les informations dans toutes les deux zones : le code dans la zone Remplacer et le symbole dans la zone Par. Cliquez sur Remplacer. Supprimer les éléments de la liste de corrections automatiques. Sélectionnez l'élément que vous souhaitez supprimer de la liste. Cliquez sur Supprimer. Pour rétablir les éléments supprimés, sélectionnez l'élément que vous souhaitez rétablir dans la liste et appuyez sur Restaurer. Utilisez l'option Rétablir paramètres par défaut pour réinitialiser les réglages par défaut. Tous les éléments que vous avez ajouté, seraient supprimés et toutes les modifications seraient annulées pour rétablir sa valeur d'origine. Pour désactiver la correction automatique mathématique et éviter les changements et les remplacements automatiques, il faut décocher la case Remplacer le texte au cours de la frappe. Le tableau ci-dessous affiche tous le codes disponibles dans l'Éditeur de Présentations à présent. On peut trouver la liste complète de codes disponibles sous l'onglet Fichier dans les Paramètres avancés... -> Vérification de l'orthographe -> Vérification Les codes disponibles Code Symbole Catégorie !! Symboles ... Dots :: Opérateurs := Opérateurs /< Opérateurs relationnels /> Opérateurs relationnels /= Opérateurs relationnels \\above Indices et exposants \\acute Accentuation \\aleph Lettres hébraïques \\alpha Lettres grecques \\Alpha Lettres grecques \\amalg Opérateurs binaires \\angle Notation de géométrie \\aoint Intégrales \\approx Opérateurs relationnels \\asmash Flèches \\ast Opérateurs binaires \\asymp Opérateurs relationnels \\atop Opérateurs \\bar Trait suscrit/souscrit \\Bar Accentuation \\because Opérateurs relationnels \\begin Séparateurs \\below Indices et exposants \\bet Lettres hébraïques \\beta Lettres grecques \\Beta Lettres grecques \\beth Lettres hébraïques \\bigcap Grands opérateurs \\bigcup Grands opérateurs \\bigodot Grands opérateurs \\bigoplus Grands opérateurs \\bigotimes Grands opérateurs \\bigsqcup Grands opérateurs \\biguplus Grands opérateurs \\bigvee Grands opérateurs \\bigwedge Grands opérateurs \\binomial Équations \\bot Notations logiques \\bowtie Opérateurs relationnels \\box Symboles \\boxdot Opérateurs binaires \\boxminus Opérateurs binaires \\boxplus Opérateurs binaires \\bra Séparateurs \\break Symboles \\breve Accentuation \\bullet Opérateurs binaires \\cap Opérateurs binaires \\cbrt Racine carrée et radicaux \\cases Symboles \\cdot Opérateurs binaires \\cdots Dots \\check Accentuation \\chi Lettres grecques \\Chi Lettres grecques \\circ Opérateurs binaires \\close Séparateurs \\clubsuit Symboles \\coint Intégrales \\cong Opérateurs relationnels \\coprod Opérateurs mathématiques \\cup Opérateurs binaires \\dalet Lettres hébraïques \\daleth Lettres hébraïques \\dashv Opérateurs relationnels \\dd Lettres avec double barres \\Dd Lettres avec double barres \\ddddot Accentuation \\dddot Accentuation \\ddot Accentuation \\ddots Dots \\defeq Opérateurs relationnels \\degc Symboles \\degf Symboles \\degree Symboles \\delta Lettres grecques \\Delta Lettres grecques \\Deltaeq Opérateurs \\diamond Opérateurs binaires \\diamondsuit Symboles \\div Opérateurs binaires \\dot Accentuation \\doteq Opérateurs relationnels \\dots Dots \\doublea Lettres avec double barres \\doubleA Lettres avec double barres \\doubleb Lettres avec double barres \\doubleB Lettres avec double barres \\doublec Lettres avec double barres \\doubleC Lettres avec double barres \\doubled Lettres avec double barres \\doubleD Lettres avec double barres \\doublee Lettres avec double barres \\doubleE Lettres avec double barres \\doublef Lettres avec double barres \\doubleF Lettres avec double barres \\doubleg Lettres avec double barres \\doubleG Lettres avec double barres \\doubleh Lettres avec double barres \\doubleH Lettres avec double barres \\doublei Lettres avec double barres \\doubleI Lettres avec double barres \\doublej Lettres avec double barres \\doubleJ Lettres avec double barres \\doublek Lettres avec double barres \\doubleK Lettres avec double barres \\doublel Lettres avec double barres \\doubleL Lettres avec double barres \\doublem Lettres avec double barres \\doubleM Lettres avec double barres \\doublen Lettres avec double barres \\doubleN Lettres avec double barres \\doubleo Lettres avec double barres \\doubleO Lettres avec double barres \\doublep Lettres avec double barres \\doubleP Lettres avec double barres \\doubleq Lettres avec double barres \\doubleQ Lettres avec double barres \\doubler Lettres avec double barres \\doubleR Lettres avec double barres \\doubles Lettres avec double barres \\doubleS Lettres avec double barres \\doublet Lettres avec double barres \\doubleT Lettres avec double barres \\doubleu Lettres avec double barres \\doubleU Lettres avec double barres \\doublev Lettres avec double barres \\doubleV Lettres avec double barres \\doublew Lettres avec double barres \\doubleW Lettres avec double barres \\doublex Lettres avec double barres \\doubleX Lettres avec double barres \\doubley Lettres avec double barres \\doubleY Lettres avec double barres \\doublez Lettres avec double barres \\doubleZ Lettres avec double barres \\downarrow Flèches \\Downarrow Flèches \\dsmash Flèches \\ee Lettres avec double barres \\ell Symboles \\emptyset Ensemble de notations \\emsp Caractères d'espace \\end Séparateurs \\ensp Caractères d'espace \\epsilon Lettres grecques \\Epsilon Lettres grecques \\eqarray Symboles \\equiv Opérateurs relationnels \\eta Lettres grecques \\Eta Lettres grecques \\exists Notations logiques \\forall Notations logiques \\fraktura Fraktur \\frakturA Fraktur \\frakturb Fraktur \\frakturB Fraktur \\frakturc Fraktur \\frakturC Fraktur \\frakturd Fraktur \\frakturD Fraktur \\frakture Fraktur \\frakturE Fraktur \\frakturf Fraktur \\frakturF Fraktur \\frakturg Fraktur \\frakturG Fraktur \\frakturh Fraktur \\frakturH Fraktur \\frakturi Fraktur \\frakturI Fraktur \\frakturk Fraktur \\frakturK Fraktur \\frakturl Fraktur \\frakturL Fraktur \\frakturm Fraktur \\frakturM Fraktur \\frakturn Fraktur \\frakturN Fraktur \\frakturo Fraktur \\frakturO Fraktur \\frakturp Fraktur \\frakturP Fraktur \\frakturq Fraktur \\frakturQ Fraktur \\frakturr Fraktur \\frakturR Fraktur \\frakturs Fraktur \\frakturS Fraktur \\frakturt Fraktur \\frakturT Fraktur \\frakturu Fraktur \\frakturU Fraktur \\frakturv Fraktur \\frakturV Fraktur \\frakturw Fraktur \\frakturW Fraktur \\frakturx Fraktur \\frakturX Fraktur \\fraktury Fraktur \\frakturY Fraktur \\frakturz Fraktur \\frakturZ Fraktur \\frown Opérateurs relationnels \\funcapply Opérateurs binaires \\G Lettres grecques \\gamma Lettres grecques \\Gamma Lettres grecques \\ge Opérateurs relationnels \\geq Opérateurs relationnels \\gets Flèches \\gg Opérateurs relationnels \\gimel Lettres hébraïques \\grave Accentuation \\hairsp Caractères d'espace \\hat Accentuation \\hbar Symboles \\heartsuit Symboles \\hookleftarrow Flèches \\hookrightarrow Flèches \\hphantom Flèches \\hsmash Flèches \\hvec Accentuation \\identitymatrix Matrices \\ii Lettres avec double barres \\iiint Intégrales \\iint Intégrales \\iiiint Intégrales \\Im Symboles \\imath Symboles \\in Opérateurs relationnels \\inc Symboles \\infty Symboles \\int Intégrales \\integral Intégrales \\iota Lettres grecques \\Iota Lettres grecques \\itimes Opérateurs mathématiques \\j Symboles \\jj Lettres avec double barres \\jmath Symboles \\kappa Lettres grecques \\Kappa Lettres grecques \\ket Séparateurs \\lambda Lettres grecques \\Lambda Lettres grecques \\langle Séparateurs \\lbbrack Séparateurs \\lbrace Séparateurs \\lbrack Séparateurs \\lceil Séparateurs \\ldiv Barres obliques \\ldivide Barres obliques \\ldots Dots \\le Opérateurs relationnels \\left Séparateurs \\leftarrow Flèches \\Leftarrow Flèches \\leftharpoondown Flèches \\leftharpoonup Flèches \\leftrightarrow Flèches \\Leftrightarrow Flèches \\leq Opérateurs relationnels \\lfloor Séparateurs \\lhvec Accentuation \\limit Limites \\ll Opérateurs relationnels \\lmoust Séparateurs \\Longleftarrow Flèches \\Longleftrightarrow Flèches \\Longrightarrow Flèches \\lrhar Flèches \\lvec Accentuation \\mapsto Flèches \\matrix Matrices \\medsp Caractères d'espace \\mid Opérateurs relationnels \\middle Symboles \\models Opérateurs relationnels \\mp Opérateurs binaires \\mu Lettres grecques \\Mu Lettres grecques \\nabla Symboles \\naryand Opérateurs \\nbsp Caractères d'espace \\ne Opérateurs relationnels \\nearrow Flèches \\neq Opérateurs relationnels \\ni Opérateurs relationnels \\norm Séparateurs \\notcontain Opérateurs relationnels \\notelement Opérateurs relationnels \\notin Opérateurs relationnels \\nu Lettres grecques \\Nu Lettres grecques \\nwarrow Flèches \\o Lettres grecques \\O Lettres grecques \\odot Opérateurs binaires \\of Opérateurs \\oiiint Intégrales \\oiint Intégrales \\oint Intégrales \\omega Lettres grecques \\Omega Lettres grecques \\ominus Opérateurs binaires \\open Séparateurs \\oplus Opérateurs binaires \\otimes Opérateurs binaires \\over Séparateurs \\overbar Accentuation \\overbrace Accentuation \\overbracket Accentuation \\overline Accentuation \\overparen Accentuation \\overshell Accentuation \\parallel Notation de géométrie \\partial Symboles \\pmatrix Matrices \\perp Notation de géométrie \\phantom Symboles \\phi Lettres grecques \\Phi Lettres grecques \\pi Lettres grecques \\Pi Lettres grecques \\pm Opérateurs binaires \\pppprime Nombres premiers \\ppprime Nombres premiers \\pprime Nombres premiers \\prec Opérateurs relationnels \\preceq Opérateurs relationnels \\prime Nombres premiers \\prod Opérateurs mathématiques \\propto Opérateurs relationnels \\psi Lettres grecques \\Psi Lettres grecques \\qdrt Racine carrée et radicaux \\quadratic Racine carrée et radicaux \\rangle Séparateurs \\Rangle Séparateurs \\ratio Opérateurs relationnels \\rbrace Séparateurs \\rbrack Séparateurs \\Rbrack Séparateurs \\rceil Séparateurs \\rddots Dots \\Re Symboles \\rect Symboles \\rfloor Séparateurs \\rho Lettres grecques \\Rho Lettres grecques \\rhvec Accentuation \\right Séparateurs \\rightarrow Flèches \\Rightarrow Flèches \\rightharpoondown Flèches \\rightharpoonup Flèches \\rmoust Séparateurs \\root Symboles \\scripta Scripts \\scriptA Scripts \\scriptb Scripts \\scriptB Scripts \\scriptc Scripts \\scriptC Scripts \\scriptd Scripts \\scriptD Scripts \\scripte Scripts \\scriptE Scripts \\scriptf Scripts \\scriptF Scripts \\scriptg Scripts \\scriptG Scripts \\scripth Scripts \\scriptH Scripts \\scripti Scripts \\scriptI Scripts \\scriptk Scripts \\scriptK Scripts \\scriptl Scripts \\scriptL Scripts \\scriptm Scripts \\scriptM Scripts \\scriptn Scripts \\scriptN Scripts \\scripto Scripts \\scriptO Scripts \\scriptp Scripts \\scriptP Scripts \\scriptq Scripts \\scriptQ Scripts \\scriptr Scripts \\scriptR Scripts \\scripts Scripts \\scriptS Scripts \\scriptt Scripts \\scriptT Scripts \\scriptu Scripts \\scriptU Scripts \\scriptv Scripts \\scriptV Scripts \\scriptw Scripts \\scriptW Scripts \\scriptx Scripts \\scriptX Scripts \\scripty Scripts \\scriptY Scripts \\scriptz Scripts \\scriptZ Scripts \\sdiv Barres obliques \\sdivide Barres obliques \\searrow Flèches \\setminus Opérateurs binaires \\sigma Lettres grecques \\Sigma Lettres grecques \\sim Opérateurs relationnels \\simeq Opérateurs relationnels \\smash Flèches \\smile Opérateurs relationnels \\spadesuit Symboles \\sqcap Opérateurs binaires \\sqcup Opérateurs binaires \\sqrt Racine carrée et radicaux \\sqsubseteq Ensemble de notations \\sqsuperseteq Ensemble de notations \\star Opérateurs binaires \\subset Ensemble de notations \\subseteq Ensemble de notations \\succ Opérateurs relationnels \\succeq Opérateurs relationnels \\sum Opérateurs mathématiques \\superset Ensemble de notations \\superseteq Ensemble de notations \\swarrow Flèches \\tau Lettres grecques \\Tau Lettres grecques \\therefore Opérateurs relationnels \\theta Lettres grecques \\Theta Lettres grecques \\thicksp Caractères d'espace \\thinsp Caractères d'espace \\tilde Accentuation \\times Opérateurs binaires \\to Flèches \\top Notations logiques \\tvec Flèches \\ubar Accentuation \\Ubar Accentuation \\underbar Accentuation \\underbrace Accentuation \\underbracket Accentuation \\underline Accentuation \\underparen Accentuation \\uparrow Flèches \\Uparrow Flèches \\updownarrow Flèches \\Updownarrow Flèches \\uplus Opérateurs binaires \\upsilon Lettres grecques \\Upsilon Lettres grecques \\varepsilon Lettres grecques \\varphi Lettres grecques \\varpi Lettres grecques \\varrho Lettres grecques \\varsigma Lettres grecques \\vartheta Lettres grecques \\vbar Séparateurs \\vdash Opérateurs relationnels \\vdots Dots \\vec Accentuation \\vee Opérateurs binaires \\vert Séparateurs \\Vert Séparateurs \\Vmatrix Matrices \\vphantom Flèches \\vthicksp Caractères d'espace \\wedge Opérateurs binaires \\wp Symboles \\wr Opérateurs binaires \\xi Lettres grecques \\Xi Lettres grecques \\zeta Lettres grecques \\Zeta Lettres grecques \\zwnj Caractères d'espace \\zwsp Caractères d'espace ~= Opérateurs relationnels -+ Opérateurs binaires +- Opérateurs binaires << Opérateurs relationnels <= Opérateurs relationnels -> Flèches >= Opérateurs relationnels >> Opérateurs relationnels Fonctions reconnues Sous cet onglet, vous pouvez trouver les expressions mathématiques que l'éditeur d'équations reconnait comme les fonctions et lesquelles ne seront pas mises en italique automatiquement. Pour accéder à la liste de fonctions reconnues, passez à l'onglet Fichier -> Paramètres avancés -> Vérification-> Options de correction automatique -> Fonctions reconnues. Pour ajouter un élément à la liste de fonctions reconnues, saisissez la fonction dans le champ vide et appuyez sur Ajouter. Pour supprimer un élément de la liste de fonctions reconnues, sélectionnez la fonction à supprimer et appuyez sur Supprimer. Pour rétablir les éléments supprimés, sélectionnez l'élément que vous souhaitez rétablir dans la liste et appuyez sur Restaurer. Utilisez l'option Rétablir paramètres par défaut pour réinitialiser les réglages par défaut. Toutes les fonctions que vous avez ajoutées, seraient supprimées et celles qui ont été supprimées, seraient rétablies. Mise en forme automatique au cours de la frappe Par défaut, l'éditeur met en forme automatiquement lors de la saisie selon les paramètres de format automatique, comme par exemple remplacer les guillemets ou les traits d'union par un tiret demi-cadratin, convertir des addresses web ou des chemins d'accès réseau en lien hypertextes, appliquer une liste à puces ou une liste numérotée lorsqu'il détecte que vous tapez une liste. L'option Ajouter un point avec un double espace permet d'insérer un point lorsqu'on appuie deux fois sur la barre d'espace. Activez ou désactivez cette option selon le cas. Par défaut, cette option est désactivée sur Linux et Windows et est activée sur macOS. Si vous souhaitez désactiver une des options de mise en forme automatique, désactivez la case à coche de l'élément pour lequel vous ne souhaitez pas de mise en forme automatique sous l'onglet Fichier -> Paramètres avancés -> Vérification -> Correction automatique -> Mise en forme automatique au cours de la frappe Correction automatique de texte Il est possible d'activer la correction automatique pour convertir en majuscule la première lettre des phrases. Par défaut, cette option est activée. Pour la désactiver, passez à l'onglet Fichier -> Paramètres avancés -> Vérification -> Options de correction automatique -> Correction automatique de texte et désactivez l'option Majuscule en début de phrase."
    },
   {
        "id": "UsageInstructions/MotionPath.htm", 
        "title": "Créer une animation de trajectoire de mouvement", 
        "body": "Trajectoires du mouvement font partie de la galerie d'effets d'animation et déterminent le mouvement d'un objet et la trajectoire qu'il suit. Les icônes de la galerie représentent la trajectoire suggérée. La galerie d'animations est disponible dans l'onglet Animation dans la barre d'outils supérieure. Appliquer un effet d'animation de trajectoire de mouvement passez à l'onglet Animation dans la barre d'outils supérieure, sélectionnez le texte, l'objet ou l'élément graphique auquel vous souhaitez appliquer un effet d'animation, sélectionnez l'un des modèles de trajectoire de mouvement prédéfinis à partir de la section Trajectoires du mouvement dans la galerie d'animations (Lignes, Arcs etc.) ou choisissez l'option Chemin personnalisé si vous souhaitez créer votre propre chemin. Vous pouvez apercevoir les effets d'animation sur la diapositive actuelle. Par défaut, les effets d'animation sont lus automatiquement lorsque vous les ajoutez à une diapositive, mais vous pouvez désactiver cette option. Cliquez sur la liste déroulante Aperçu dans l'onglet Animation et sélectionnez l'un des modes de prévisualisation : Aperçu pour afficher un aperçu lorsque vous cliquez sur le bouton Aperçu, Aperçu partiel pour afficher un aperçu automatiquement lorsque vous ajoutez une animation à une diapositive ou remplacez une animation existante. Ajouter un effet d'animation de chemin personnalisé Pour dessiner un chemin personnalisé, Cliquez sur l'objet auquel vous souhaitez ajouter une animation de chemin personnalisé. Marquez les points du chemin avec le bouton gauche de la souris. Vous pouvez dessiner une ligne en cliquant une fois sur le bouton gauche de la souris ou maintenir le bouton gauche pour créer n'importe quelle courbe requise. Le point de départ du chemin sera marqué d'une flèche directionnelle verte, le point d'arrivée sera rouge. Quand tout est prêt, double-cliquez sur le bouton gauche de la souris ou appuyez sur le bouton Esc afin d'arrêter de dessiner votre chemin. Modifier les points de trajectoire de mouvement Afin de modifier les points de trajectoire de mouvement, sélectionnez l'objet chemin, cliquez sur le bouton droit de la souris pour ouvrir le menu contextuel et choisissez l'option Modifier les points. Faites glisser les carrés noirs pour modifier la direction générale de la trajectoire ; faites glisser les carrés blancs pour modifier la courbe associée au carré noir sélectionné. Appuyez sur Esc ou n'importe où en dehors de l'objet chemin pour quitter le mode d'édition. Vous pouvez mettre à l'échelle la trajectoire de mouvement en cliquant dessus et en faisant glisser les points carrés sur les bordures de l'objet."
    },
   {
        "id": "UsageInstructions/OpenCreateNew.htm", 
        "title": "Créer une nouvelle présentation ou ouvrir une présentation existante", 
        "body": "Dans l'Éditeur de Présentations, vous pouvez ouvrir une présentation existante, créer une nouvelle présentation ou revenir à la liste de présentations existantes. Pour créer une nouvelle présentation Dans la version en ligne cliquez sur l'onglet Fichier de la barre d'outils supérieure, sélectionnez l'option Créer nouveau. Dans l'éditeur de bureau dans la fenêtre principale du programme, sélectionnez l'élément de menu Présentation dans la section Créer nouveau de la barre latérale gauche - un nouveau fichier s'ouvrira dans un nouvel onglet, une fois tous les changements nécessaires effectués, cliquez sur l'icône Enregistrer dans le coin supérieur gauche ou passez à l'onglet Fichier et choisissez l'élément de menu Enregistrer sous. dans la fenêtre du gestionnaire de fichiers, sélectionnez l'emplacement du fichier, spécifiez son nom, choisissez le format dans lequel vous souhaitez enregistrer la présentation (PPTX, Modèle de présentation (POTX), ODP, OTP, PDF ou PDFA) et cliquez sur le bouton Enregistrer. Pour ouvrir une présentation existante Dans l'>éditeur de bureau dans la fenêtre principale du programme, sélectionnez l'élément de menu Ouvrir fichier local dans la barre latérale gauche, choisissez la présentation nécessaire dans la fenêtre du gestionnaire de fichiers et cliquez sur le bouton Ouvrir. Vous pouvez également cliquer avec le bouton droit de la souris sur la présentation nécessaire dans la fenêtre du gestionnaire de fichiers, sélectionner l'option Ouvrir avec et choisir l'application nécessaire dans le menu. Si les fichiers de documents Office sont associés à l'application, vous pouvez également ouvrir les présentations en double-cliquant sur le nom du fichier dans la fenêtre d'exploration de fichiers. Tous les répertoires auxquels vous avez accédé à l'aide de l'éditeur de bureau seront affichés dans la liste Dossiers récents afin que vous puissiez y accéder rapidement. Cliquez sur le dossier nécessaire pour sélectionner l'un des fichiers qui y sont stockés. Pour ouvrir une présentation récemment éditée Dans la version en ligne cliquez sur l'onglet Fichier de la barre d'outils supérieure, sélectionnez l'option Ouvrir récent..., choisissez la présentation dont vous avez besoin dans la liste des documents récemment édités. Dans l'éditeur de bureau dans la fenêtre principale du programme, sélectionnez l'élément de menu Fichiers récents dans la barre latérale gauche, choisissez la présentation dont vous avez besoin dans la liste des documents récemment édités. Pour renommer une présentation ouverte Dans l'éditeur en ligne cliquez sur le nom de la présentation en haut de la page, saisissez un nouveau nom de la présentation, cliquez sur Entrer pour accepter les modifications. Pour ouvrir le dossier dans lequel le fichier est stocké dans un nouvel onglet du navigateur de la version en ligne, dans la fenêtre de l'explorateur de fichiers de la version de bureau, cliquez sur l'icône Ouvrir l'emplacement du fichier à droite de l'en-tête de l'éditeur. Vous pouvez aussi passer à l'onglet Fichier sur la barre d'outils supérieure et sélectionner l'option Ouvrir l'emplacement du fichier."
    },
   {
        "id": "UsageInstructions/PhotoEditor.htm", 
        "title": "Modification d'une image", 
        "body": "Éditeur de Présentations ONLYOFFICE dispose d'un éditeur de photos puissant qui permet aux utilisateurs d'appliquer divers effets de filtre à vos images et de faire les différents types d'annotations. Sélectionnez une image incorporée dans votre présentation. Passez à l'onglet Modules complémentaires et choisissez Photo Editor. Vous êtes dans l'environnement de traitement des images. Au-dessous de l'image il y a les cases à cocher et les filtres en curseur suivants: Niveaux de gris, Sépia 1, Sépia 2, Flou, Embosser, Inverser, Affûter; Enlever les blancs (Seuil, Distance), Transparence des dégradés</b>, Brillance, Bruit, Pixélateur, Filtre de couleur; Teinte, Multiplication, Mélange. Au-dessous, les filtres dont vous pouvez accéder avec les boutons Annuler, Rétablir et Remettre à zéro; Supprimer, Supprimer tout</b>; Rogner (Personnalisé, Carré, 3:2, 4:3, 5:4, 7:5, 16:9); Retournement </b>(Retourner X, Retourner Y, Remettre à zéro); Rotation (à 30 degrés, -30 degrés, Gamme); Dessiner (Libre, Direct, Couleur, Gamme); Forme (Rectangle, Cercle, Triangle, Remplir, Trait, Largeur du trait); Icône (Flèches, Étoiles, Polygone, Emplacement, Coeur, Bulles, Icône personnalisée, Couleur); Texte (Gras, Italique, Souligné, Gauche, Centre, Droite, Couleur, Taille de texte); Masque. N'hésitez pas à les essayer tous et rappelez-vous que vous pouvez annuler les modifications à tout moment. Une fois que vous avez terminé, cliquez sur OK. Maintenant l'image modifiée est insérée dans votre présentation."
    },
   {
        "id": "UsageInstructions/PreviewPresentation.htm", 
        "title": "Aperçu de la présentation", 
        "body": "Démarrer l'aperçu Remarque : Lors du téléchargement d'une présentation crée à l'aide des outils de présentation alternatifs, il est possible de lancer un aperçu des effets d'animation éventuels. Pour afficher un aperçu de la présentation en cours de l'édition dans l'Éditeur de Présentations : cliquez sur l'icône Démarrer le diaporama dans l'onglet Accueil de la barre d'outils supérieure ou sur le côté gauche de la barre d'état, ou sélectionnez une diapositive dans la liste à gauche, cliquez dessus avec le bouton droit de la souris et choisissez l'option Démarrer le diaporama dans le menu contextuel. L'aperçu commencera à partir de la diapositive actuellement sélectionnée. Vous pouvez également cliquer sur la flèche à côté de l'icône Démarrer le diaporama dans l'onglet Accueil de la barre d'outils supérieure et sélectionner l'une des options disponibles: Afficher depuis le début - pour commencer l'aperçu à partir de la toute première diapositive, Afficher à partir de la diapositive actuelle - pour démarrer l'aperçu depuis la diapositive actuellement sélectionnée, Afficher le mode Présentateur - pour lancer l'aperçu en mode Présentateur, ce qui permet d'afficher la présentation à votre audience sans les notes de diapositives tout en affichant la présentation avec les notes sur un moniteur différent. Afficher les paramètres - pour ouvrir une fenêtre de paramètres qui permet de définir une seule option : Boucler en continu jusqu'à ce que 'Echap' soit appuyé. Cochez cette option si nécessaire et cliquez sur OK. Si vous activez cette option, la présentation s'affichera jusqu'à ce que vous appuyiez sur la touche Echap du clavier, c'est-à-dire que lorsque la dernière diapositive de la présentation sera atteinte, vous pourrez revenir à la première diapositive à nouveau. Si vous désactivez cette option Une fois la dernière diapositive de la présentation atteinte, un écran noir apparaît pour vous informer que la présentation est terminée et vous pourrez quitter l'Aperçu. Utiliser le mode Aperçu En mode Aperçu, vous pouvez utiliser les contrôles suivants dans le coin inférieur gauche : le bouton Diapositive précédente vous permet de revenir à la diapositive précédente. le bouton Mettre en pause la présentation vous permet d'arrêter l'aperçu. Lorsque le bouton est enfoncé, il devient le bouton . le bouton Démarrer la présentation vous permet de reprendre l'aperçu. Lorsque le bouton est enfoncé, il devient le bouton . le bouton Diapositive suivante vous permet d'avancer à la diapositive suivante. l'Indicateur de numéro de diapositive affiche le numéro de diapositive en cours ainsi que le nombre total de diapositives dans la présentation. Pour aller à une certaine diapositive en mode aperçu, cliquez sur l'Indicateur de numéro de diapositive, entrez le numéro de diapositive nécessaire dans la fenêtre ouverte et appuyez sur Entrée. le bouton Plein écran vous permet de passer en mode plein écran. Le bouton Quitter le plein écran vous permet de quitter le mode plein écran. le bouton Fermer le diaporama vous permet de quitter le mode aperçu. Vous pouvez également utiliser les raccourcis clavier pour naviguer entre les diapositives en mode Aperçu. Utiliser le mode Présentateur Remarque: dans l'édition de bureau, le mode Présentateur n'est disponible lorsque le deuxième moniteur est connecté. En mode Présentateur, vous pouvez afficher vos présentations avec des notes de diapositives dans une fenêtre séparée, tout en les affichant sans notes sur un moniteur différent. Les notes de chaque diapositive s'affichent sous la zone d'aperçu de la diapositive. Pour naviguer entre les diapositives, vous pouvez utiliser les boutons et ou cliquer sur les diapositives dans la liste à gauche. Les numéros de diapositives masquées sont barrés dans la liste à gauche. Si vous souhaitez afficher une diapositive masquée aux autres, il suffit de cliquer dessus dans la liste à gauche - la diapositive s'affichera. Vous pouvez utiliser les contrôles suivants sous la zone d'aperçu de diapositive : le Chronomètre affiche le temps écoulé de la présentation au format hh.mm.ss. le bouton Mettre en pause la présentation vous permet d'arrêter l'aperçu. Lorsque le bouton est enfoncé, il devient le bouton . le bouton Démarrer la présentation vous permet de reprendre l'aperçu. Lorsque le bouton est enfoncé, il devient le bouton . le bouton Réinitialiser permet de réinitialiser le temps écoulé de la présentation. le bouton Diapositive précédente vous permet de revenir à la diapositive précédente. le bouton Diapositive suivante vous permet d'avancer à la diapositive suivante. l'Indicateur de numéro de diapositive affiche le numéro de diapositive en cours ainsi que le nombre total de diapositives dans la présentation. le bouton Pointeur vous permet de mettre en évidence quelque chose sur l'écran lors de l'affichage de la présentation. Lorsque cette option est activée, le bouton ressemble à ceci : . Pour pointer vers certains objets, placez le pointeur de votre souris sur la zone d'aperçu de la diapositive et déplacez le pointeur sur la diapositive. Le pointeur aura l'apparence suivante : . Pour désactiver cette option, cliquez à nouveau sur le bouton . le bouton Fin du diaporama vous permet de quitter le mode Présentateur."
    },
   {
        "id": "UsageInstructions/SavePrintDownload.htm", 
        "title": "Enregistrer/imprimer/télécharger votre classeur", 
        "body": "Enregistrer/imprimer/télécharger votre présentation Enregistrement Par défaut, l'Éditeur de Présentations en ligne enregistre automatiquement votre fichier toutes les 2 secondes afin de prévenir la perte des données en cas de fermeture inattendue de l'éditeur. Si vous co-éditez le fichier en mode Rapide, le minuteur récupère les mises à jour 25 fois par seconde et enregistre les modifications si elles ont été effectuées. Lorsque le fichier est co-édité en mode Strict, les modifications sont automatiquement sauvegardées à des intervalles de 10 minutes. Si nécessaire, vous pouvez facilement changer la périodicité de l'enregistrement automatique ou même désactiver cette fonction sur la page Paramètres avancés. Pour enregistrer manuellement votre présentation actuelle dans le format et l'emplacement actuels, cliquez sur l'icône Enregistrer dans la partie gauche de l'en-tête de l'éditeur, ou utilisez la combinaison des touches Ctrl+S, ou cliquez sur l'onglet Fichier de la barre d'outils supérieure et sélectionnez l'option Enregistrer. Dans la version de bureau, pour éviter la perte de données en cas de fermeture inattendue du programme, vous pouvez activer l'option Récupération automatique sur la page Paramètres avancés. Dans la version de bureau, vous pouvez enregistrer la présentation sous un autre nom, dans un nouvel emplacement ou format, cliquez sur l'onglet Fichier de la barre d'outils supérieure, sélectionnez l'option Enregistrer sous..., sélectionnez l'un des formats disponibles selon vos besoins : PPTX, ODP, PDF, PDF/A, PNG, JPG. Vous pouvez également choisir l'option Modèle de présentation (POTX or OTP). Téléchargement en cours Dans la version en ligne, vous pouvez télécharger la présentation résultante sur le disque dur de votre ordinateur, cliquez sur l'onglet Fichier de la barre d'outils supérieure, sélectionnez l'option Télécharger comme..., sélectionnez l'un des formats disponibles selon vos besoins : PPTX, PDF, ODP, POTX, PDF/A, OTP, PNG, JPG. Enregistrer une copie Dans la version en ligne, vous pouvez enregistrer une copie du fichier sur votre portail, cliquez sur l'onglet Fichier de la barre d'outils supérieure, sélectionnez l'option Enregistrer la copie sous..., sélectionnez l'un des formats disponibles selon vos besoins : PPTX, PDF, ODP, POTX, PDF/A, OTP, PNG, JPG. sélectionnez un emplacement pour le fichier sur le portail et appuyez sur Enregistrer. Impression Pour imprimer la présentation active, cliquez sur l'icône Imprimer dans la partie gauche de l'en-tête de l'éditeur, ou utilisez la combinaison des touches Ctrl+P, ou cliquez sur l'onglet Fichier de la barre d'outils supérieure et sélectionnez l'option Imprimer. Le navigateur Firefox permet d'imprimer sans télécharger le document au format .pdf d'avance. Il est aussi possible d'imprimer les diapositives sélectionnés en utilisant l'option Imprimer la sélection du menu contextuel en mode Édition aussi que en mode Affichage (cliquez avec le bouton droit de la souris sur les diapositives sélectionnées et choisissez Imprimer la sélection). Dans la version de bureau, le fichier sera imprimé directement. Dans la version en ligne, un fichier PDF est généré depuis votre présentation. Vous pouvez l'ouvrir et l'imprimer, ou l'enregistrer sur le disque dur de l'ordinateur ou sur un support amovible pour l'imprimer plus tard. Certains navigateurs (par ex. Chrome et Opera) supportent l'impression directe."
    },
   {
        "id": "UsageInstructions/SetSlideParameters.htm", 
        "title": "Définir les paramètres de la diapositive", 
        "body": "Pour personnaliser votre présentation dans l'Éditeur de Présentations, vous pouvez sélectionner le thème, les jeux de couleurs, la taille et l'orientation de la diapositive pour toute la présentation, changer le remplissage de l'arrière-plan ou la mise en page pour chaque diapositive séparée aussi bien qu'appliquer des transitions entre les diapositives. Il est également possible d'ajouter des notes explicatives à chaque diapositive qui peuvent être utiles lorsque la présentation est en mode Présentateur. Les Thèmes vous permettent de changer rapidement la conception de présentation, notamment l'apparence d'arrière-plan de diapositives, les polices prédéfinies pour les titres et les textes, la palette de couleurs qui est utilisée pour les éléments de présentation. Pour sélectionner un thème pour la présentation, cliquez sur le thème prédéfini voulu dans la galerie de thèmes sur le côté droit de l'onglet Accueil de la barre d'outils supérieure. Le thème choisi sera appliqué à toutes les diapositives à moins que vous n'en ayez choisi quelques-unes pour appliquer le thème. Pour modifier le thème sélectionné pour une ou plusieurs diapositives, vous pouvez cliquer avec le bouton droit sur les diapositives sélectionnées dans la liste de gauche (ou cliquer avec le bouton droit sur une diapositive dans la zone d'édition), sélectionner l'option Changer de thème dans le menu contextuel et choisir le thème voulu. Les Jeux de couleurs servent à définir des couleurs prédéfinies utilisées pour les éléments de présentation (polices, lignes, remplissages etc.) et vous permettent de maintenir la cohérence des couleurs dans toute la présentation. Pour changer de jeu de couleurs, cliquez sur l'icône Modifier le jeu de couleurs dans l'onglet Accueil de la barre d'outils supérieure et sélectionnez le jeu nécessaire de la liste déroulante. Le jeu de couleurs sélectionné sera appliqué à toutes les diapositives. Pour changer la taille de toutes les diapositives de la présentation, cliquez sur l'icône Sélectionner la taille de la diapositive sous l'onglet Accueil de la barre d'outils supérieure et sélectionnez l'option nécessaire depuis la liste déroulante. Vous pouvez sélectionner : l'un des deux paramètres prédéfinis - Standard (4:3) ou Écran large (16:9), l'option Paramètres avancés pour ouvrir la fenêtre Paramètres de taille et sélectionnez l'un des préréglages disponibles ou définissez la taille Personnalisée en spécifiant les valeurs de Largeur et Hauteur. Les options disponibles sont les suivantes : Standard (4:3), Plein écran (16:9), Plein écran (16:10), Papier à lettres (8.5x11 in), Ledger Paper (11x17 in), A3 (297x420 mm), Format A4 (210x297 mm), B4 (ICO) Papier (250x353 mm), B5 (ICO) Papier (176x250 mm), Diapositives 35 mm, Transparent, Bannière, Plein écran, Personnalisé. Le menu Orientation de la diapositive permet de changer le type d'orientation actuellement sélectionné. Le type d'orientation par défaut est Paysage qui peut être commuté sur Portrait. Pour modifier le remplissage de l'arrière-plan: sélectionnez les diapositives dont le remplissage vous voulez modifier de la liste des diapositives à gauche. Ou cliquez sur n'importe quel espace vide dans la diapositive en cours de la modification dans la zone de travail pour changer le type de remplissage de cette diapositive séparée. dans l'onglet Paramètres de la diapositive de la barre latérale droite, sélectionnez une des options : Couleur de remplissage - sélectionnez cette option pour spécifier la couleur unie à utiliser pour remplir l'espace intérieur de la diapositive sélectionnée. Remplissage en dégradé - sélectionnez cette option pour sélectionner deux couleurs et remplir la diapositive d'une transition douce entre elles. Image ou Texture - sélectionnez cette option pour utiliser une image ou une texture prédéfinie en tant que l'arrière-plan de la diapositive. Modèle - sélectionnez cette option pour sélectionner le modèle à deux couleurs composé des éléments répétés pour remplir l'espace intérieur de la diapositive. Pas de remplissage - sélectionnez cette option si vous ne voulez pas utiliser un remplissage. Opacité - faites glisser le curseur ou saisissez la valeur de pourcentage à la main. La valeur par défaut est 100%. Elle correspond à l'opacité complète. La valeur 0% correspond à la transparence totale. Pour en savoir plus consultez la section Remplir les objets et sélectionner les couleurs . Transitions vous aident à rendre votre présentation plus dynamique et captiver l'attention de votre auditoire. Pour appliquer une transition : sélectionnez les diapositives auxquelles vous voulez appliquer la transition de la liste de diapositives à gauche, sélectionnez une transition de la liste déroulante Effet de l'onglet Paramètres de la diapositive, Pour ouvrir l'onglet Paramètres de la diapositive, vous pouvez cliquer sur l'icône Paramètres de la diapositive à droite ou cliquer avec le bouton droit sur la diapositive dans la zone d'édition de la diapositive et sélectionner l'option Paramètres de la diapositive dans le menu contextuel. réglez les paramètres de la transition : choisissez le type de la transition, la durée et le mode de l'avancement des diapositives, cliquez sur le bouton Appliquer à toutes les diapositives si vous voulez appliquer la même transition à toutes les diapositives de la présentation. Pour en savoir plus sur ces options consultez le chapitre Appliquer des transitions . Pour appliquer une mise en page : sélectionnez les diapositives auxquelles vous voulez appliquer une nouvelle mise en page de la liste de diapositives à gauche, cliquez sur l'icône Modifier la disposition de la diapositive dans l'onglet Accueil de la barre d'outils supérieure, sélectionnez une mise en page nécessaire de la liste déroulante. Vous pouvez également cliquer avec le bouton droit sur la diapositive nécessaire dans la liste située à gauche, sélectionnez l'option Modifier la disposition de diapositive depuis le menu contextuel et sélectionnez la mise en page nécessaire. Actuellement les mises en page suivantes sont disponibles : Titre, Titre et objet, En-tête de section, Objet et deux objets, Deux textes et deux objets, Titre seulement, Vide, Titre, Objet et Légende, Image et Légende, Texte vertical, Texte vertical et texte. Pour ajouter des objets à la disposition d'une diapositive : cliquez sur l'icône Modifier la disposition de diapositive et sélectionnez la disposition pour laquelle vous souhaitez ajouter un objet, ajoutez les objets appropriés à la diapositive (image, tableau, graphique, forme) sous l'onglet Insertion dans la barre d'outil supérieure, ensuite cliquez avec le bouton droit de la souris sur cet objet et sélectionnez l'option Ajouter dans une mise en page, cliquez sur Modifier la disposition de diapositive sous l'onglet Accueil et sélectionnez la disposition modifiée. Les objets sélectionnés seront ajoutés à la disposition du thème actuel. Il n'est pas possible de sélectionner, redimensionner ou déplacer des objets que vous ajoutez d'une telle façon. Pour réinitialiser la disposition de diapositive à son état d'origine : sélectionnez les diapositives à réinitialiser la disposition à son état d'origine de la liste des diapositives à gauche, Maintenez la touche Ctrl enfoncée et sélectionnez une diapositive à la foi, ou maintenez la touche Maj enfoncée pour sélectionner toutes les diapositives à partir de la diapositive actuelle jusqu'à la dernière. cliquez avec le bouton droit de la souris sur une diapositive et sélectionnez l'option Réinitialiser la diapositive dans le menu contextuelle. Toutes les zones de texte et les objets dans la diapositive sont réinitialisés selon la disposition de la diapositive. Pour ajouter des notes à une diapositive : sélectionnez la diapositive à laquelle vous voulez ajouter une note dans la liste de diapositives à gauche, cliquez sur la légende Cliquez pour ajouter des notes sous la zone d'édition de la diapositive, tapez le texte de votre note. Vous pouvez mettre en forme du texte à l'aide des icônes sous l'onglet Accueil de la barre d'outils supérieure. Lorsque vous démarrez le diaporama en mode Présentateur, vous pouvez voir toutes les notes de la diapositive sous la zone d'aperçu de la diapositive."
    },
   {
        "id": "UsageInstructions/SupportSmartArt.htm", 
        "title": "Prise en charge des graphiques SmartArt par l'Éditeur de Présentations ONLYOFFICE", 
        "body": "Un graphique SmartArt sert à créer une représentation visuelle de la structure hiérarchique en choisissant le type du graphique qui convient le mieux. Éditeur de Présentations ONLYOFFICE prend en charge les graphiques SmartArt qui étaient créés dans d'autres applications. Vous pouvez ouvrir un fichier contenant SmartArt et le modifier en tant qu'un élément graphique en utilisant les outils d'édition disponibles. Une fois que vous avez cliqué sur le graphique SmartArt ou sur son élément, les onglets suivants deviennent actifs sur la barre latérale droite pour modifier la disposition du graphique : Paramètres de la diapositive pour modifier le remplissage d'arrière plan sur la diapositive, afficher ou masquer le numéro de diapositive, la date et l'heure. Pour en savoir plus, veuillez consulter Définir les paramètres de la diapositive et Insérer les pieds de page. Paramètres de la forme pour modifier les formes inclues dans le graphique. Vous pouvez modifier les formes, le remplissage, les lignes, le style d'habillage, la position, les poids et les flèches, la zone de texte et le texte de remplacement. Paramètres du paragraphe pour modifier les retraits et l'espacement, les enchaînements, les bordures et le remplissage, la police, les taquets et les marges intérieures. Veuillez consulter la section Mise en forme du texte pour une description détaillée de toutes options disponibles. Cette onglet n'est disponible que pour des éléments du graphique SmartArt. Paramètres de Texte Art pour modifier le style des objets Texte Art inclus dans le graphique SmartArt pour mettre en évidence du texte. Vous pouvez modifier le modèle de l'objet Text Art, le remplissage, la couleur et l'opacité, le poids, la couleur et le type des traits. Cette onglet n'est disponible que pour des éléments du graphique SmartArt. Faites un clic droit sur la bordure du graphique SmartArt ou sur la bordure de ses éléments pour accéder aux options suivantes : Organiser pour organiser des objets en utilisant les options suivantes : Mettre au premier plan, Mettre en arrière plan, Déplacer vers l'avant et Reculer sont disponibles pour le graphique SmartArt. Les options Grouper et Dissocier sont disponibles pour les éléments du graphique SmartArt et dépendent du fait s'ils sont groupés ou non. Aligner pour aligner du graphic ou des objets en utilisant les options suivantes : Aligner à gauche, Aligner au centre, Aligner à droite, Aligner en haut, Aligner au milieu, Aligner en bas, Distribuer horizontalement et Distribuer verticalement. Rotation pour définir le sens de rotation de l'élément inclus dans le graphique SmartArt. Faire pivoter à droite de 90°, Faire pivoter à gauche de 90°, Retourner horizontalement, Retourner verticalement. L'option Rotation n'est disponible que pour les éléments du graphique SmartArt. Paramètres avancés de la forme pour accéder aux paramètres avancés de mise en forme. Ajouter un commentaire pour laisser un commentaire sur le graphique SmartArt ou son élément. Ajouter dans une mise en page pour ajouter un graphique SmartArt à la disposition d'une diapositive. Faites un clic droit sur l'élément du graphique SmartArt pour accéder aux options suivantes : Alignement vertical pour définir l'alignement du texte dans l'élément du graphique SmartArt : Aligner en haut, Aligner au milieu ou Aligner en bas. Orientation du texte pour définir l'orientation du texte dans l'élément du graphique SmartArt : Horizontal, Rotation du texte vers le bas, Rotation du texte vers le haut. Paramètres avancés du paragraphe pour accéder aux paramètres avancés de mise en forme du paragraphe. Ajouter un commentaire pour laisser un commentaire sur le graphique SmartArt ou son élément. Lien hypertexte pour ajouter un lien hypertexte menant à l'élément du graphique SmartArt."
    },
   {
        "id": "UsageInstructions/Thesaurus.htm", 
        "title": "Remplacer un mot par synonyme", 
        "body": "Si on répète plusieurs fois le même mot ou il ne semble pas que le mot est juste, l'Éditeur de Présentations ONLYOFFICE vous permet de trouver les synonymes. Retrouvez les antonymes du mot affiché aussi. Sélectionnez le mot dans votre présentation. Passez à l'onglet Modules complémentaires et choisissez Thésaurus. Le panneau gauche liste les synonymes et les antonymes. Cliquez sur le mot à remplacer dans votre document."
    },
   {
        "id": "UsageInstructions/Translator.htm", 
        "title": "Traduire un texte", 
        "body": "Dans l'Éditeur de Présentations, vous pouvez traduire votre présentation dans de nombreuses langues disponibles. Sélectionnez le texte à traduire. Passez à l'onglet Modules complémentaires et choisissez Traducteur, l'application de traduction fait son apparition dans le panneau de gauche. Cliquer sur la liste déroulante et sélectionnez la langue préférée. La langue du texte choisie est détectée automatiquement et le texte est traduit dans la langue par défaut. Changez la langue cible : Cliquer sur la liste déroulante et sélectionnez la langue préférée. La traduction va changer tout de suite."
    },
   {
        "id": "UsageInstructions/ViewPresentationInfo.htm", 
        "title": "Afficher les informations sur la présentation", 
        "body": "Pour accéder aux informations détaillées sur la présentation actuellement modifiée dans l'Éditeur de Présentations, cliquez sur l'onglet Fichier de la barre d'outils supérieure et sélectionnez l'option Descriptif. Informations générales L'information sur la présentation comprend le titre de la présentation et l'application avec laquelle la présentation a été créée. Certains de ces paramètres sont mis à jour automatiquement mais les autres peuvent être modifiés. Emplacement - le dossier dans le module Documents où le fichier est stocké. Propriétaire - le nom de l'utilisateur qui a créé le fichier. Chargé - la date et l'heure quand le fichier a été créé. Ces paramètres ne sont disponibles que sous la version en ligne. Titre, sujet, commentaire - ces paramètres facilitent la classification des documents. Vos pouvez saisir l'information nécessaire dans les champs appropriés. Dernière modification - la date et l'heure quand le fichier a été modifié la dernière fois. Dernière modification par - le nom de l'utilisateur qui a apporté la dernière modification à la présentation quand plusieurs utilisateurs travaillent sur un même document. Application - l'application dans laquelle on a créé la présentation. Auteur - la personne qui a créé le fichier. Saisissez le nom approprié dans ce champ. Appuyez sur la touche Entrée pour ajouter un nouveau champ et spécifier encore un auteur. Si vous avez modifié les paramètres du fichier, cliquez sur Appliquer pour enregistrer les modifications. Remarque : Les éditeurs en ligne vous permettent de modifier le titre de la présentation directement à partir de l'interface de l'éditeur. Pour ce faire, cliquez sur l'onglet Fichier de la barre d'outils supérieure et sélectionnez l'option Renommer..., puis entrez le Nom de fichier voulu dans la nouvelle fenêtre qui s'ouvre et cliquez sur OK. Informations d'autorisation Dans la version en ligne, vous pouvez consulter les informations sur les permissions des fichiers stockés dans le cloud. Remarque : cette option n'est pas disponible pour les utilisateurs disposant des autorisations en Lecture seule. Pour savoir qui a le droit d'afficher ou de modifier le document, sélectionnez l'option Droits d'accès... dans la barre latérale de gauche. Vous pouvez également changer les droits d'accès actuels en cliquant sur le bouton Changer les droits d'accès dans la section Personnes qui ont des droits. Historique des versions Dans la version en ligne, il est possible d'afficher l'historique des fichiers stockés dans le nuage. Cette option n'est pas disponible pour les utilisateurs qui ont accès en Lecture seule. Pour afficher toutes les modifications qui ont été apportées à la présentation, sélectionnez l'option Historique des versions sur la barre latérale gauche. Il est également possible de parcourir l'historique des versions en utilisant l'icône Historique des versions sous l'onglet Collaboration de la barre d'outils supérieure. Vous voyez la liste des versions (modifications majeures) et des révisions (modifications mineures) de la présentation, le nom de la personne ayant modifié la présentation, la date et l'heure de la modification. Pour les versions de présentations, le numéro de la version s'affiche aussi (par ex. ver. 2). Pour afficher les modifications apportées à chaque version/révision, cliquez sur la version/révision appropriée sur la barre latérale gauche. Les modifications apportées par chaque auteur apparaissent dans la couleur associée à son nom qui s'affiche à côté de chaque nom sur la barre latérale gauche. Vous pouvez utiliser l'option Restaurer au-dessous de la version sélectionnée pour la restaurer. Pour revenir à la version actuelle de la présentation, utilisez l'option Fermer l'historique en haut de la liste des versions. Pour fermer l'onglet Fichier et reprendre le travail sur votre présentation, sélectionnez l'option Fermer le menu."
    },
   {
        "id": "UsageInstructions/YouTube.htm", 
        "title": "Insérer une vidéo", 
        "body": "Dans l'Éditeur de Présentations, vous pouvez insérer une vidéo dans votre présentation. Celle-ci sera affichée comme une image. Faites un double-clic sur l'image pour faire apparaître la boîte de dialogue vidéo. Vous pouvez démarrer votre vidéo ici. Copier l'URL de la vidéo à insérer. (l'adresse complète dans la barre d'adresse du navigateur) Accédez à votre présentation et placez le curseur à l'endroit où le vidéo doit être inséré. Passez à l'onglet Modules complémentaires et choisissez YouTube. Collez l'URL et cliquez sur OK. Vérifiez si la vidéo est vrai et cliquez sur OK au-dessous la vidéo. Maintenant la vidéo est insérée dans votre présentation."
    }
]