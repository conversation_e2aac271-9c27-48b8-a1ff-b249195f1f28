﻿<!DOCTYPE html>
<html>
	<head>
		<title>GLÄTTEN-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>GLÄTTEN-Funktion</h1>
			<p>Die Funktion <b>GLÄTTEN</b> gehört zur Gruppe der Text- und Datenfunktionen. Sie wird verwendet, um Leerzeichen aus Text zu entfernen.</p>
			<p>Die Formelsyntax der Funktion <b>GLÄTTEN</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>GLÄTTEN(Text)</em></b></p> 
			<p><b><em>Text</em></b> ist der Text aus dem Sie Leerzeichen entfernen möchten.</p>
			<p>Anwenden der Funktion <b>GLÄTTEN</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie in einer gewählten Zelle mit der rechten Maustaste und wählen Sie die Option <b>Funktion einfügen</b> aus dem Menü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>GLÄTTEN</b>.</li>
			<li>Geben Sie das erforderliche Argument ein.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="GLÄTTEN-Funktion" src="../images/trim.png" /></p>
		</div>
	</body>
</html>