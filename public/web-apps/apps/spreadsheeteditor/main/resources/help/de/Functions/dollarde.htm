﻿<!DOCTYPE html>
<html>
	<head>
		<title>NOTIERUNGDEZ-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>NOTIERUNGDEZ-Funktion</h1>
			<p>Die Funktion <b>NOTIERUNGDEZ</b> gehört zur Gruppe der Finanzmathematischen Funktionen. Sie wandelt eine Notierung, die durch eine Kombination aus ganzer Zahl und Dezimalbruch ausgedrückt wurde, in eine Dezimalzahl um.</p>
			<p>Die Formelsyntax der Funktion <b>NOTIERUNGDEZ</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>NOTIERUNGDEZ(Zahl;Teiler)</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Zahl</em></b> ist eine Zahl, die durch eine Kombination aus Ganzzahl und Dezimalbruch, getrennt durch ein Dezimaltrennzeichen ausgedrückt wurde.</p> 
				<p style="text-indent: 50px;"><b><em>Teiler</em></b> Bruchzahl ist eine Ganzzahl, die Sie als Nenner zum Zähler <em>Zahl</em> verwenden möchten.</p> 
            <p class="note"><b>Hinweis:</b> beispielsweise wird der Wert <em>Zahl</em>, ausgedrückt als <b>1,03</b>, als <b>1 + 3/n</b> interpretiert, wobei <b>n</b> der Wert von <em>Teiler</em> ist.</p>
            <p>Die Argumente werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>NOTIERUNGDEZ</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Gruppe <b>Finanzmathematische</b> Funktionen aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>NOTIERUNGDEZ</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="NOTIERUNGDEZ-Funktion" src="../images/dollarde.png" /></p>
		</div>
	</body>
</html>