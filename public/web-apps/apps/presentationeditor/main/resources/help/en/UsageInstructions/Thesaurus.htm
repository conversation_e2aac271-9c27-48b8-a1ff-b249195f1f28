﻿<!DOCTYPE html>
<html>
<head>
    <title>Replace a word by a synonym</title>
    <meta charset="utf-8" />
    <meta name="description" content="The description of Thesaurus plugin for ONLYOFFICE editors, which helps you find synonyms" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Replace a word by a synonym</h1>
        <p>
            If you are using the same word multiple times, or a word is just not quite the word you are looking for, ONLYOFFICE <a target="_blank" href="https://www.onlyoffice.com/en/presentation-editor.aspx" onclick="onhyperlinkclick(this)"><b>Presentation Editor</b></a> lets you look up synonyms.
            It will show you the antonyms too.
        </p>
        <ol>
            <li>Select the word in your presentation.</li>
            <li>Switch to the <b>Plugins</b> tab and choose <div class = "icon icon-thesaurus_icon"></div> <b>Thesaurus</b>.</li>
            <li>The synonyms and antonyms will show up in the left sidebar.</li>
            <li>Click a word to replace the word in your presentation.</li>
        </ol>
        <img class="gif" alt="Thesaurus plugin gif" src="../../images/thesaurus_plugin.gif" width="600" />
    </div>
</body>
</html>