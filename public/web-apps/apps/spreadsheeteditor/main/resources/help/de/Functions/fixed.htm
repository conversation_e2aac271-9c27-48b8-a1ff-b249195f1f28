﻿<!DOCTYPE html>
<html>
	<head>
		<title>FEST-Funktion</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>FEST-Funktion</h1>
			<p>Die Funktion <b>FEST</b> gehört zur Gruppe der Text- und Datenfunktionen. Sie formatiert eine Zahl als Text mit einer festen Anzahl von Nachkommastellen.</p>
			<p>Die Formelsyntax der Funktion <b>FEST</b> ist:</p> 
			<p style="text-indent: 150px;"><b><em>FEST(Zahl;[Dezimalstellen];[Keine_Punkte])</em></b></p> 
			<p><em>Dabei gilt:</em></p> 
				<p style="text-indent: 50px;"><b><em>Zahl</em></b> ist die zu rundende Zahl, die in Text umgewandelt werden soll.</p>
				<p style="text-indent: 50px;"><b><em>Dezimalstellen</em></b> gibt an wie viele Dezimalstellen angezeigt werden sollen. Fehlt das Argument, wird es als 2 (Zwei) angenommen.</p> 
				<p style="text-indent: 50px;"><b><em>Keine_Punkte</em></b> ist ein Wahrheitswert. Ist Keine_Punkte mit WAHR belegt, gibt die Funktion das Ergebnis ohne Kommas zurück. Ist Keine_Punkte FALSCH oder nicht angegeben, enthält der zurückgegebene Text die üblicherweise verwendeten Punkte.</p>
			<p>Die Werte werden manuell eingegeben oder sind in die Zelle eingeschlossen, auf die Sie Bezug nehmen.</p>
			<p>Anwendung der Funktion <b>FEST</b>:</p>
			<ol>
			<li>Wählen Sie die gewünschte Zelle für die Ergebnisanzeige aus.</li>
			<li>Klicken Sie auf das Symbol <b>Funktion einfügen</b> <div class = "icon icon-insertfunction"></div> auf der oberen Symbolleiste <br />oder klicken Sie mit der rechten Maustaste in die gewünschte Zelle und wählen Sie die Option <b>Funktion einfügen</b> aus dem Kontextenü aus <br />oder klicken Sie auf das Symbol <div class = "icon icon-function"></div> auf der Formelleiste.</li>
			<li>Wählen Sie die Funktionsgruppe <b>Text und Daten</b> aus der Liste aus.</li>
			<li>Klicken Sie auf die Funktion <b>FEST</b>.</li>
			<li>Geben Sie die erforderlichen Argumente ein und trennen Sie diese durch Kommas.</li>
			<li>Drücken Sie die <b>Eingabetaste</b>.</li>
			</ol>
			<p>Das Ergebnis wird in der gewählten Zelle angezeigt.</p>
			<p style="text-indent: 150px;"><img alt="FEST-Funktion" src="../images/fixed.png" /></p>
		</div>
	</body>
</html>